import { useCallback, useEffect, useMemo, useState } from 'react';

import { motion } from 'framer-motion';
import {
  Activity,
  Briefcase,
  Building2,
  Calendar,
  CheckCircle,
  CheckCircle2,
  Clock,
  Eye,
  FileText,
  XCircle,
} from 'lucide-react';
import { useRouter } from 'next/router';

import JobApplicationDetailsModal from '@/components/Applications/JobApplicationDetailsModal';
import PageHeader from '@/components/common/PageHeader';
import { TabNavigation } from '@/components/common/TabNavigation';
import GenericTable from '@/components/Layouts/GenericTable';
import WithdrawApplicationModal from '@/components/shared/WithdrawApplicationModal';
import AppLayout from '@/components/steps/layout/AppLayout';
import { Button } from '@/components/ui/button';
import apiClient from '@/lib/apiHelper';
import { useUser } from '@auth0/nextjs-auth0/client';

interface Job {
  id: string;
  companyName: string;
  jobType: string;
  department: string;
  location: string[];
  salaryRange: string | null;
  status: 'MATCHED' | 'ACTIVE' | 'INACTIVE';
  skills: string[];
  requirements: string[];
  typeOfHiring: 'EMPLOYMENT' | 'CONTRACT';
  typeOfJob: 'PERMANENT' | 'TEMPORARY';
  companyDescription: string;
  experience: 'Senior' | 'Junior' | 'Mid';
  benefits: string[];
  tldr: {
    summary: string;
    keyPoints: string[];
  } | null;
}

interface JobApplication {
  jobId: string;
  id: string;
  status: 'APPLIED' | 'PENDING' | 'WITHDRAWN';
  appliedAt: string;
  createdAt: string;
  coverLetter: string | null;
  job: Job;
}

interface ApplicationModalWrapperProps {
  application: JobApplication | null;
  onClose: () => void;
  isJobApplied: (jobId: string) => boolean;
  onWithdraw: (applicationId: string) => void;
}

const ApplicationModalWrapper = ({
  application,
  onClose,
  isJobApplied,
  onWithdraw,
}: ApplicationModalWrapperProps) => {
  return (
    <JobApplicationDetailsModal
      application={application}
      isOpen={!!application}
      onClose={onClose}
      onWithdraw={onWithdraw}
    />
  );
};

export default function ApplicationsPage() {
  const { user } = useUser();
  const router = useRouter();
  const [applications, setApplications] = useState<JobApplication[]>([]);
  const [activeApplications, setActiveApplications] = useState<JobApplication[]>([]);
  const [withdrawnApplications, setWithdrawnApplications] = useState<JobApplication[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm] = useState('');
  const [selectedApplication, setSelectedApplication] = useState<JobApplication | null>(null);
  const [activeView, setActiveView] = useState<string>('active');
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);

  const columns = [
    {
      key: 'job.companyName',
      label: 'Company',
      icon: Building2,
      render: (_value: any, row: JobApplication) => {
        const companyName = row.job.companyName || 'Unknown Company';
        const department = row.job.department || '';

        return (
          <div className="flex flex-col">
            <span className="font-medium text-white">{companyName}</span>
            {department && (
              <span className="text-xs text-gray-400 truncate max-w-[200px]" title={department}>
                {department}
              </span>
            )}
          </div>
        );
      },
    },
    {
      key: 'job.jobType',
      label: 'Position',
      icon: Briefcase,
      render: (_value: any, row: JobApplication) => {
        const position = row.job.jobType || 'Not Provided';
        const location = row.job.location?.join(', ') || '';

        return (
          <div className="flex flex-col">
            <span className="text-white truncate max-w-[250px]" title={position}>
              {position}
            </span>
            {location && (
              <span className="text-xs text-gray-400 truncate max-w-[200px]" title={location}>
                {location}
              </span>
            )}
          </div>
        );
      },
    },
    {
      key: 'status',
      label: 'Status',
      icon: Activity,
      render: (_value: string, row: JobApplication) => (
        <div className="flex items-center gap-2">
          {row.status === 'APPLIED' && <CheckCircle2 className="w-4 h-4 text-green-400" />}
          {row.status === 'WITHDRAWN' && <XCircle className="w-4 h-4 text-red-400" />}
          {row.status === 'PENDING' && <Clock className="w-4 h-4 text-yellow-400" />}
          <span
            className={`capitalize ${
              row.status === 'APPLIED'
                ? 'text-green-400'
                : row.status === 'PENDING'
                  ? 'text-yellow-400'
                  : row.status === 'WITHDRAWN'
                    ? 'text-red-400'
                    : 'text-gray-300'
            }`}
          >
            {row.status.toLowerCase()}
          </span>
        </div>
      ),
    },
    {
      key: 'appliedAt',
      label: 'Applied',
      icon: Calendar,
      render: (_value: string, row: JobApplication) => {
        try {
          if (!row?.createdAt) return <span className="text-gray-400">-</span>;
          const date = new Date(row.createdAt);
          if (isNaN(date.getTime())) return <span className="text-gray-400">-</span>;

          const days = Math.floor((Date.now() - date.getTime()) / (1000 * 60 * 60 * 24));
          if (days === 0) return <span className="text-gray-300">Today</span>;
          if (days === 1) return <span className="text-gray-300">Yesterday</span>;
          if (days < 7) return <span className="text-gray-300">{days} days ago</span>;
          if (days < 30)
            return <span className="text-gray-300">{Math.floor(days / 7)} weeks ago</span>;
          return <span className="text-gray-300">{date.toLocaleDateString()}</span>;
        } catch (error) {
          console.error('Error formatting date:', error);
          return <span className="text-gray-400">-</span>;
        }
      },
    },
  ];

  const fetchApplications = useCallback(async () => {
    if (user?.sub) {
      try {
        setIsLoading(true);
        const response = await apiClient.get(`/job-seekers/applications`);

        if (response && typeof response === 'object' && 'active' in response) {
          const { active, withdrawn } = response;
          setApplications([...active, ...withdrawn]);
          setActiveApplications(active);
          setWithdrawnApplications(withdrawn);
        } else {
          const apps = Array.isArray(response) ? response : [response];
          setApplications(apps);

          setActiveApplications(apps.filter(app => app.status !== 'WITHDRAWN'));
          setWithdrawnApplications(apps.filter(app => app.status === 'WITHDRAWN'));
        }
      } catch (error) {
        console.error('Error fetching applications:', error);
      } finally {
        setIsLoading(false);
      }
    }
  }, [user?.sub]);

  useEffect(() => {
    fetchApplications();
  }, [fetchApplications]);

  useEffect(() => {
    setCurrentPage(1);
  }, [activeView]);

  // Handle URL parameter for auto-opening application details
  useEffect(() => {
    if (router.query.id && applications.length > 0) {
      const id = router.query.id as string;
      // Try to find by job ID first, then by application ID
      const application = applications.find(app => app.job.id === id || app.id === id);
      if (application) {
        setSelectedApplication(application);
      }
    }
  }, [router.query.id, applications]);

  const handleWithdrawSuccess = () => {
    // Refresh the applications list from the server to get the latest data
    fetchApplications();

    setShowWithdrawModal(false);
    setSelectedApplication(null);
  };

  const handleWithdrawError = (error: string) => {
    console.error('Error withdrawing application:', error);
    // You could show a toast notification here
  };

  const handleWithdraw = async (applicationId: string) => {
    const application = applications.find(app => app.id === applicationId);
    if (application) {
      setSelectedApplication(application);
      setShowWithdrawModal(true);
    }
  };

  const displayedApplications = useMemo(() => {
    let apps: JobApplication[] = [];
    switch (activeView) {
      case 'all':
        apps = applications;
        break;
      case 'active':
        apps = activeApplications;
        break;
      case 'withdrawn':
        apps = withdrawnApplications;
        break;
      default:
        apps = activeApplications;
    }

    if (searchTerm) {
      const searchString = searchTerm.toLowerCase();
      return apps.filter(
        app =>
          app?.job?.jobType?.toLowerCase().includes(searchString) ||
          app?.job?.companyName?.toLowerCase().includes(searchString) ||
          app?.job?.location?.some((loc: string) => loc?.toLowerCase().includes(searchString)) ||
          app?.job?.department?.toLowerCase().includes(searchString)
      );
    }

    return apps;
  }, [activeView, applications, activeApplications, withdrawnApplications, searchTerm]);

  const totalItems = displayedApplications.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const totalCount = applications.length;
  const activeCount = activeApplications.length;
  const withdrawnCount = withdrawnApplications.length;

  const isJobApplied = (jobId: string) => {
    return (applications || []).some(app => app?.job?.id === jobId && app?.status !== 'WITHDRAWN');
  };

  return (
    <AppLayout isLoading={isLoading}>
      <div className="h-full flex flex-col relative">
        {/* Full-width Header */}
        <PageHeader
          variant="fullwidth"
          title="Job Applications"
          description="Track and manage all your job applications in one place"
          icon={FileText}
          imageSrc="/images/insights/performance_analytics_turquoise.png"
        />

        {/* Spacer for fixed header */}
        <div className="h-[200px] flex-shrink-0"></div>

        {/* Tabs Navigation - Full width */}
        <div className="sticky top-0 z-40 flex-none border-b border-gray-300/10 bg-background/95 backdrop-blur">
          <div className="flex items-center justify-between h-12 sm:h-14 px-4 sm:px-6">
            <TabNavigation
              tabs={[
                {
                  id: 'active',
                  label: `Active Applications (${activeCount})`,
                  icon: <Clock className="text-blue-300" />,
                },
                {
                  id: 'withdrawn',
                  label: `Withdrawn (${withdrawnCount})`,
                  icon: <XCircle className="text-red-300" />,
                },
                {
                  id: 'all',
                  label: `All (${totalCount})`,
                  icon: <CheckCircle className="text-green-300" />,
                },
              ]}
              activeTab={activeView}
              onTabChange={setActiveView}
              updateUrl={false}
              variant="gradient"
              gradientColors={{
                from: 'from-purple-600',
                to: 'to-pink-600',
              }}
              className="text-white"
            />
            <div className="text-sm text-gray-400">
              Total: <span className="text-white font-medium">{totalCount}</span>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 overflow-auto">
          <div className="px-4 sm:px-6 py-4 sm:py-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="w-full">
                {displayedApplications.length > 0 || isLoading ? (
                  <GenericTable
                    data={displayedApplications}
                    columns={columns}
                    itemsPerPage={itemsPerPage}
                    onRowClick={(application: JobApplication) => {
                      setSelectedApplication(application);
                      // Update URL with job ID
                      router.push(
                        {
                          pathname: router.pathname,
                          query: { ...router.query, id: application.job.id },
                        },
                        undefined,
                        { shallow: true }
                      );
                    }}
                    hideDelete
                    hideDetailsUrl
                    showPagination={true}
                    isLoading={isLoading}
                    paginationData={{
                      currentPage,
                      totalPages,
                      totalItems,
                      itemsPerPage,
                    }}
                    onPageChange={setCurrentPage}
                    customActions={(application: JobApplication) => (
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e: React.MouseEvent) => {
                            e.stopPropagation();
                            setSelectedApplication(application);
                            // Update URL with job ID
                            router.push(
                              {
                                pathname: router.pathname,
                                query: { ...router.query, id: application.job.id },
                              },
                              undefined,
                              { shallow: true }
                            );
                          }}
                          className="flex items-center gap-1.5 text-gray-400 hover:text-white hover:bg-white/10"
                        >
                          <Eye className="w-4 h-4" />
                          View
                        </Button>
                      </div>
                    )}
                  />
                ) : (
                  <div className="bg-black/10 backdrop-blur-md rounded-xl border border-white/10 p-8 text-center">
                    <p className="text-gray-400">
                      {activeView === 'active' && 'No active applications found'}
                      {activeView === 'withdrawn' && 'No withdrawn applications found'}
                      {activeView === 'all' && 'No applications found'}
                    </p>
                    {activeView !== 'active' && activeCount > 0 && (
                      <Button
                        variant="outline"
                        className="mt-4 text-white/90"
                        onClick={() => setActiveView('active')}
                      >
                        View Active Applications
                      </Button>
                    )}
                  </div>
                )}
              </div>
            </motion.div>

            {selectedApplication && (
              <ApplicationModalWrapper
                application={selectedApplication}
                onClose={() => {
                  setSelectedApplication(null);
                  // Remove ID from URL when closing modal
                  const { id, ...restQuery } = router.query;
                  router.push(
                    {
                      pathname: router.pathname,
                      query: restQuery,
                    },
                    undefined,
                    { shallow: true }
                  );
                }}
                isJobApplied={isJobApplied}
                onWithdraw={handleWithdraw}
              />
            )}

            {selectedApplication && (
              <WithdrawApplicationModal
                isOpen={showWithdrawModal}
                onClose={() => setShowWithdrawModal(false)}
                onSuccess={handleWithdrawSuccess}
                onError={handleWithdrawError}
                jobId={selectedApplication.jobId}
                jobTitle={selectedApplication.job.jobType}
                companyName={selectedApplication.job.companyName}
              />
            )}
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
