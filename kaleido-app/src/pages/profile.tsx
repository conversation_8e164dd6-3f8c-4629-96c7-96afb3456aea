import { useEffect, useState } from 'react';

import { Award, Briefcase, Download, GraduationCap, User } from 'lucide-react';
import { useRouter } from 'next/router';

import PageHeader from '@/components/common/PageHeader';
import { TabNavigation } from '@/components/common/TabNavigation';
import { ProfileDataCollectionModal } from '@/components/JobSeeker/components/ProfileDataCollectionModal';
import { ResumeGenerator } from '@/components/shared/ResumeGenerator';
import AppLayout from '@/components/steps/layout/AppLayout';
import { useProfileCompletion } from '@/hooks/useProfileCompletion';
import apiClient from '@/lib/apiHelper';
import { GraduateProfile } from '@/types/graduate';
import { JobSeekerProfile } from '@/types/jobSeeker';
import { UserRole } from '@/types/roles';
import { useUser } from '@auth0/nextjs-auth0/client';

// Import the PersonalInfoTab component from its own file (read-only version)
import { PersonalInfoTab } from '@/components/JobSeeker/PersonalInfoTabReadOnly';

// Content Components
function PersonalInfoContent({
  profile,
  userRole,
}: {
  profile: JobSeekerProfile | GraduateProfile;
  userRole: UserRole | null;
}) {
  if (userRole === UserRole.GRADUATE) {
    // For graduates, show their personal info tab content
    // This would need to be extracted from GraduateProfileTabs
    return <div className="text-white">Graduate personal info content to be implemented</div>;
  }

  // Use the standalone PersonalInfoTab component that handles its own edit state
  // Ensure we pass the complete profile data
  return <PersonalInfoTab profile={profile as JobSeekerProfile} />;
}

function ResumeGeneratorContent({
  profile,
  userRole,
}: {
  profile: JobSeekerProfile | GraduateProfile;
  userRole: UserRole | null;
}) {
  const role = userRole === UserRole.GRADUATE ? UserRole.GRADUATE : UserRole.JOB_SEEKER;
  return <ResumeGenerator profile={profile} userRole={role} />;
}

// Placeholder components for Graduate tabs
function AcademicInfoContent({ profile }: { profile: GraduateProfile }) {
  return <div className="text-white">Academic info content to be implemented</div>;
}

function InternshipsContent({ profile }: { profile: GraduateProfile }) {
  return <div className="text-white">Internships content to be implemented</div>;
}

function AdditionalInfoContent({ profile }: { profile: GraduateProfile }) {
  return <div className="text-white">Additional info content to be implemented</div>;
}

export default function ProfilePage() {
  const { user } = useUser();

  // Profile completion modal handling
  const { isModalOpen, missingFields, closeModal, handleComplete } = useProfileCompletion();
  const router = useRouter();
  const [profile, setProfile] = useState<JobSeekerProfile | GraduateProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [userRole, setUserRole] = useState<UserRole | null>(null);

  // Tab management
  const [activeTab, setActiveTab] = useState(() => {
    // Initialize from URL if available, otherwise default to first tab
    return (
      (typeof window !== 'undefined' && new URLSearchParams(window.location.search).get('tab')) ||
      'personal-info'
    );
  });

  // Tab configuration based on user role
  const tabs = [
    {
      id: 'personal-info',
      label: 'Personal Info',
      icon: User,
    },
    {
      id: 'resume-generator',
      label: 'Resume Generator',
      icon: Download,
    },
    // Additional tabs for graduates
    ...(userRole === UserRole.GRADUATE
      ? [
          {
            id: 'academic-info',
            label: 'Academic Info',
            icon: GraduationCap,
          },
          {
            id: 'internships',
            label: 'Internships',
            icon: Briefcase,
          },
          {
            id: 'additional-info',
            label: 'Additional Info',
            icon: Award,
          },
        ]
      : []),
  ];

  // Sync URL changes with active tab
  useEffect(() => {
    const tabFromUrl = router.query.tab as string;
    if (tabFromUrl && tabs.some(tab => tab.id === tabFromUrl)) {
      setActiveTab(tabFromUrl);
    }
  }, [router.query.tab, tabs]);

  useEffect(() => {
    const fetchUserRole = async () => {
      if (user?.sub) {
        try {
          const roleData = await apiClient.get<{ role: UserRole }>(`/roles/${user.sub}`);
          setUserRole(roleData.role);

          // Redirect if not authorized
          if (![UserRole.JOB_SEEKER, UserRole.GRADUATE].includes(roleData.role)) {
            router.push('/dashboard');
          }
        } catch (error) {
          console.error('Error fetching user role:', error);
        }
      }
    };

    fetchUserRole();
  }, [user, router]);

  const fetchProfile = async () => {
    if (user?.sub && userRole) {
      try {
        const endpoint = userRole === UserRole.GRADUATE ? `/graduates/profile` : `/job-seekers`;

        const data = await apiClient.get<JobSeekerProfile | GraduateProfile>(`${endpoint}`);
        setProfile(data);
      } catch (error) {
        console.error('Error fetching profile:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    if (userRole) {
      fetchProfile();
    }
  }, [user, userRole]);

  // Function to render tab content based on active tab
  const renderTabContent = () => {
    if (!profile) return null;

    // Directly render the content based on active tab without using the old tab components
    switch (activeTab) {
      case 'personal-info':
        return <PersonalInfoContent profile={profile} userRole={userRole} />;
      case 'resume-generator':
        return <ResumeGeneratorContent profile={profile} userRole={userRole} />;
      case 'academic-info':
        return userRole === UserRole.GRADUATE ? (
          <AcademicInfoContent profile={profile as GraduateProfile} />
        ) : null;
      case 'internships':
        return userRole === UserRole.GRADUATE ? (
          <InternshipsContent profile={profile as GraduateProfile} />
        ) : null;
      case 'additional-info':
        return userRole === UserRole.GRADUATE ? (
          <AdditionalInfoContent profile={profile as GraduateProfile} />
        ) : null;
      default:
        return <PersonalInfoContent profile={profile} userRole={userRole} />;
    }
  };

  return (
    <AppLayout isLoading={isLoading}>
      <div className="h-full flex flex-col relative">
        {/* Full-width Header */}
        <PageHeader
          imageSrc="/images/insights/performance_analytics.png"
          variant="fullwidth"
          title={userRole === UserRole.GRADUATE ? 'Graduate Profile' : 'Professional Profile'}
          description={
            userRole === UserRole.GRADUATE
              ? 'Manage your graduate profile, skills, and preferences to match with the right opportunities'
              : 'Manage your professional profile, experience, and preferences to match with the right opportunities'
          }
          icon={userRole === UserRole.GRADUATE ? GraduationCap : User}
          socialLinks={
            profile
              ? [
                  ...(profile.linkedinUrl
                    ? [{ type: 'linkedin' as const, url: profile.linkedinUrl }]
                    : []),
                  ...(profile.githubUrl
                    ? [{ type: 'github' as const, url: profile.githubUrl }]
                    : []),
                  ...(profile.portfolioUrl
                    ? [{ type: 'portfolio' as const, url: profile.portfolioUrl }]
                    : []),
                ]
              : []
          }
        />

        {/* Spacer for fixed header */}
        <div className="h-[200px] flex-shrink-0"></div>

        {/* Tabs Navigation */}
        <div className="sticky top-0 z-40 flex-none border-b border-gray-300/10 bg-background/95 backdrop-blur">
          <div className="flex items-center h-12 sm:h-14 px-4 sm:px-6">
            {!isLoading && (
              <TabNavigation
                tabs={tabs}
                activeTab={activeTab}
                onTabChange={setActiveTab}
                variant="gradient"
                gradientColors={{
                  from: 'from-purple-600',
                  to: 'to-pink-600',
                }}
                updateUrl={true}
                queryParamName="tab"
                showBorder={true}
                showSeparators={true}
                mobileCollapse={true}
                className="w-full"
              />
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 overflow-auto">
          <div className="px-4 sm:px-6 py-4 sm:py-8">{profile && renderTabContent()}</div>
        </div>
      </div>

      {/* Profile data collection modal for missing LinkedIn data */}
      <ProfileDataCollectionModal
        isOpen={isModalOpen}
        onClose={closeModal}
        onComplete={handleComplete}
        missingFields={missingFields}
      />
    </AppLayout>
  );
}
