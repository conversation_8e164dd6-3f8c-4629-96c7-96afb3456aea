import { useCallback, useEffect, useMemo, useState } from 'react';

import { motion } from 'framer-motion';
import { Activity, CheckCircle, Clock, XCircle } from 'lucide-react';

import { TabNavigation } from '@/components/common/TabNavigation';
import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import GenericTable from '@/components/Layouts/GenericTable';
import WithdrawApplicationModal from '@/components/shared/WithdrawApplicationModal';
import { WithdrawApplicationButton } from '@/components/shared/WithdrawApplicationButton';
import { Button } from '@/components/ui/button';
import { useApplicationStore } from '@/stores/applicationStore';
import { CandidateStatus } from '@/types/candidate.types';
import { useUser } from '@auth0/nextjs-auth0/client';

// Using types from applicationStore

export default function Applications() {
  const { user } = useUser();
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [activeView, setActiveView] = useState<string>('active');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);

  // Use the application store
  const {
    applications,
    isLoading,
    activeApplications,
    withdrawnApplications,
    totalCount,
    activeCount,
    withdrawnCount,
    fetchApplications,
    selectedApplication,
    selectedApplicationId,
    setSelectedApplication,
  } = useApplicationStore();

  // Fetch applications when the component mounts
  useEffect(() => {
    if (user?.sub) {
      fetchApplications();
    }
  }, [user?.sub, fetchApplications]);

  const handleWithdrawClick = useCallback(
    (applicationId: string) => {
      setSelectedApplication(applicationId);
      setShowWithdrawModal(true);
    },
    [setSelectedApplication]
  );

  const handleWithdrawSuccess = () => {
    // Refresh the applications list
    fetchApplications();
    setShowWithdrawModal(false);
  };

  const handleWithdrawError = (error: string) => {
    console.error('Error withdrawing application:', error);
    // You could show a toast notification here
  };

  // Get the applications based on the active view
  const displayedApplications = useMemo(() => {
    switch (activeView) {
      case 'all':
        return applications;
      case 'active':
        return activeApplications;
      case 'withdrawn':
        return withdrawnApplications;
      default:
        return activeApplications;
    }
  }, [activeView, applications, activeApplications, withdrawnApplications]);

  // Calculate pagination data
  const totalItems = displayedApplications.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Reset to page 1 when changing tabs
  useEffect(() => {
    setCurrentPage(1);
  }, [activeView]);

  // Define columns for the GenericTable
  const columns = useMemo(
    () => [
      {
        key: 'job.jobType',
        label: 'Position',
        render: (value: string, application: any) => (
          <div>
            <div className="font-medium text-white">{value}</div>
            <div className="text-xs text-gray-400">{application.job.companyName}</div>
          </div>
        ),
      },
      {
        key: 'job.department',
        label: 'Department',
      },
      {
        key: 'job.location',
        label: 'Location',
        render: (value: string[]) => <div>{Array.isArray(value) ? value.join(', ') : value}</div>,
      },
      {
        key: 'status',
        label: 'Status',
        render: (value: CandidateStatus) => {
          const statusColors: Record<string, string> = {
            [CandidateStatus.NEW]: 'bg-gray-700 text-gray-200',
            [CandidateStatus.APPLIED]: 'bg-blue-700 text-blue-200',
            [CandidateStatus.MATCHED]: 'bg-yellow-700 text-yellow-200',
            [CandidateStatus.SHORTLISTED]: 'bg-green-700 text-green-200',
            [CandidateStatus.INTERVIEWING]: 'bg-purple-700 text-purple-200',
            [CandidateStatus.OFFER_EXTENDED]: 'bg-purple-700 text-pink-200',
            [CandidateStatus.HIRED]: 'bg-emerald-700 text-emerald-200',
            [CandidateStatus.WITHDRAWN]: 'bg-red-700 text-red-200',
          };

          const statusClass = statusColors[value] || 'bg-gray-700 text-gray-200';
          const formattedStatus = value
            .replace(/_/g, ' ')
            .toLowerCase()
            .replace(/\b\w/g, l => l.toUpperCase());

          return (
            <span className={`px-2 py-1 rounded-full text-xs ${statusClass}`}>
              {formattedStatus}
            </span>
          );
        },
      },
      {
        key: 'createdAt',
        label: 'Applied On',
        render: (value: string) => <div>{new Date(value).toLocaleDateString()}</div>,
      },
    ],
    []
  );

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <div className="flex flex-col space-y-4 mb-6">
          <div className="flex justify-between items-center">
            <div className="flex items-center h-12 sm:h-14">
              <TabNavigation
                tabs={[
                  {
                    id: 'active',
                    label: `Active Applications (${activeCount})`,
                    icon: <Clock className="text-blue-300" />,
                  },
                  {
                    id: 'withdrawn',
                    label: `Withdrawn (${withdrawnCount})`,
                    icon: <XCircle className="text-red-300" />,
                  },
                  {
                    id: 'all',
                    label: `All (${totalCount})`,
                    icon: <CheckCircle className="text-green-300" />,
                  },
                ]}
                activeTab={activeView}
                onTabChange={setActiveView}
                updateUrl={false}
                variant="gradient"
                gradientColors={{
                  from: 'from-purple-600',
                  to: 'to-pink-600',
                }}
              />
            </div>
            <div className="text-sm text-gray-400">
              Total: <span className="text-white font-medium">{totalCount}</span>
            </div>
          </div>
        </div>

        {isLoading ? (
          <ColorfulSmokeyOrbLoader text="Loading applications..." useModalBg={false} />
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            {displayedApplications.length > 0 ? (
              <GenericTable
                data={displayedApplications}
                columns={columns}
                itemsPerPage={itemsPerPage}
                onRowClick={application => {
                  // You could implement a detail view here
                }}
                customActions={application => (
                  <div className="flex justify-end space-x-2">
                    {application.status !== CandidateStatus.WITHDRAWN && (
                      <WithdrawApplicationButton
                        variant="compact"
                        onClick={() => handleWithdrawClick(application.id)}
                      />
                    )}
                  </div>
                )}
                hideDelete
                hideDetailsUrl
                showPagination={true}
                paginationData={{
                  currentPage,
                  totalPages,
                  totalItems,
                  itemsPerPage,
                }}
                onPageChange={setCurrentPage}
              />
            ) : (
              <div className="bg-black/10 backdrop-blur-md rounded-xl border border-white/10 p-8 text-center">
                <p className="text-gray-400">
                  {activeView === 'active' && 'No active applications found'}
                  {activeView === 'withdrawn' && 'No withdrawn applications found'}
                  {activeView === 'all' && 'No applications found'}
                </p>
                {activeView !== 'active' && activeCount > 0 && (
                  <Button
                    variant="outline"
                    className="mt-4 text-white/90"
                    onClick={() => setActiveView('active')}
                  >
                    View Active Applications
                  </Button>
                )}
              </div>
            )}
          </motion.div>
        )}
      </div>

      {/* Withdraw Application Modal */}
      {selectedApplication && (
        <WithdrawApplicationModal
          isOpen={showWithdrawModal}
          onClose={() => setShowWithdrawModal(false)}
          onSuccess={handleWithdrawSuccess}
          onError={handleWithdrawError}
          jobId={selectedApplication.jobId}
          jobTitle={selectedApplication.job.jobType}
          companyName={selectedApplication.job.companyName}
        />
      )}
    </div>
  );
}
