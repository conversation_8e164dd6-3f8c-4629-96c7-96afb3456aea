import { Activity, Banknote, Clock, FileText, Globe, Medal, X, XCircle } from 'lucide-react';

import TabComponent from '@/components/common/TabComponent';
import { Button } from '@/components/ui/button';
import { WithdrawApplicationButton } from '@/components/shared/WithdrawApplicationButton';

interface ModalHeaderProps {
  jobId: string;
  jobType: string;
  companyName: string;
  department: string;
  location: string[];
  salaryRange: string;
  experience: string;
  onClose: () => void;
  onApply?: () => void;
  applicationStatus?: string;
  isApplied?: boolean;
  hideApplyButton?: boolean;
  activeTab?: string;
  onTabChange?: (tab: string) => void;
  onWithdraw?: () => void;
}

export const ModalHeader = ({
  jobType,
  companyName,
  department,
  location,
  salaryRange,
  experience,
  onClose,
  activeTab = 'description',
  onTabChange,
  isApplied,
  onWithdraw,
}: ModalHeaderProps) => {
  const formatText = (text: string) => {
    if (!text) return '';
    return text
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  const formatLocation = (locations: string[]) => {
    if (!locations.length) return 'Remote';
    if (locations.length === 1) return formatText(locations[0]);
    return `${formatText(locations[0])} +${locations.length - 1}`;
  };

  const quickViewItems = [
    {
      icon: <Banknote className="w-4 h-4 text-emerald-400" />,
      label: 'Salary',
      value: salaryRange,
    },
    {
      icon: <Clock className="w-4 h-4 text-blue-400" />,
      label: 'Type',
      value: formatText(jobType),
    },
    {
      icon: <Medal className="w-4 h-4 text-amber-400" />,
      label: 'Level',
      value: formatText(experience),
    },
    {
      icon: <Globe className="w-4 h-4 text-violet-400" />,
      label: 'Location',
      value: formatLocation(location),
    },
  ];

  return (
    <div className="border-b border-white/10 p-6 relative">
      <div className="absolute top-4 right-4 flex items-center gap-2">
        <button
          type="button"
          onClick={onClose}
          className="text-white/40 hover:text-white transition-colors"
          aria-label="Close modal"
        >
          <X className="h-5 w-5" />
        </button>
      </div>
      <div className="absolute top-40 right-4 flex items-center gap-2">
        {isApplied && onWithdraw && (
          <WithdrawApplicationButton onClick={onWithdraw} label="Withdraw my application" />
        )}
      </div>
      <div className="flex flex-col gap-4 pr-8">
        <div className="flex items-start gap-6">
          <div className="flex-1 space-y-3">
            <div className="flex items-center gap-3">
              <h2 className="text-xl font-semibold text-white">{formatText(jobType)}</h2>
            </div>
            <p className="text-purple-400 mt-1.5 text-sm">
              {formatText(companyName)} •{' '}
              <span className="text-pink-400">{formatText(department)}</span>
            </p>
          </div>
          <div className="w-[500px] bg-purple-500/5 rounded-lg p-3 backdrop-blur-sm border border-[#45943D]/20">
            <div className="grid grid-cols-2 gap-3">
              {quickViewItems.map((item, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="p-1 rounded-lg bg-white/5">{item.icon}</div>
                  <div className="min-w-0">
                    <p className="text-xs text-white/50">{item.label}</p>
                    <p className="text-sm font-medium text-white truncate max-w-[200px]">
                      {item.value}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        {/* Modern Tab Component */}
        {onTabChange && (
          <div className="mt-2 w-full sm:w-[400px]">
            <TabComponent
              tabs={[
                {
                  id: 'description',
                  label: 'Job Description',
                  icon: <FileText className="text-purple-200" />,
                },
                {
                  id: 'status',
                  label: 'Application Status',
                  icon: <Activity className="text-purple-200" />,
                },
              ]}
              activeTab={activeTab}
              onTabChange={onTabChange}
              containerBackgroundColor="bg-purple-900/20"
              activeBackgroundColor="bg-purple-800/50"
              updateUrl={false}
              iconSize="w-5 h-5"
              responsiveIconOnly={true}
              tabPadding="px-3 py-2.5"
            />
          </div>
        )}
      </div>
    </div>
  );
};
