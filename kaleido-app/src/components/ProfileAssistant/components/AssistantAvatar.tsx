'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { X } from 'lucide-react';
import { AssistantAvatarProps } from '../types';

export const AssistantAvatar: React.FC<AssistantAvatarProps> = ({
  mood,
  hasNewMessage,
  isMinimized,
  onToggle,
  onMinimize,
}) => {
  return (
    <div className="relative">
      {/* New message indicator */}
      {hasNewMessage && !isMinimized && (
        <motion.div
          className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{
            type: 'spring',
            stiffness: 500,
            damping: 15,
          }}
        >
          <div className="absolute inset-0 bg-red-500 rounded-full animate-ping" />
        </motion.div>
      )}

      {/* Avatar with simple background */}
      <motion.div
        className="bg-white rounded-full p-3 shadow-md cursor-pointer relative border-2 border-gray-200"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={onToggle}
        data-testid="assistant-avatar"
      >
        {/* Avatar emoji */}
        <div className="w-10 h-10 flex items-center justify-center text-2xl">
          <motion.span
            key={mood.icon}
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{
              type: 'spring',
              stiffness: 200,
              damping: 15,
            }}
          >
            {mood.icon}
          </motion.span>
        </div>
      </motion.div>

      {/* Minimize button */}
      {!isMinimized && (
        <motion.button
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          exit={{ scale: 0 }}
          onClick={e => {
            e.stopPropagation();
            onMinimize();
          }}
          className="absolute -top-2 -right-2 bg-white rounded-full p-1 shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
          data-testid="assistant-minimize"
        >
          <X className="w-3 h-3 text-gray-500" />
        </motion.button>
      )}

      {/* Mood message tooltip */}
      {isMinimized && (
        <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap opacity-0 hover:opacity-100 transition-opacity pointer-events-none">
          {mood.message}
        </div>
      )}
    </div>
  );
};
