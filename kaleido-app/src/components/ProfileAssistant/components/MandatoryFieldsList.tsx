'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>Circle2, AlertCircle, Spark<PERSON> } from 'lucide-react';
import { MandatoryFieldsListProps } from '../types';
import { fieldFriendlyNames } from '../constants';

export const MandatoryFieldsList: React.FC<MandatoryFieldsListProps> = ({
  currentStep,
  mandatoryFields,
  completedFields,
  missingFields,
}) => {
  const getStepTitle = () => {
    switch (currentStep) {
      case 'essentials':
        return 'Required Information';
      case 'professional':
        return 'Required Skills';
      case 'preferences':
        return 'Required Preferences';
      default:
        return 'Required Fields';
    }
  };

  const allFieldsComplete = missingFields.length === 0 && mandatoryFields.length > 0;

  return (
    <div className="bg-gray-50 rounded-lg p-2.5">
      <div className="space-y-1.5" data-testid="mandatory-fields-list">
        {/* Step title */}
        <div className="text-xs font-semibold text-gray-700 mb-1.5">{getStepTitle()}</div>

        {/* Fields list */}
        <AnimatePresence mode="popLayout">
          {mandatoryFields.map((field, index) => {
            const isComplete = completedFields.includes(field);
            const fieldName = fieldFriendlyNames[field] || field;

            return (
              <motion.div
                key={field}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ delay: index * 0.05 }}
                className="flex items-center gap-2 group"
                data-field-name={field}
              >
                {/* Status indicator */}
                <motion.div
                  className={`
                    w-4 h-4 rounded-full flex items-center justify-center transition-all
                    ${isComplete ? 'bg-green-500' : 'bg-gray-300'}
                  `}
                  initial={false}
                  animate={{
                    scale: isComplete ? [1, 1.2, 1] : 1,
                    rotate: isComplete ? [0, 360] : 0,
                  }}
                  transition={{ duration: 0.3 }}
                >
                  {isComplete ? (
                    <CheckCircle2 className="w-2.5 h-2.5 text-white" />
                  ) : (
                    <div className="w-1.5 h-1.5 bg-gray-500 rounded-full" />
                  )}
                </motion.div>

                {/* Field name */}
                <span
                  className={`
                  text-xs transition-all
                  ${isComplete ? 'text-gray-500 line-through' : 'text-gray-700'}
                `}
                >
                  {fieldName}
                </span>

                {/* Optional helper icon for incomplete fields */}
                {!isComplete && (
                  <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="ml-auto">
                    <AlertCircle className="w-3 h-3 text-amber-500 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </motion.div>
                )}
              </motion.div>
            );
          })}
        </AnimatePresence>

        {/* Simple status for partial completion */}
        {!allFieldsComplete && completedFields.length > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="mt-1.5 text-xs text-gray-600"
          >
            {missingFields.length === 1
              ? `1 field remaining`
              : `${missingFields.length} fields remaining`}
          </motion.div>
        )}
      </div>
    </div>
  );
};
