'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ProfileAssistantProps, ProgressData } from './types';
import {
  assistantMoods,
  assistantMessages,
  mandatoryFieldsByStep,
  floatingAnimation,
} from './constants';
import { AssistantAvatar, MandatoryFieldsList, ProgressRing, QuickActions } from './components';
import { calculateProgress } from './utils';

export const ProfileAssistant: React.FC<ProfileAssistantProps> = ({
  currentStep,
  formData,
  onSkipOptional,
  onGetHelp,
  className = '',
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [isMinimized, setIsMinimized] = useState(false);
  const [hasNewMessage, setHasNewMessage] = useState(false);

  // Calculate progress
  const progressData = useMemo(
    () => calculateProgress(currentStep, formData, mandatoryFieldsByStep),
    [currentStep, formData]
  );

  const { mandatoryProgress, missingFields } = progressData;

  // Determine mood based on progress
  const currentMood = useMemo(() => {
    if (isMinimized) return 'sleeping';
    if (missingFields.length > 0 && mandatoryProgress < 50) return 'thinking';
    if (mandatoryProgress === 100) return 'celebrating';
    if (mandatoryProgress >= 75) return 'proud';
    if (mandatoryProgress >= 50) return 'excited';
    if (mandatoryProgress >= 25) return 'happy';
    return 'encouraging';
  }, [mandatoryProgress, missingFields, isMinimized]);

  const mood = assistantMoods[currentMood];

  // Get contextual message
  const getContextualMessage = () => {
    const messages = assistantMessages[currentStep];
    if (!messages) return "Keep going! You're doing great!";

    if (mandatoryProgress === 100) {
      return messages.completion || 'Perfect! Ready for the next step! ✨';
    }

    if (missingFields.length > 0) {
      return `Just ${missingFields.length} more field${missingFields.length > 1 ? 's' : ''} to complete this step!`;
    }

    return messages.greeting || messages.mandatory;
  };

  // Show new message animation when step changes
  useEffect(() => {
    setHasNewMessage(true);
    const timer = setTimeout(() => setHasNewMessage(false), 3000);
    return () => clearTimeout(timer);
  }, [currentStep]);

  // Auto-expand when step changes
  useEffect(() => {
    if (!isMinimized) {
      setIsExpanded(true);
    }
  }, [currentStep, isMinimized]);

  const handleToggle = () => {
    if (isMinimized) {
      setIsMinimized(false);
      setIsExpanded(true);
    } else {
      setIsExpanded(!isExpanded);
    }
  };

  const handleMinimize = () => {
    setIsMinimized(true);
    setIsExpanded(false);
  };

  // Check if current step has optional content
  const hasOptionalContent =
    currentStep === 'additional' ||
    currentStep === 'verification' ||
    (currentStep === 'import' && formData);

  // Check if step can be skipped
  const canSkipStep =
    currentStep === 'import' ||
    currentStep === 'additional' ||
    currentStep === 'verification' ||
    (missingFields.length === 0 && hasOptionalContent);

  return (
    <motion.div
      className={`fixed bottom-6 right-6 z-50 ${isMinimized ? 'w-16' : 'w-72'} transition-all duration-300 ease-in-out ${className}`}
      initial="initial"
      animate="animate"
      variants={floatingAnimation}
      data-testid="profile-assistant"
    >
      {/* Assistant Avatar */}
      <AssistantAvatar
        mood={mood}
        hasNewMessage={hasNewMessage}
        isMinimized={isMinimized}
        onToggle={handleToggle}
        onMinimize={handleMinimize}
      />

      {/* Expandable Content Card */}
      <AnimatePresence>
        {!isMinimized && isExpanded && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.95 }}
            transition={{
              type: 'spring',
              stiffness: 300,
              damping: 25,
            }}
            className="mt-3 overflow-hidden"
          >
            <div className="relative">
              {/* Step Header */}
              <div className="bg-white rounded-t-xl px-4 py-3 border border-gray-200 border-b-0">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-semibold text-gray-900">Step Progress</h4>
                    <p className="text-xs text-gray-600 mt-0.5">{getContextualMessage()}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-gray-900">
                      {progressData.overallProgress}%
                    </div>
                    <div className="text-xs text-gray-500">Complete</div>
                  </div>
                </div>
              </div>

              {/* Progress Content */}
              <div className="bg-white rounded-b-xl px-4 py-3 border border-gray-200 border-t-0 space-y-3">
                {/* Simple Progress Bars */}
                <div className="space-y-2">
                  <div>
                    <div className="flex items-center justify-between text-xs mb-1">
                      <span className="text-gray-600 font-medium">Required Fields</span>
                      <span
                        className={`font-semibold ${progressData.mandatoryProgress === 100 ? 'text-green-600' : 'text-gray-900'}`}
                      >
                        {progressData.mandatoryProgress}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div
                        className={`h-1.5 rounded-full transition-all duration-300 ${
                          progressData.mandatoryProgress === 100 ? 'bg-green-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${progressData.mandatoryProgress}%` }}
                      />
                    </div>
                  </div>

                  {progressData.optionalProgress > 0 && (
                    <div>
                      <div className="flex items-center justify-between text-xs mb-1">
                        <span className="text-gray-600 font-medium">Optional Fields</span>
                        <span className="text-gray-900 font-semibold">
                          {progressData.optionalProgress}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1.5">
                        <div
                          className="h-1.5 bg-gray-400 rounded-full transition-all duration-300"
                          style={{ width: `${progressData.optionalProgress}%` }}
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* Mandatory Fields List */}
                {mandatoryFieldsByStep[currentStep] && missingFields.length > 0 && (
                  <MandatoryFieldsList
                    currentStep={currentStep}
                    mandatoryFields={mandatoryFieldsByStep[currentStep] || []}
                    completedFields={progressData.completedFields}
                    missingFields={missingFields}
                  />
                )}

                {/* Success message when all mandatory fields are complete */}
                {mandatoryFieldsByStep[currentStep] && missingFields.length === 0 && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="bg-green-50 rounded-lg p-2 text-center"
                  >
                    <div className="text-green-600 font-medium text-xs">
                      ✅ Required fields complete
                    </div>
                  </motion.div>
                )}

                {/* Quick Actions */}
                <QuickActions
                  onGetHelp={onGetHelp}
                  onSkipOptional={onSkipOptional}
                  canSkip={canSkipStep}
                  hasOptionalContent={hasOptionalContent}
                />
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Minimized state hint */}
      {isMinimized && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap pointer-events-none"
        >
          Click to expand assistant
        </motion.div>
      )}
    </motion.div>
  );
};
