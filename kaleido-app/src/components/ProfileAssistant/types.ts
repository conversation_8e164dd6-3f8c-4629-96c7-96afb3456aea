export interface ProfileAssistantProps {
  currentStep: string;
  formData: any;
  onSkipOptional?: () => void;
  onGetHelp?: () => void;
  className?: string;
}

export interface AssistantMood {
  icon: string;
  message: string;
  color: string;
}

export interface ProgressData {
  mandatoryProgress: number;
  optionalProgress: number;
  overallProgress: number;
  completedFields: string[];
  missingFields: string[];
}

export interface StepMessage {
  greeting: string;
  mandatory: string;
  tip?: string;
  encouragement?: string;
  completion?: string;
  fields?: {
    display: string;
    fun: string;
  };
  benefits?: string;
  celebration?: string;
}

export interface ProgressRingProps {
  value: number;
  label: string;
  color: 'red' | 'blue' | 'green' | 'gray' | 'indigo';
  size?: 'small' | 'medium' | 'large';
  testId?: string;
}

export interface MandatoryFieldsListProps {
  currentStep: string;
  mandatoryFields: string[];
  completedFields: string[];
  missingFields: string[];
}

export interface AssistantAvatarProps {
  mood: AssistantMood;
  hasNewMessage: boolean;
  isMinimized: boolean;
  onToggle: () => void;
  onMinimize: () => void;
}
