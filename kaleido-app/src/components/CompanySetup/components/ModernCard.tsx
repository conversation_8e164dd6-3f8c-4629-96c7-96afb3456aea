'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { CheckCircle2, AlertTriangle, Info, Sparkles, Building2 } from 'lucide-react';

export type CardVariant = 'default' | 'success' | 'warning' | 'info' | 'gradient' | 'required';

interface ModernCardProps {
  children: React.ReactNode;
  variant?: CardVariant;
  title?: string;
  subtitle?: string;
  icon?: React.ReactNode;
  className?: string;
  collapsible?: boolean;
  defaultExpanded?: boolean;
  required?: boolean;
  completed?: boolean;
  onToggle?: (expanded: boolean) => void;
}

const getVariantStyles = (variant: CardVariant, completed?: boolean) => {
  switch (variant) {
    case 'success':
      return 'border-gray-200 bg-white shadow-sm';
    case 'warning':
      return 'border-gray-200 bg-white shadow-sm';
    case 'info':
      return 'border-gray-200 bg-white shadow-sm';
    case 'gradient':
      return 'border-gray-200 bg-white shadow-sm';
    case 'required':
      return completed
        ? 'border-gray-200 bg-white shadow-sm'
        : 'border-pink-200 bg-white shadow-sm';
    default:
      return 'border-gray-200 bg-white shadow-sm';
  }
};

const getVariantIcon = (variant: CardVariant, completed?: boolean) => {
  switch (variant) {
    case 'success':
      return <CheckCircle2 className="w-5 h-5 text-gray-600" />;
    case 'warning':
      return <AlertTriangle className="w-5 h-5 text-gray-600" />;
    case 'info':
      return <Info className="w-5 h-5 text-gray-600" />;
    case 'gradient':
      return <Sparkles className="w-5 h-5 text-gray-600" />;
    case 'required':
      return completed ? (
        <CheckCircle2 className="w-5 h-5 text-green-600" />
      ) : (
        <AlertTriangle className="w-5 h-5 text-pink-500" />
      );
    default:
      return <Building2 className="w-5 h-5 text-gray-600" />;
  }
};

export const ModernCard: React.FC<ModernCardProps> = ({
  children,
  variant = 'default',
  title,
  subtitle,
  icon,
  className = '',
  required = false,
  completed = false,
  ...props
}) => {
  const variantStyles = getVariantStyles(variant, completed);
  const defaultIcon = icon || getVariantIcon(variant, completed);

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
      className={`border rounded-xl shadow-sm hover:shadow-md transition-all duration-200 ${variantStyles} ${className}`}
    >
      {(title || subtitle || defaultIcon) && (
        <div className="px-6 py-5 border-b border-gray-100">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-4">
              {defaultIcon && (
                <div className="flex items-center justify-center w-10 h-10">{defaultIcon}</div>
              )}
              <div>
                {title && (
                  <div className="flex items-center gap-3">
                    <h3 className="text-lg font-bold text-gray-900 tracking-tight">{title}</h3>
                    {required && !completed && (
                      <span className="inline-flex items-center text-xs font-medium px-2.5 py-1 bg-pink-50 text-pink-600 rounded-full">
                        Required
                      </span>
                    )}
                    {completed && (
                      <span className="inline-flex items-center text-xs font-medium px-2.5 py-1 bg-green-50 text-green-600 rounded-full">
                        Complete
                      </span>
                    )}
                  </div>
                )}
                {subtitle && <p className="text-sm text-gray-600 mt-2 font-medium">{subtitle}</p>}
              </div>
            </div>
          </div>
        </div>
      )}
      <div className="p-6">{children}</div>
    </motion.div>
  );
};

export const CompactCard: React.FC<ModernCardProps> = ({
  children,
  variant = 'default',
  className = '',
  ...props
}) => {
  const variantStyles = getVariantStyles(variant, props.completed);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.98 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.15 }}
      className={`border rounded-xl shadow-sm hover:shadow-md transition-all duration-200 p-5 ${variantStyles} ${className}`}
    >
      {children}
    </motion.div>
  );
};

export const InfoTip: React.FC<{
  title: string;
  description: string;
  icon?: React.ReactNode;
  variant?: 'info' | 'success' | 'warning';
}> = ({ title, description, icon, variant = 'info' }) => {
  const variantStyles = {
    info: 'bg-blue-50 border-blue-200 text-blue-800',
    success: 'bg-green-50 border-green-200 text-green-800',
    warning: 'bg-amber-50 border-amber-200 text-amber-800',
  };

  const variantIcons = {
    info: <Info className="w-4 h-4" />,
    success: <CheckCircle2 className="w-4 h-4" />,
    warning: <AlertTriangle className="w-4 h-4" />,
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.2 }}
      className={`flex gap-3 p-4 border rounded-lg ${variantStyles[variant]}`}
    >
      <div className="flex-shrink-0 mt-0.5">{icon || variantIcons[variant]}</div>
      <div className="flex-1 space-y-1">
        <p className="text-sm font-semibold">{title}</p>
        <p className="text-xs opacity-90">{description}</p>
      </div>
    </motion.div>
  );
};

export default ModernCard;
