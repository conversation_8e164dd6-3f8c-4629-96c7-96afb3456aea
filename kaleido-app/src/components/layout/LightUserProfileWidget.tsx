import React, { useEffect, useRef, useState } from 'react';

import { motion } from 'framer-motion';
import {
  Bell,
  BriefcaseIcon,
  ChevronDown,
  GraduationCapIcon,
  LogOut,
  UserIcon,
  Users2Icon,
} from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

import { useNotifications } from '@/hooks/useNotifications';
import { useUser } from '@/hooks/useUser';
import { usePublicAuth } from '@/hooks/usePublicAuth';
import { logoutUser } from '@/lib/apiHelper';
import apiHelper from '@/lib/apiHelper';
import { getUserRole } from '@/lib/auth0-role-management-nextjs';
import { UserRole } from '@/types/roles';
import { getUserDisplayName, toTitleCase } from '@/utils/getUserDisplayName';

// Simplified hook for public pages that works with basic Auth0 user data
const usePublicUserProfileData = () => {
  const { user, isLoading: userLoading, error } = usePublicAuth();
  const [userRole, setUserRole] = useState<string | null>(null);
  const [profileRecord, setProfileRecord] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch user role when user is available
  useEffect(() => {
    const fetchUserRole = async () => {
      if (!user?.sub) {
        setUserRole(null);
        return;
      }

      // Debug: Log the complete user object to understand the data structure
      console.log('🔍 LightUserProfileWidget - Complete user object:', {
        sub: user.sub,
        name: user.name,
        given_name: user.given_name,
        family_name: user.family_name,
        nickname: user.nickname,
        email: user.email,
        picture: user.picture,
        metadata: user.user_metadata,
        app_metadata: user.app_metadata,
        fullUser: user,
      });

      setIsLoading(true);
      try {
        // Try to get role from Auth0 user metadata first
        const roleFromUser = getUserRole(user);
        console.log('🎭 Role from Auth0 metadata:', roleFromUser);

        if (roleFromUser) {
          setUserRole(roleFromUser);
          setIsLoading(false);
          return;
        }

        // Fallback: Try to validate profile to get role
        console.log('📋 Trying profile validation fallback...');
        const validationResponse = await apiHelper.post('/job-seekers/validate-profile', user);
        console.log('📋 Profile validation response:', validationResponse);
        console.log('📋 Specific role from validation:', validationResponse?.role);
        console.log('📋 All properties:', Object.keys(validationResponse || {}));

        if (validationResponse?.role) {
          setUserRole(validationResponse.role);
        } else if (validationResponse?.record && validationResponse?.isValid !== false) {
          // If we successfully validate a job seeker profile, user is a job seeker
          setUserRole(UserRole.JOB_SEEKER);
          console.log('📋 Inferred role as JOB_SEEKER from successful profile validation');
        }

        // Also check if validation response has profile data with real name
        if (validationResponse?.record) {
          console.log('📋 Profile record from validation:', validationResponse.record);
          setProfileRecord(validationResponse.record);
        }
      } catch (error) {
        console.error('❌ Error fetching user role:', error);
        // Don't set error state, just continue without role
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserRole();
  }, [user?.sub]);

  // Get display name using the utility function
  const getDisplayName = () => {
    if (!user) return 'Guest';

    // If we have profile record with firstName/lastName, use that
    if (profileRecord?.firstName && profileRecord?.lastName) {
      const fullName = `${profileRecord.firstName} ${profileRecord.lastName}`;
      console.log('📝 Using profile record name:', fullName);
      return fullName;
    }

    console.log('📝 Getting display name from user...');
    const name = getUserDisplayName(user);
    console.log('📝 getUserDisplayName returned:', name);

    return name && name !== 'Guest' ? name : 'User';
  };

  return {
    displayName: getDisplayName(),
    userRole,
    profileRecord,
    isLoading,
  };
};

interface LightUserProfileWidgetProps {
  userName?: string;
  userRole?: string;
  simplified?: boolean;
}

const getRoleIcon = (role?: string) => {
  switch (role?.toLowerCase()) {
    case UserRole.EMPLOYER:
      return <BriefcaseIcon className="text-black w-3.5 h-3.5" />;
    case UserRole.JOB_SEEKER:
      return <UserIcon className="text-black w-3.5 h-3.5" />;
    case UserRole.ADMIN:
      return <Users2Icon className="text-black w-3.5 h-3.5" />;
    case UserRole.GRADUATE:
      return <GraduationCapIcon className="text-black w-3.5 h-3.5" />;
    default:
      return <UserIcon className="text-black w-3.5 h-3.5" />;
  }
};

const formatRole = (role?: string) => {
  if (!role) return 'Employer';
  // Convert kebab-case or snake_case to Title Case
  return role
    .replace(/[-_]/g, ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

export const LightUserProfileWidget: React.FC<LightUserProfileWidgetProps> = ({
  userName,
  userRole,
  simplified = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();
  const { user } = useUser();
  const { notifications = [] } = useNotifications(user?.sub ?? '');
  const hasNotifications = notifications.length > 0;

  // Use the simplified hook for public pages
  const {
    displayName,
    userRole: effectiveUserRole,
    isLoading: profileLoading,
  } = usePublicUserProfileData();
  const formattedName = toTitleCase(displayName);

  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSignOut = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    logoutUser();
  };

  const handleNotificationsClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsOpen(false);
    router.push('/notifications');
  };

  // Get user initials for avatar fallback
  const getInitials = (name?: string) => {
    const nameToUse = name || displayName;
    if (!nameToUse) return 'G';
    return nameToUse
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase();
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      className="flex items-center"
    >
      {/* Notification Bell - Hide in simplified mode */}
      {!simplified && (
        <button
          type="button"
          onClick={handleNotificationsClick}
          className="relative p-2 rounded-full hover:bg-black/5 transition-colors"
          aria-label="Notifications"
        >
          <Bell className="w-5 h-5 text-gray-600" />
          {hasNotifications && (
            <span className="absolute top-2 right-2 w-2 h-2 bg-purple-500 rounded-full animate-pulse"></span>
          )}
        </button>
      )}

      {/* User Profile */}
      <div className="relative" ref={dropdownRef}>
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center gap-3 hover:bg-black/5 p-2 rounded-full transition-colors"
          aria-label="User profile"
        >
          {/* User Avatar */}
          <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center overflow-hidden border border-gray-200">
            {user?.picture ? (
              <Image
                src={user.picture}
                alt={formattedName}
                width={40}
                height={40}
                className="w-full h-full object-cover rounded-full"
              />
            ) : (
              <span className="text-gray-700 text-sm font-medium">{getInitials()}</span>
            )}
          </div>

          {/* User Info - Hide in simplified mode */}
          {!simplified && (
            <div className="flex flex-col items-start">
              <span className="text-base font-medium text-gray-900">{formattedName}</span>
              <div className="flex items-center gap-1.5 px-2.5 py-1 bg-indigo-100 rounded-full border border-indigo-200">
                <span className="text-indigo-700">{getRoleIcon(effectiveUserRole)}</span>
                <div className="text-xs text-indigo-800 font-medium">
                  {formatRole(effectiveUserRole)}
                </div>
              </div>
            </div>
          )}

          <ChevronDown
            className={`w-5 h-5 text-gray-500 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          />
        </button>

        {/* Dropdown Menu */}
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 8, scale: 0.96 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 8, scale: 0.96 }}
            transition={{ duration: 0.15, ease: [0.23, 1, 0.32, 1] }}
            className="absolute right-0 mt-3 w-72 rounded-2xl bg-white border border-gray-200 shadow-2xl z-10 overflow-hidden"
          >
            {/* User Header Section */}
            <div className="relative p-5 border-b border-gray-100 bg-gradient-to-r from-indigo-50 to-blue-50">
              <div className="relative flex items-center gap-4">
                <div className="relative">
                  <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-indigo-500 to-blue-500 p-0.5">
                    <div className="w-full h-full rounded-2xl bg-white flex items-center justify-center overflow-hidden">
                      {user?.picture ? (
                        <Image
                          src={user.picture}
                          alt={formattedName}
                          width={56}
                          height={56}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <span className="text-indigo-600 font-semibold text-lg">
                          {getInitials()}
                        </span>
                      )}
                    </div>
                  </div>
                  {/* Status indicator */}
                  <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white" />
                </div>
                <div className="flex-1">
                  <p className="text-gray-900 font-semibold text-base leading-tight">
                    {formattedName}
                  </p>
                  <div className="flex items-center gap-2 mt-1">
                    <div className="flex items-center text-black gap-1.5 px-2.5 py-1 bg-indigo-100 rounded-full border border-indigo-200">
                      {getRoleIcon(effectiveUserRole)}
                      <div className="text-sm text-indigo-800 font-medium">
                        <span className="text-black">{formatRole(effectiveUserRole)} </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Menu Items */}
            <div className="p-2">
              {/* Notifications */}
              <button
                type="button"
                onClick={handleNotificationsClick}
                className="group w-full text-left px-4 py-3.5 rounded-xl transition-all duration-200 flex items-center gap-3 relative overflow-hidden hover:bg-gray-50"
              >
                <div className="relative flex items-center justify-center w-10 h-10 rounded-xl bg-indigo-50 group-hover:bg-indigo-100 transition-colors border border-indigo-100">
                  <Bell className="w-5 h-5 text-indigo-600" />
                  {hasNotifications && (
                    <span className="absolute -top-1 -right-1 flex h-3 w-3">
                      <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-purple-400 opacity-75"></span>
                      <span className="relative inline-flex rounded-full h-3 w-3 bg-purple-500"></span>
                    </span>
                  )}
                </div>
                <div className="flex-1">
                  <span className="text-sm font-medium text-gray-900 group-hover:text-gray-900">
                    Notifications
                  </span>
                  {hasNotifications && (
                    <p className="text-xs text-gray-500 mt-0.5">{notifications.length} unread</p>
                  )}
                </div>
                {hasNotifications && (
                  <div className="px-2.5 py-1 bg-purple-100 rounded-lg border border-purple-200">
                    <span className="text-xs font-medium text-purple-700">
                      {notifications.length}
                    </span>
                  </div>
                )}
              </button>

              {/* Divider */}
              <div className="my-2 mx-4 h-px bg-gray-200" />

              {/* Sign Out */}
              <button
                type="button"
                onClick={handleSignOut}
                className="group w-full text-left px-4 py-3.5 rounded-xl transition-all duration-200 flex items-center gap-3 relative overflow-hidden hover:bg-red-50"
              >
                <div className="relative flex items-center justify-center w-10 h-10 rounded-xl bg-gray-50 group-hover:bg-red-100 transition-colors border border-gray-200 group-hover:border-red-200">
                  <LogOut className="w-5 h-5 text-gray-600 group-hover:text-red-600" />
                </div>
                <span className="text-sm font-medium text-gray-700 group-hover:text-red-600">
                  Sign Out
                </span>
              </button>
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};
