/* WithdrawApplicationModal Styles - Elegant Glassmorphic Design */
.modalOverlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.4);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 70;
  padding: 1rem;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modalContainer {
  background: transparent;
  border-radius: 1.5rem;
  max-width: 56rem;
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  position: relative;
  display: flex;
  animation: slideUp 0.4s ease-out;
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.imageSection {
  flex: 0 0 40%;
  position: relative;
  overflow: hidden;
  border-top-left-radius: 1.5rem;
  border-bottom-left-radius: 1.5rem;
}

.heroImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  filter: brightness(0.75);
  z-index: 1;
}

.content {
  flex: 1;
  padding: 2rem;
  background: rgba(65, 3, 58, 0.012);
  -webkit-backdrop-filter: blur(32px);
  backdrop-filter: blur(32px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-top-right-radius: 1.5rem;
  border-bottom-right-radius: 1.5rem;
  border-left: none;
  position: relative;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    0 8px 32px rgba(0, 0, 0, 0.3);
}

.closeButton {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(16px);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 2.25rem;
  height: 2.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #e2e8f0;
  z-index: 10;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #f8fafc;
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

.closeButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.title {
  color: #f1f5f9;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.titleIcon {
  width: 1.25rem;
  height: 1.25rem;
  color: #ef4444;
}

.warningBox {
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(16px);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
  padding: 1.25rem;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.warningIcon {
  width: 1rem;
  height: 1rem;
  color: #f59e0b;
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.warningTitle {
  font-weight: 600;
  color: #f1f5f9;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.warningText {
  color: #cbd5e1;
  font-size: 0.8125rem;
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.warningSubtext {
  color: #94a3b8;
  font-size: 0.75rem;
  line-height: 1.4;
}

.jobHighlight {
  font-weight: 600;
  color: #f8fafc;
}

.reasonSection {
  background: rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(16px);
  backdrop-filter: blur(16px);
  border-radius: 1rem;
  padding: 1.25rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.reasonLabel {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.reasonIcon {
  width: 1rem;
  height: 1rem;
  color: #e2e8f0;
}

.labelText {
  font-weight: 500;
  color: #f1f5f9;
  font-size: 0.8125rem;
}

.reasonTextarea {
  width: 100%;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 0.75rem;
  padding: 0.75rem;
  color: #f1f5f9;
  font-size: 0.8125rem;
  line-height: 1.4;
  resize: vertical;
  min-height: 4rem;
  transition: all 0.2s ease;
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
}

.reasonTextarea::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.reasonTextarea:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.05);
}

.errorMessage {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  color: #ef4444;
  font-size: 0.75rem;
  margin-top: 0.5rem;
}

.errorIcon {
  width: 0.875rem;
  height: 0.875rem;
}

.infoBox {
  background: rgba(255, 255, 255, 0.03);
  -webkit-backdrop-filter: blur(16px);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 1rem;
  padding: 1rem;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

.infoIcon {
  width: 1rem;
  height: 1rem;
  color: #cbd5e1;
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.infoText {
  color: #94a3b8;
  font-size: 0.75rem;
  line-height: 1.4;
}

.actionButtons {
  display: flex;
  gap: 1rem;
  margin-top: auto;
}

.button {
  flex: 1;
  padding: 0.75rem 1.25rem;
  border-radius: 0.75rem;
  font-weight: 600;
  font-size: 0.8125rem;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 2.5rem;
  white-space: nowrap;
}

.cancelButton {
  background: rgba(255, 255, 255, 0.05);
  color: #cbd5e1;
  border: 1px solid rgba(255, 255, 255, 0.15);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
}

.cancelButton:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
  color: #f1f5f9;
  border-color: rgba(255, 255, 255, 0.25);
}

.confirmButton {
  background: rgba(239, 68, 68, 0.8);
  color: white;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
}

.confirmButton:hover:not(:disabled) {
  background: rgba(239, 68, 68, 0.9);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.35);
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.buttonIcon {
  width: 0.875rem;
  height: 0.875rem;
}

.spinner {
  width: 0.875rem;
  height: 0.875rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .modalContainer {
    max-width: 48rem;
  }
}

@media (max-width: 768px) {
  .modalContainer {
    flex-direction: column;
    max-width: 90vw;
    max-height: 90vh;
  }

  .imageSection {
    flex: 0 0 200px;
    border-radius: 1.5rem 1.5rem 0 0;
  }

  .content {
    border-radius: 0 0 1.5rem 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-top: none;
    padding: 1.5rem;
    gap: 1.25rem;
  }

  .title {
    font-size: 1.25rem;
  }

  .actionButtons {
    flex-direction: column;
    gap: 0.75rem;
  }

  .button {
    white-space: normal;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .modalContainer {
    max-width: 95vw;
  }

  .content {
    padding: 1.25rem;
  }

  .imageSection {
    flex: 0 0 180px;
  }
}
