'use client';

import React from 'react';
import { XCircle } from 'lucide-react';

interface WithdrawApplicationButtonProps {
  onClick: () => void;
  disabled?: boolean;
  variant?: 'default' | 'compact';
  className?: string;
  label?: string;
}

/**
 * Reusable Withdraw Application Button with consistent styling
 * Used across the application for withdrawing job applications
 */
export const WithdrawApplicationButton: React.FC<WithdrawApplicationButtonProps> = ({
  onClick,
  disabled = false,
  variant = 'default',
  className = '',
  label,
}) => {
  // Determine the label based on variant or custom label
  const buttonLabel = label || (variant === 'compact' ? 'Withdraw' : 'Withdraw Application');

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`
        relative bg-gradient-to-r from-red-500/20 to-red-600/20 hover:from-red-500/30 hover:to-red-600/30
        backdrop-blur-xl backdrop-saturate-150 text-white font-semibold
        transition-all duration-300 flex items-center gap-2
        border border-red-400/30 hover:border-red-400/50
        shadow-[0_8px_32px_0_rgba(220,38,38,0.2)] hover:shadow-[0_8px_40px_0_rgba(220,38,38,0.35)]
        before:absolute before:inset-0 before:rounded-xl before:bg-gradient-to-r before:from-red-400/10 before:to-red-500/10
        hover:scale-105 overflow-hidden group
        disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100
        ${variant === 'compact' ? 'px-4 py-2 text-sm rounded-lg' : 'px-6 py-3 text-sm rounded-xl'}
        ${className}
      `}
    >
      {/* Glassmorphic shine effect */}
      <div className="absolute inset-0 bg-gradient-to-tr from-red-300/10 via-transparent to-transparent opacity-50" />

      {/* Content */}
      <XCircle
        className={`
          relative z-10 text-red-200 group-hover:text-white transition-colors
          ${variant === 'compact' ? 'w-4 h-4' : 'w-5 h-5'}
        `}
      />
      <span className="relative z-10">{buttonLabel}</span>

      {/* Glow effect on hover */}
      <div className="absolute -inset-1 bg-gradient-to-r from-red-500/20 to-red-600/20 rounded-xl blur-lg opacity-0 group-hover:opacity-75 transition-opacity duration-300" />
    </button>
  );
};

export default WithdrawApplicationButton;
