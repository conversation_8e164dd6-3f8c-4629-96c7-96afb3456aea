'use client';

import React from 'react';

import { AnimatePresence, motion } from 'framer-motion';
import { X } from 'lucide-react';

import JobDetailsPage from './JobDetailsPage';
import { ScoutJob } from './types';

interface JobDetailsModalProps {
  job: <PERSON>J<PERSON>;
  isOpen: boolean;
  onClose: () => void;
}

const JobDetailsModal: React.FC<JobDetailsModalProps> = ({ job, isOpen, onClose }) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Full screen backdrop with blur - extra layer for better coverage */}
          <div className="fixed inset-0 bg-black/10 z-40" />
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/20 backdrop-blur-md z-50"
            onClick={onClose}
            style={{ top: '-10px', bottom: '-10px', left: '-10px', right: '-10px' }} // Extra coverage
          />

          {/* Modal sliding from bottom */}
          <motion.div
            initial={{ y: '100%', opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: '100%', opacity: 0 }}
            transition={{
              type: 'spring',
              damping: 25,
              stiffness: 300,
              opacity: { duration: 0.3 },
            }}
            className="fixed inset-x-0 bottom-0 max-h-[90vh] sm:max-h-[85vh] bg-gradient-to-r from-slate-900/95 via-purple-950/95 to-slate-900/95 backdrop-blur-xl rounded-t-2xl sm:rounded-t-3xl z-[60] overflow-hidden flex flex-col border-t border-purple-400/20"
            style={{
              boxShadow:
                '0 -20px 50px -20px rgba(168, 85, 247, 0.5), 0 -10px 20px -10px rgba(168, 85, 247, 0.3)',
              minHeight: '60vh',
              height: 'auto',
              transition: 'height 0.3s ease-in-out',
            }}
          >
            {/* Header with gradient background */}
            <div className="relative flex-shrink-0 bg-gradient-to-b from-slate-900/80 to-transparent">
              {/* Gradient shadows emitting from edges */}
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 via-transparent to-purple-600/20 blur-3xl transform scale-110" />

              {/* Header content */}
              <div className="relative z-10 p-4 sm:p-6 flex items-center justify-between">
                <div className="flex-1 pr-4">
                  <h2 className="text-xl sm:text-2xl font-bold text-white tracking-tight">
                    Job Details
                  </h2>
                  <p className="text-sm text-white/60 mt-1">
                    {job.jobTitle || 'Position Information'}
                  </p>
                </div>

                {/* Close button */}
                <button
                  onClick={onClose}
                  className="p-2 rounded-full bg-white/10 backdrop-blur-md hover:bg-white/20 transition-all border border-white/20"
                  aria-label="Close modal"
                >
                  <X className="w-5 h-5 text-white" />
                </button>
              </div>
            </div>

            {/* Content with scrollable area */}
            <motion.div
              className="flex-1 overflow-y-auto bg-slate-900/50 custom-scrollbar"
              animate={{ height: 'auto' }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
            >
              <div className="p-4 sm:p-6">
                <JobDetailsPage job={job} />
              </div>
            </motion.div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default JobDetailsModal;
