'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence, useScroll, useTransform } from 'framer-motion';
import {
  Target,
  TrendingUp,
  Lightbulb,
  CheckCircle2,
  AlertCircle,
  ArrowRight,
  Hash,
  ChevronRight,
  ChevronLeft,
  Star,
  Zap,
  BookOpen,
  Briefcase,
  Users,
  MessageCircle,
  BarChart3,
  Clock,
  Sparkles,
  Shield,
  Rocket,
  Brain,
  Globe,
  Award,
  TrendingDown,
  Building2,
  GraduationCap,
  Heart,
  Info,
  MapPin,
  DollarSign,
  Code,
  Layers,
  GitBranch,
  Database,
  Cloud,
  Lock,
  Cpu,
  Gauge,
  Maximize2,
  Circle,
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { SimpleTable, SimpleTableColumn } from '@/components/ui/SimpleTable';
import { cn } from '@/lib/utils';

interface AnalysisRendererProps {
  content: string;
  className?: string;
}

interface Section {
  title: string;
  content: string[];
  icon?: React.ReactNode;
  color?: string;
  bgPattern?: string;
  importance?: 'high' | 'medium' | 'low';
  keyHighlight?: string;
}

const iconMap: Record<string, React.ReactElement> = {
  overview: <BookOpen className="w-5 h-5 text-blue-400" />,
  summary: <Sparkles className="w-5 h-5 text-yellow-400" />,
  strength: <Star className="w-5 h-5 text-emerald-400" />,
  advantage: <Award className="w-5 h-5 text-green-400" />,
  challenge: <AlertCircle className="w-5 h-5 text-orange-400" />,
  gap: <TrendingDown className="w-5 h-5 text-red-400" />,
  weakness: <Shield className="w-5 h-5 text-red-400" />,
  opportunity: <TrendingUp className="w-5 h-5 text-purple-400" />,
  potential: <Rocket className="w-5 h-5 text-indigo-400" />,
  recommendation: <Target className="w-5 h-5 text-purple-400" />,
  action: <Zap className="w-5 h-5 text-pink-400" />,
  'next step': <ArrowRight className="w-5 h-5 text-purple-400" />,
  skill: <Brain className="w-5 h-5 text-yellow-400" />,
  competenc: <GraduationCap className="w-5 h-5 text-indigo-400" />,
  career: <Briefcase className="w-5 h-5 text-blue-400" />,
  path: <GitBranch className="w-5 h-5 text-purple-400" />,
  market: <BarChart3 className="w-5 h-5 text-cyan-400" />,
  trend: <TrendingUp className="w-5 h-5 text-cyan-400" />,
  timeline: <Clock className="w-5 h-5 text-pink-400" />,
  timeframe: <Gauge className="w-5 h-5 text-pink-400" />,
  demand: <Users className="w-5 h-5 text-blue-400" />,
  supply: <Database className="w-5 h-5 text-indigo-400" />,
  growth: <Rocket className="w-5 h-5 text-green-400" />,
  projection: <TrendingUp className="w-5 h-5 text-emerald-400" />,
  emerging: <Sparkles className="w-5 h-5 text-yellow-400" />,
  technology: <Cpu className="w-5 h-5 text-blue-400" />,
  salary: <DollarSign className="w-5 h-5 text-yellow-400" />,
  compensation: <Award className="w-5 h-5 text-yellow-400" />,
  remote: <Globe className="w-5 h-5 text-blue-400" />,
  hybrid: <Building2 className="w-5 h-5 text-purple-400" />,
  company: <Building2 className="w-5 h-5 text-indigo-400" />,
  hiring: <Users className="w-5 h-5 text-green-400" />,
  ai: <Brain className="w-5 h-5 text-purple-400" />,
  automation: <Cpu className="w-5 h-5 text-orange-400" />,
  geographic: <MapPin className="w-5 h-5 text-red-400" />,
  hotspot: <MapPin className="w-5 h-5 text-orange-400" />,
  certification: <Award className="w-5 h-5 text-yellow-400" />,
  conclusion: <CheckCircle2 className="w-5 h-5 text-green-400" />,
  cloud: <Cloud className="w-5 h-5 text-blue-400" />,
  security: <Lock className="w-5 h-5 text-red-400" />,
  devops: <Code className="w-5 h-5 text-purple-400" />,
  microservice: <Layers className="w-5 h-5 text-cyan-400" />,
};

const colorThemes = {
  blue: {
    gradient: 'from-blue-600 to-cyan-600',
    bg: 'bg-gradient-to-br from-blue-500/10 to-cyan-500/10',
    border: 'border-blue-500/20',
    text: 'text-blue-400',
    hoverBg: 'hover:bg-blue-500/10',
    iconBg: 'bg-gradient-to-br from-blue-600 to-cyan-600',
    badge: 'bg-blue-500/20 text-blue-300 border-blue-500/30',
    cardGradient: 'from-blue-600/20 via-transparent to-cyan-600/20',
  },
  green: {
    gradient: 'from-emerald-600 to-green-600',
    bg: 'bg-gradient-to-br from-emerald-500/10 to-green-500/10',
    border: 'border-emerald-500/20',
    text: 'text-emerald-400',
    hoverBg: 'hover:bg-emerald-500/10',
    iconBg: 'bg-gradient-to-br from-emerald-600 to-green-600',
    badge: 'bg-emerald-500/20 text-emerald-300 border-emerald-500/30',
    cardGradient: 'from-emerald-600/20 via-transparent to-green-600/20',
  },
  orange: {
    gradient: 'from-orange-600 to-red-600',
    bg: 'bg-gradient-to-br from-orange-500/10 to-red-500/10',
    border: 'border-orange-500/20',
    text: 'text-orange-400',
    hoverBg: 'hover:bg-orange-500/10',
    iconBg: 'bg-gradient-to-br from-orange-600 to-red-600',
    badge: 'bg-orange-500/20 text-orange-300 border-orange-500/30',
    cardGradient: 'from-orange-600/20 via-transparent to-red-600/20',
  },
  purple: {
    gradient: 'from-purple-600 to-pink-600',
    bg: 'bg-gradient-to-br from-purple-500/10 to-pink-500/10',
    border: 'border-purple-500/20',
    text: 'text-purple-400',
    hoverBg: 'hover:bg-purple-500/10',
    iconBg: 'bg-gradient-to-br from-purple-600 to-pink-600',
    badge: 'bg-purple-500/20 text-purple-300 border-purple-500/30',
    cardGradient: 'from-purple-600/20 via-transparent to-pink-600/20',
  },
  yellow: {
    gradient: 'from-yellow-600 to-amber-600',
    bg: 'bg-gradient-to-br from-yellow-500/10 to-amber-500/10',
    border: 'border-yellow-500/20',
    text: 'text-yellow-400',
    hoverBg: 'hover:bg-yellow-500/10',
    iconBg: 'bg-gradient-to-br from-yellow-600 to-amber-600',
    badge: 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30',
    cardGradient: 'from-yellow-600/20 via-transparent to-amber-600/20',
  },
  indigo: {
    gradient: 'from-indigo-600 to-purple-600',
    bg: 'bg-gradient-to-br from-indigo-500/10 to-purple-500/10',
    border: 'border-indigo-500/20',
    text: 'text-indigo-400',
    hoverBg: 'hover:bg-indigo-500/10',
    iconBg: 'bg-gradient-to-br from-indigo-600 to-purple-600',
    badge: 'bg-indigo-500/20 text-indigo-300 border-indigo-500/30',
    cardGradient: 'from-indigo-600/20 via-transparent to-purple-600/20',
  },
  cyan: {
    gradient: 'from-cyan-600 to-blue-600',
    bg: 'bg-gradient-to-br from-cyan-500/10 to-blue-500/10',
    border: 'border-cyan-500/20',
    text: 'text-cyan-400',
    hoverBg: 'hover:bg-cyan-500/10',
    iconBg: 'bg-gradient-to-br from-cyan-600 to-blue-600',
    badge: 'bg-cyan-500/20 text-cyan-300 border-cyan-500/30',
    cardGradient: 'from-cyan-600/20 via-transparent to-blue-600/20',
  },
  pink: {
    gradient: 'from-pink-600 to-rose-600',
    bg: 'bg-gradient-to-br from-pink-500/10 to-rose-500/10',
    border: 'border-pink-500/20',
    text: 'text-pink-400',
    hoverBg: 'hover:bg-pink-500/10',
    iconBg: 'bg-gradient-to-br from-pink-600 to-rose-600',
    badge: 'bg-pink-500/20 text-pink-300 border-pink-500/30',
    cardGradient: 'from-pink-600/20 via-transparent to-rose-600/20',
  },
};

// Define gradient color sets for the summary carousel
const summaryGradients = [
  'from-purple-600/40 via-transparent to-pink-600/40',
  'from-blue-600/40 via-transparent to-cyan-600/40',
  'from-emerald-600/40 via-transparent to-green-600/40',
  'from-orange-600/40 via-transparent to-red-600/40',
  'from-indigo-600/40 via-transparent to-purple-600/40',
];

export default function AnalysisRenderer({ content, className = '' }: AnalysisRendererProps) {
  const [currentHighlight, setCurrentHighlight] = useState(0);
  const [expandedSection, setExpandedSection] = useState<number | null>(null);

  const parseSections = (text: string): Section[] => {
    const sections: Section[] = [];
    const lines = text.split('\n');

    let currentSection: Section | null = null;
    let currentContent: string[] = [];
    let inTable = false;
    let tableLines: string[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmedLine = line.trim();

      // Check if we're starting a table
      if (!inTable && i + 1 < lines.length) {
        const nextLine = lines[i + 1].trim();
        if (trimmedLine.includes('|') && nextLine.includes('|') && nextLine.includes('-')) {
          inTable = true;
          tableLines = [trimmedLine];
          continue;
        }
      }

      // Handle table lines
      if (inTable) {
        if (trimmedLine.includes('|')) {
          tableLines.push(trimmedLine);
        } else {
          // End of table, add it as a special marker
          if (tableLines.length > 2) {
            currentContent.push(`__TABLE_START__`);
            tableLines.forEach(tl => currentContent.push(tl));
            currentContent.push(`__TABLE_END__`);
          }
          tableLines = [];
          inTable = false;
          // Process the current non-table line
          if (trimmedLine) {
            currentContent.push(trimmedLine);
          }
        }
        continue;
      }

      if (line.startsWith('#')) {
        // Close any open table
        if (tableLines.length > 2) {
          currentContent.push(`__TABLE_START__`);
          tableLines.forEach(tl => currentContent.push(tl));
          currentContent.push(`__TABLE_END__`);
          tableLines = [];
          inTable = false;
        }

        if (currentSection && currentContent.length > 0) {
          currentSection.content = currentContent;
          // Extract key highlight (first meaningful content) - no truncation
          const highlight = currentContent.find(
            c =>
              !c.startsWith('__TABLE_') &&
              (c.startsWith('- ') || c.startsWith('* ') || c.length > 20)
          );
          if (highlight) {
            currentSection.keyHighlight = highlight.replace(/^[-*]\s/, '');
          }
          sections.push(currentSection);
          currentContent = [];
        }

        const match = line.match(/^(#+)\s*(.+)$/);
        if (match) {
          const level = match[1].length;
          const title = match[2].trim();
          const lowerTitle = title.toLowerCase();

          // Find matching icon
          let icon: React.ReactElement = <Hash className="w-5 h-5 text-gray-400" />;
          for (const [key, iconComponent] of Object.entries(iconMap)) {
            if (lowerTitle.includes(key)) {
              icon = iconComponent as React.ReactElement;
              break;
            }
          }

          // Determine color based on content type
          let color = 'blue';
          let importance: 'high' | 'medium' | 'low' = 'medium';

          if (
            lowerTitle.includes('strength') ||
            lowerTitle.includes('advantage') ||
            lowerTitle.includes('opportunity')
          ) {
            color = 'green';
            importance = 'high';
          } else if (
            lowerTitle.includes('challenge') ||
            lowerTitle.includes('gap') ||
            lowerTitle.includes('weakness')
          ) {
            color = 'orange';
            importance = 'high';
          } else if (
            lowerTitle.includes('recommendation') ||
            lowerTitle.includes('action') ||
            lowerTitle.includes('next step')
          ) {
            color = 'purple';
            importance = 'high';
          } else if (lowerTitle.includes('skill') || lowerTitle.includes('competenc')) {
            color = 'yellow';
          } else if (lowerTitle.includes('career') || lowerTitle.includes('path')) {
            color = 'indigo';
          } else if (lowerTitle.includes('market') || lowerTitle.includes('trend')) {
            color = 'cyan';
          } else if (lowerTitle.includes('timeline') || lowerTitle.includes('timeframe')) {
            color = 'pink';
          }

          currentSection = {
            title,
            content: [],
            icon,
            color,
            importance,
          };
        }
      } else if (trimmedLine) {
        currentContent.push(trimmedLine);
      }
    }

    // Close any remaining table
    if (inTable && tableLines.length > 2) {
      currentContent.push(`__TABLE_START__`);
      tableLines.forEach(tl => currentContent.push(tl));
      currentContent.push(`__TABLE_END__`);
    }

    if (currentSection && currentContent.length > 0) {
      currentSection.content = currentContent;
      const highlight = currentContent.find(
        c =>
          !c.startsWith('__TABLE_') && (c.startsWith('- ') || c.startsWith('* ') || c.length > 20)
      );
      if (highlight) {
        currentSection.keyHighlight = highlight.replace(/^[-*]\s/, '');
      }
      sections.push(currentSection);
    }

    return sections.filter(section => section.content.length > 0);
  };

  const parseMarkdownTable = (lines: string[]): { headers: string[]; rows: string[][] } | null => {
    // Check if this looks like a markdown table
    if (lines.length < 2) return null;

    const headerLine = lines[0];
    const separatorLine = lines[1];

    // Check for table separator pattern (e.g., |---|---|---|)
    if (!separatorLine || !separatorLine.includes('|') || !separatorLine.includes('-')) {
      return null;
    }

    // Parse headers
    const headers = headerLine
      .split('|')
      .map(h => h.trim())
      .filter(h => h);

    // Parse rows
    const rows: string[][] = [];
    for (let i = 2; i < lines.length; i++) {
      const line = lines[i];
      if (!line.trim() || !line.includes('|')) break;

      const cells = line
        .split('|')
        .map(c => c.trim())
        .filter(c => c);
      if (cells.length > 0) {
        rows.push(cells);
      }
    }

    return headers.length > 0 ? { headers, rows } : null;
  };

  const renderTable = (table: { headers: string[]; rows: string[][] }, index: number) => {
    // Convert headers to SimpleTableColumn format
    const columns: SimpleTableColumn[] = table.headers.map((header, idx) => ({
      key: `col_${idx}`,
      label: header, // Use plain text for headers
      align: 'left' as const,
    }));

    // Convert rows to data format expected by SimpleTable
    const data = table.rows.map(row => {
      const rowData: Record<string, any> = {};
      row.forEach((cell, idx) => {
        rowData[`col_${idx}`] = formatText(cell);
      });
      return rowData;
    });

    return (
      <motion.div
        key={`table-${index}`}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.08, duration: 0.5 }}
        className="my-4"
      >
        <SimpleTable
          columns={columns}
          data={data}
          compact={true}
          hoverable={true}
          striped={false}
          className="rounded-lg overflow-hidden bg-white/[0.02] backdrop-blur-sm"
          headerClassName="bg-gradient-to-r from-purple-600/10 to-pink-600/10"
        />
      </motion.div>
    );
  };

  const renderSectionContent = (content: string[]) => {
    const elements: React.ReactNode[] = [];
    let i = 0;
    let elementIndex = 0;

    while (i < content.length) {
      const line = content[i];

      // Check for table marker
      if (line === '__TABLE_START__') {
        const tableLines: string[] = [];
        i++; // Skip the marker

        // Collect table lines until end marker
        while (i < content.length && content[i] !== '__TABLE_END__') {
          tableLines.push(content[i]);
          i++;
        }
        i++; // Skip the end marker

        // Parse and render the table
        const table = parseMarkdownTable(tableLines);
        if (table) {
          elements.push(renderTable(table, elementIndex++));
        }
      } else {
        // Render normal content
        elements.push(renderContentItem(line, elementIndex++));
        i++;
      }
    }

    // Group consecutive list items
    const groupedElements: React.ReactNode[] = [];
    let currentList: React.ReactNode[] = [];
    let inList = false;

    elements.forEach((element, idx) => {
      if (React.isValidElement(element) && element.type === 'motion.li') {
        currentList.push(element);
        inList = true;
      } else {
        if (inList && currentList.length > 0) {
          groupedElements.push(
            <ul key={`list-${idx}`} className="space-y-1">
              {currentList}
            </ul>
          );
          currentList = [];
          inList = false;
        }
        groupedElements.push(element);
      }
    });

    // Handle any remaining list items
    if (currentList.length > 0) {
      groupedElements.push(
        <ul key={`list-final`} className="space-y-1">
          {currentList}
        </ul>
      );
    }

    return <div className="space-y-2">{groupedElements}</div>;
  };

  const renderContentItem = (line: string, index: number) => {
    const isNumbered = line.match(/^\d+\.\s/);
    const isBullet = line.startsWith('- ') || line.startsWith('* ');
    const isSubPoint = line.startsWith('  ');

    if (isBullet || isNumbered) {
      const content = line.replace(/^[-*]\s/, '').replace(/^\d+\.\s/, '');

      return (
        <motion.li
          key={index}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: index * 0.08, duration: 0.5 }}
          className="flex items-start space-x-3 group py-2"
        >
          <div className="mt-1">
            {isNumbered ? (
              <div className="w-6 h-6 rounded-full bg-gradient-to-br from-white/20 to-white/10 flex items-center justify-center ring-1 ring-white/20">
                <span className="text-xs font-bold text-white">{line.match(/^(\d+)\./)?.[1]}</span>
              </div>
            ) : (
              <Circle className="w-2 h-2 text-white/40 group-hover:text-white/60 transition-colors" />
            )}
          </div>
          <span className="text-gray-200 leading-relaxed flex-1 text-sm">
            {formatText(content)}
          </span>
        </motion.li>
      );
    }

    if (isSubPoint) {
      return (
        <motion.div
          key={index}
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: index * 0.08, duration: 0.5 }}
          className="ml-12 py-1"
        >
          <p className="text-gray-400 text-sm leading-relaxed pl-4 border-l-2 border-white/10">
            {formatText(line.trim())}
          </p>
        </motion.div>
      );
    }

    return (
      <motion.p
        key={index}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.08, duration: 0.5 }}
        className="text-gray-200 leading-relaxed py-2 text-sm"
      >
        {formatText(line)}
      </motion.p>
    );
  };

  const cleanMarkdownText = (text: string): string => {
    // Remove numbered lists (e.g., "3. ")
    let cleaned = text.replace(/^\d+\.\s+/gm, '');

    // Remove bullet points
    cleaned = cleaned.replace(/^[-*+]\s+/gm, '');

    // Remove bold markdown but keep the text
    cleaned = cleaned.replace(/\*\*(.*?)\*\*/g, '$1');
    cleaned = cleaned.replace(/__(.*?)__/g, '$1');

    // Remove italic markdown but keep the text
    cleaned = cleaned.replace(/\*(.*?)\*/g, '$1');
    cleaned = cleaned.replace(/_(.*?)_/g, '$1');

    // Remove headers
    cleaned = cleaned.replace(/^#+\s+/gm, '');

    // Clean up extra spaces
    cleaned = cleaned.replace(/\s+/g, ' ').trim();

    return cleaned;
  };

  const formatText = (text: string) => {
    // For already cleaned text, just return it
    if (
      !text.includes('**') &&
      !text.includes('__') &&
      !text.includes('*') &&
      !text.includes('_')
    ) {
      return text;
    }

    // Enhanced markdown parsing for bold text
    const formattedText = text
      .split(/(\*\*[^*]+\*\*|\*[^*]+\*|__[^_]+__|_[^_]+_)/)
      .map((part, i) => {
        // Bold text with ** or __
        if (
          (part.startsWith('**') && part.endsWith('**')) ||
          (part.startsWith('__') && part.endsWith('__'))
        ) {
          return (
            <span key={i} className="font-bold text-white">
              {part.slice(2, -2)}
            </span>
          );
        }
        // Italic text with * or _
        if (
          (part.startsWith('*') && part.endsWith('*')) ||
          (part.startsWith('_') && part.endsWith('_'))
        ) {
          return (
            <em key={i} className="text-gray-100">
              {part.slice(1, -1)}
            </em>
          );
        }
        return part;
      });

    return formattedText;
  };

  const sections = parseSections(content);

  // Auto-rotate carousel
  useEffect(() => {
    if (sections.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentHighlight(prev => (prev + 1) % sections.length);
    }, 8000); // Slower rotation

    return () => clearInterval(interval);
  }, [sections.length]);

  const goToHighlight = (index: number) => {
    setCurrentHighlight(index);
  };

  const nextHighlight = () => {
    setCurrentHighlight(prev => (prev + 1) % sections.length);
  };

  const prevHighlight = () => {
    setCurrentHighlight(prev => (prev - 1 + sections.length) % sections.length);
  };

  return (
    <div className={cn('space-y-0', className)}>
      {/* Beautiful Gradient Summary Carousel */}
      {sections.length > 1 && (
        <div className="relative mb-8">
          <div className="max-w-5xl mx-auto">
            <motion.h3
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center text-xs font-medium text-gray-500 mb-6 uppercase tracking-[0.2em]"
            >
              Analysis Overview
            </motion.h3>

            <div className="relative">
              {/* Beautiful gradient background that changes with carousel */}
              <div className="absolute inset-0 -z-10">
                <div
                  className={cn(
                    'absolute inset-0 rounded-3xl bg-gradient-to-r opacity-40 blur-3xl transition-all duration-1000',
                    summaryGradients[currentHighlight % summaryGradients.length]
                  )}
                />
              </div>

              <div className="relative px-8 md:px-20">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={currentHighlight}
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -30 }}
                    transition={{ duration: 0.8, ease: [0.23, 1, 0.32, 1] }}
                    className="relative"
                  >
                    <div className="text-center space-y-4">
                      {/* Floating icon */}
                      <motion.div
                        initial={{ scale: 0, rotate: -180 }}
                        animate={{ scale: 1, rotate: 0 }}
                        transition={{ duration: 0.8, delay: 0.2, type: 'spring', stiffness: 200 }}
                        className="flex justify-center"
                      >
                        <div className="relative">
                          <div className="absolute inset-0 blur-xl bg-gradient-to-r from-purple-400 to-pink-400 opacity-40 rounded-full" />
                          <div className="relative w-14 h-14 rounded-xl bg-gradient-to-br from-purple-600/20 to-pink-600/20 backdrop-blur-sm flex items-center justify-center border border-purple-400/30">
                            <div className="text-purple-400">{sections[currentHighlight].icon}</div>
                          </div>
                        </div>
                      </motion.div>

                      {/* Title with gradient text */}
                      <motion.h4
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.8, delay: 0.3 }}
                        className="text-2xl md:text-3xl font-light text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-pink-400 to-blue-400"
                      >
                        {sections[currentHighlight].title}
                      </motion.h4>

                      {/* Summary text with beautiful styling */}
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.8, delay: 0.4 }}
                        className="max-w-3xl mx-auto"
                      >
                        <div className="relative">
                          <div className="text-gray-100 text-base md:text-lg leading-relaxed px-4 py-4">
                            {formatText(
                              cleanMarkdownText(
                                sections[currentHighlight].keyHighlight ||
                                  sections[currentHighlight].content[0]
                              )
                            )}
                          </div>
                        </div>
                      </motion.div>

                      {/* Action button */}
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.8, delay: 0.5 }}
                      >
                        <button
                          className="group inline-flex items-center gap-2 px-6 py-3 rounded-full bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-sm border border-purple-400/30 hover:from-purple-600/30 hover:to-pink-600/30 transition-all duration-300"
                          onClick={() => {
                            setExpandedSection(currentHighlight);
                            const element = document.getElementById(`section-${currentHighlight}`);
                            element?.scrollIntoView({ behavior: 'smooth', block: 'start' });
                          }}
                        >
                          <span className="text-sm text-purple-300 group-hover:text-purple-200">
                            View Details
                          </span>
                          <ArrowRight className="w-4 h-4 text-purple-400 group-hover:text-purple-300 group-hover:translate-x-1 transition-all" />
                        </button>
                      </motion.div>
                    </div>
                  </motion.div>
                </AnimatePresence>

                {/* Navigation Buttons - More subtle */}
                <button
                  className="absolute left-0 top-1/2 -translate-y-1/2 p-3 text-gray-600 hover:text-white transition-all duration-300 hover:bg-white/5 rounded-full"
                  onClick={prevHighlight}
                >
                  <ChevronLeft className="w-5 h-5" />
                </button>

                <button
                  className="absolute right-0 top-1/2 -translate-y-1/2 p-3 text-gray-600 hover:text-white transition-all duration-300 hover:bg-white/5 rounded-full"
                  onClick={nextHighlight}
                >
                  <ChevronRight className="w-5 h-5" />
                </button>
              </div>

              {/* Beautiful dots indicator */}
              <div className="flex justify-center items-center gap-2 mt-6">
                {sections.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => goToHighlight(index)}
                    className="relative p-1 transition-all duration-500"
                  >
                    <div
                      className={cn(
                        'rounded-full transition-all duration-500',
                        index === currentHighlight
                          ? 'w-8 h-2 bg-gradient-to-r from-purple-400 to-pink-400'
                          : 'w-2 h-2 bg-white/20 hover:bg-white/40'
                      )}
                    />
                    {index === currentHighlight && (
                      <motion.div
                        layoutId="highlight-glow"
                        className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-400 to-pink-400 blur-md opacity-50"
                      />
                    )}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Seamless Detailed Sections */}
      <div className="relative">
        {sections.map((section, sectionIndex) => {
          const theme = colorThemes[section.color as keyof typeof colorThemes] || colorThemes.blue;
          const isExpanded = expandedSection === sectionIndex;

          return (
            <motion.div
              id={`section-${sectionIndex}`}
              key={`section-${sectionIndex}`}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: sectionIndex * 0.1, duration: 0.6 }}
              className={cn(
                'relative border-l-4 transition-all duration-500',
                theme.border,
                sectionIndex === 0 && 'rounded-t-2xl',
                sectionIndex === sections.length - 1 && 'rounded-b-2xl mb-8',
                isExpanded && 'shadow-2xl z-10'
              )}
            >
              {/* Subtle Background */}
              <div className={cn('absolute inset-0 opacity-30', theme.bg)} />

              {/* Content */}
              <div className="relative">
                {/* Section Header */}
                <div
                  className={cn(
                    'px-6 py-6 cursor-pointer transition-all duration-300',
                    'hover:bg-white/5',
                    sectionIndex !== 0 && 'border-t border-white/5'
                  )}
                  onClick={() => setExpandedSection(isExpanded ? null : sectionIndex)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-white/10 to-white/5 flex items-center justify-center border border-white/10">
                        <div>{section.icon}</div>
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold text-white flex items-center gap-2">
                          {section.title}
                          {section.importance === 'high' && (
                            <Badge
                              variant="outline"
                              className="text-xs border-white/10 text-gray-400"
                            >
                              Key Section
                            </Badge>
                          )}
                        </h3>
                        <p className="text-sm text-gray-500 mt-0.5">
                          {section.content.length}{' '}
                          {section.content.length === 1 ? 'insight' : 'insights'}
                        </p>
                      </div>
                    </div>

                    <motion.div
                      animate={{ rotate: isExpanded ? 90 : 0 }}
                      transition={{ duration: 0.3 }}
                      className="flex-shrink-0"
                    >
                      <ChevronRight className="w-5 h-5 text-gray-400" />
                    </motion.div>
                  </div>
                </div>

                {/* Section Content - Completely hidden when collapsed */}
                <AnimatePresence>
                  {isExpanded && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.5, ease: [0.23, 1, 0.32, 1] }}
                      className="overflow-hidden"
                    >
                      <div className="px-6 pb-6 space-y-2">
                        {renderSectionContent(section.content)}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
          );
        })}
      </div>

      {/* Summary Footer */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: sections.length * 0.1, duration: 0.6 }}
        className="mt-4 p-6 rounded-2xl bg-gradient-to-br from-white/5 to-transparent border border-white/10"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-white/5">
              <Sparkles className="w-5 h-5 text-gray-400" />
            </div>
            <div>
              <h4 className="text-sm font-semibold text-white">Analysis Complete</h4>
              <p className="text-xs text-gray-500">
                {sections.length} sections •{' '}
                {sections.reduce((sum, s) => sum + s.content.length, 0)} insights generated
              </p>
            </div>
          </div>
          <Badge variant="outline" className="text-xs border-white/10 text-gray-500">
            AI Powered
          </Badge>
        </div>
      </motion.div>
    </div>
  );
}
