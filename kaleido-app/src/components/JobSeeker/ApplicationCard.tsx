import React from 'react';
import { motion } from 'framer-motion';
import {
  Briefcase,
  Building2,
  Calendar,
  CheckCircle2,
  Clock,
  MapPin,
  Sparkles,
  Star,
  TrendingUp,
  UserCheck,
  XCircle,
  AlertTriangle,
  Send,
  Eye,
  FileText,
  Trophy,
  Heart,
  Target,
  Zap,
} from 'lucide-react';

import { CandidateStatus } from '@/types/candidate.types';
import { UserRole } from '@/types/roles';
import { WithdrawApplicationButton } from '@/components/shared/WithdrawApplicationButton';

interface StatusTimelineItem {
  status: CandidateStatus;
  timestamp: string;
  note?: string;
}

interface JobApplication {
  id: string;
  jobId: string;
  status: CandidateStatus;
  createdAt: string;
  updatedAt: string;
  statusTimeline: StatusTimelineItem[];
  job: {
    id: string;
    jobType: string;
    companyName: string;
    department: string;
    location: string[];
    salaryRange: string;
    experience: string;
    status: string;
  };
}

interface ApplicationCardProps {
  application: JobApplication;
  onWithdraw: (applicationId: string) => void;
}

// Modern status configuration with gradient colors and icons
const statusConfig = {
  [CandidateStatus.APPLIED]: {
    label: 'Applied',
    icon: Send,
    gradient: 'from-purple-500 to-pink-500',
    bgGradient: 'from-purple-500/20 to-pink-500/20',
    borderColor: 'border-purple-400/30',
    glowColor: 'rgba(168, 85, 247, 0.3)',
    description: 'Your application has been submitted',
  },
  [CandidateStatus.MATCHED]: {
    label: 'Matched',
    icon: Target,
    gradient: 'from-blue-500 to-cyan-500',
    bgGradient: 'from-blue-500/20 to-cyan-500/20',
    borderColor: 'border-blue-400/30',
    glowColor: 'rgba(59, 130, 246, 0.3)',
    description: 'Your profile matches the requirements',
  },
  [CandidateStatus.SHORTLISTED]: {
    label: 'Shortlisted',
    icon: Star,
    gradient: 'from-amber-500 to-yellow-500',
    bgGradient: 'from-amber-500/20 to-yellow-500/20',
    borderColor: 'border-amber-400/30',
    glowColor: 'rgba(245, 158, 11, 0.3)',
    description: "You've been shortlisted for this position",
  },
  [CandidateStatus.INTERVIEWING]: {
    label: 'Interviewing',
    icon: UserCheck,
    gradient: 'from-violet-500 to-purple-500',
    bgGradient: 'from-violet-500/20 to-purple-500/20',
    borderColor: 'border-violet-400/30',
    glowColor: 'rgba(139, 92, 246, 0.3)',
    description: 'Interview process in progress',
  },
  [CandidateStatus.OFFER_PENDING_APPROVAL]: {
    label: 'Offer Pending',
    icon: Clock,
    gradient: 'from-orange-500 to-amber-500',
    bgGradient: 'from-orange-500/20 to-amber-500/20',
    borderColor: 'border-orange-400/30',
    glowColor: 'rgba(251, 146, 60, 0.3)',
    description: 'Offer is being prepared',
  },
  [CandidateStatus.OFFER_APPROVED]: {
    label: 'Offer Approved',
    icon: CheckCircle2,
    gradient: 'from-emerald-500 to-green-500',
    bgGradient: 'from-emerald-500/20 to-green-500/20',
    borderColor: 'border-emerald-400/30',
    glowColor: 'rgba(34, 197, 94, 0.3)',
    description: 'Offer has been approved internally',
  },
  [CandidateStatus.OFFER_EXTENDED]: {
    label: 'Offer Extended',
    icon: Trophy,
    gradient: 'from-green-500 to-emerald-500',
    bgGradient: 'from-green-500/20 to-emerald-500/20',
    borderColor: 'border-green-400/30',
    glowColor: 'rgba(34, 197, 94, 0.3)',
    description: "Congratulations! You've received an offer",
  },
  [CandidateStatus.OFFER_ACCEPTED]: {
    label: 'Offer Accepted',
    icon: Heart,
    gradient: 'from-pink-500 to-rose-500',
    bgGradient: 'from-pink-500/20 to-rose-500/20',
    borderColor: 'border-pink-400/30',
    glowColor: 'rgba(236, 72, 153, 0.3)',
    description: "You've accepted the offer",
  },
  [CandidateStatus.HIRED]: {
    label: 'Hired',
    icon: Sparkles,
    gradient: 'from-green-500 to-teal-500',
    bgGradient: 'from-green-500/20 to-teal-500/20',
    borderColor: 'border-green-400/30',
    glowColor: 'rgba(34, 197, 94, 0.3)',
    description: "Welcome aboard! You're hired",
  },
  [CandidateStatus.REJECTED]: {
    label: 'Not Selected',
    icon: XCircle,
    gradient: 'from-gray-500 to-gray-600',
    bgGradient: 'from-gray-500/20 to-gray-600/20',
    borderColor: 'border-gray-400/30',
    glowColor: 'rgba(107, 114, 128, 0.3)',
    description: 'Application was not selected',
  },
  [CandidateStatus.WITHDRAWN]: {
    label: 'Withdrawn',
    icon: AlertTriangle,
    gradient: 'from-red-500 to-rose-500',
    bgGradient: 'from-red-500/20 to-rose-500/20',
    borderColor: 'border-red-400/30',
    glowColor: 'rgba(239, 68, 68, 0.3)',
    description: 'You withdrew your application',
  },
};

// Default config for unknown statuses
const defaultStatusConfig = {
  label: 'Processing',
  icon: Clock,
  gradient: 'from-gray-500 to-gray-600',
  bgGradient: 'from-gray-500/20 to-gray-600/20',
  borderColor: 'border-gray-400/30',
  glowColor: 'rgba(107, 114, 128, 0.3)',
  description: 'Application is being processed',
};

const getStatusConfig = (status: CandidateStatus) => {
  return statusConfig[status] || defaultStatusConfig;
};

// Modern status step configuration
const statusSteps = [
  { key: CandidateStatus.APPLIED, label: 'Applied', icon: Send },
  { key: CandidateStatus.MATCHED, label: 'Matched', icon: Target },
  { key: CandidateStatus.SHORTLISTED, label: 'Shortlisted', icon: Star },
  { key: CandidateStatus.INTERVIEWING, label: 'Interviewing', icon: UserCheck },
  { key: CandidateStatus.OFFER_EXTENDED, label: 'Offer', icon: Trophy },
  { key: CandidateStatus.OFFER_ACCEPTED, label: 'Accepted', icon: Heart },
  { key: CandidateStatus.HIRED, label: 'Hired', icon: Sparkles },
];

export function ApplicationCard({ application, onWithdraw }: ApplicationCardProps) {
  // Get the latest status update from the timeline
  const latestStatus =
    application.statusTimeline.length > 0
      ? application.statusTimeline[application.statusTimeline.length - 1]
      : null;

  const statusInfo = getStatusConfig(application.status);
  const StatusIcon = statusInfo.icon;

  // Calculate progress for the status bar
  const currentStepIndex = statusSteps.findIndex(step => step.key === application.status);
  const progressPercentage =
    currentStepIndex === -1 ? 0 : ((currentStepIndex + 1) / statusSteps.length) * 100;

  // Check if application is in a final state
  const isWithdrawn = application.status === CandidateStatus.WITHDRAWN;
  const isRejected = application.status === CandidateStatus.REJECTED;
  const isFinalState = isWithdrawn || isRejected || application.status === CandidateStatus.HIRED;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="relative group"
    >
      {/* Glassmorphic Card Container */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-slate-900/90 via-purple-950/50 to-slate-900/90 backdrop-blur-xl border border-purple-400/20 shadow-2xl hover:shadow-purple-500/20 transition-all duration-500">
        {/* Animated gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-purple-600/10 via-pink-600/10 to-purple-600/10 animate-pulse" />

        {/* Glow effect on hover */}
        <div
          className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
          style={{
            background: `radial-gradient(600px circle at var(--mouse-x, 50%) var(--mouse-y, 50%), ${statusInfo.glowColor}, transparent 40%)`,
          }}
        />

        {/* Main Content */}
        <div className="relative z-10 p-6">
          {/* Header Section */}
          <div className="flex items-start justify-between mb-6">
            <div className="flex-1">
              <div className="flex items-start gap-4">
                {/* Company Icon Area */}
                <div
                  className={`relative p-3 rounded-xl bg-gradient-to-br ${statusInfo.bgGradient} ${statusInfo.borderColor} border backdrop-blur-sm`}
                >
                  <Building2 className="w-6 h-6 text-white" />
                </div>

                {/* Job Information */}
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-white mb-1 tracking-tight">
                    {application.job.jobType}
                  </h3>
                  <p className="text-purple-200 font-medium mb-2">{application.job.companyName}</p>

                  <div className="flex flex-wrap items-center gap-3 text-sm">
                    {application.job.location && application.job.location.length > 0 && (
                      <div className="flex items-center gap-1.5 text-purple-300/80">
                        <MapPin className="w-3.5 h-3.5" />
                        <span>{application.job.location[0]}</span>
                      </div>
                    )}
                    {application.job.department && (
                      <div className="flex items-center gap-1.5 text-purple-300/80">
                        <Briefcase className="w-3.5 h-3.5" />
                        <span>{application.job.department}</span>
                      </div>
                    )}
                    <div className="flex items-center gap-1.5 text-purple-300/80">
                      <Calendar className="w-3.5 h-3.5" />
                      <span>
                        {new Date(application.createdAt).toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric',
                        })}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Status Text - Simple and clean */}
            <div className="flex flex-col items-end gap-1">
              <div className="flex items-center gap-2">
                <StatusIcon className="w-4 h-4 text-purple-400" />
                <span className="text-sm font-semibold text-purple-300">{statusInfo.label}</span>
              </div>
              {latestStatus && latestStatus.timestamp && (
                <p className="text-[11px] text-purple-400/50">
                  Updated {new Date(latestStatus.timestamp).toLocaleDateString()}
                </p>
              )}
            </div>
          </div>

          {/* Modern Status Timeline */}
          <div className="mb-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-sm font-semibold text-purple-200 flex items-center gap-2">
                <TrendingUp className="w-4 h-4" />
                Application Progress
              </h4>
              {/* Small status description next to title */}
              <p className="text-xs text-purple-300/70 flex items-center gap-1.5">
                <Sparkles className="w-3 h-3" />
                {statusInfo.description}
              </p>
            </div>

            {/* Progress Timeline */}
            <div className="relative">
              {/* Progress Bar Container - positioned behind circles */}
              <div className="absolute top-5 left-0 right-0 flex items-center">
                <div className="flex-1 h-0.5 bg-purple-900/30 relative">
                  <motion.div
                    className="absolute top-0 left-0 h-full bg-gradient-to-r from-purple-500 to-pink-500"
                    initial={{ width: 0 }}
                    animate={{
                      width:
                        currentStepIndex === -1
                          ? '0%'
                          : `${(currentStepIndex / (statusSteps.length - 1)) * 100}%`,
                    }}
                    transition={{ duration: 1, ease: 'easeOut' }}
                  />
                </div>
              </div>

              {/* Status Steps */}
              <div className="relative flex justify-between">
                {statusSteps.map((step, index) => {
                  const StepIcon = step.icon;
                  const isPast = currentStepIndex > index;
                  const isCurrent = step.key === application.status;
                  const isDisabled = isFinalState && !isPast && !isCurrent;

                  return (
                    <motion.div
                      key={step.key}
                      className="relative flex flex-col items-center"
                      initial={{ scale: 0, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      {/* Step Circle */}
                      <div
                        className={`
                          relative w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 z-10
                          ${
                            isCurrent
                              ? 'bg-gradient-to-br from-purple-500 to-pink-500 shadow-lg shadow-purple-500/50'
                              : isPast
                                ? 'bg-gradient-to-br from-purple-600/50 to-pink-600/50'
                                : isDisabled
                                  ? 'bg-gray-800/50'
                                  : 'bg-purple-900/30 border border-purple-700/30'
                          }
                        `}
                      >
                        {isCurrent && (
                          <div className="absolute inset-0 rounded-full bg-gradient-to-br from-purple-400 to-pink-400 animate-ping opacity-30" />
                        )}
                        <StepIcon
                          className={`w-5 h-5 ${isPast || isCurrent ? 'text-white' : isDisabled ? 'text-gray-600' : 'text-purple-400/50'}`}
                        />
                      </div>

                      {/* Step Label */}
                      <span
                        className={`
                        mt-2 text-xs font-medium whitespace-nowrap
                        ${isCurrent ? 'text-white' : isPast ? 'text-purple-200' : isDisabled ? 'text-gray-600' : 'text-purple-400/50'}
                      `}
                      >
                        {step.label}
                      </span>

                      {/* Simple current text - not as a badge */}
                      {isCurrent && (
                        <span className="text-[10px] text-purple-400/70 mt-0.5">Current</span>
                      )}
                    </motion.div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Withdrawn/Rejected Alert */}
          {(isWithdrawn || isRejected) && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="mt-6 p-4 rounded-xl bg-gradient-to-r from-red-500/20 to-rose-500/20 border border-red-400/30"
            >
              <div className="flex items-start gap-3">
                <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-red-300">
                    {isWithdrawn ? 'Application Withdrawn' : 'Application Not Selected'}
                  </p>
                  <p className="text-xs text-red-300/70 mt-1">
                    {isWithdrawn
                      ? 'You have withdrawn this application. No further updates will be made.'
                      : 'This application was not selected for the next stage.'}
                  </p>
                </div>
              </div>
            </motion.div>
          )}

          {/* Action Button */}
          {!isWithdrawn && !isRejected && application.status !== CandidateStatus.HIRED && (
            <div className="mt-6 flex justify-end">
              <WithdrawApplicationButton onClick={() => onWithdraw(application.id)} />
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
}
