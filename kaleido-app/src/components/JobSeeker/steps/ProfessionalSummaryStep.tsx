'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Briefcase,
  Code,
  FileText,
  Languages as LanguagesIcon,
  ChevronDown,
  User,
  Star,
  X,
} from 'lucide-react';
import { BaseStepProps } from '../types';
import { ModernCard } from '../components/ModernCard';
import { ModernTextArea } from '../components/ModernFormInput';

const tooltips = {
  summary: `Write a brief overview highlighting your key achievements, expertise, and career goals. Keep it concise and impactful.`,
  skills: `List your most relevant technical and professional skills (e.g., JavaScript, Project Management, Data Analysis)`,
  languages: `List languages with proficiency levels (e.g., English - Native, Spanish - Intermediate)`,
};

export const ProfessionalSummaryStep: React.FC<BaseStepProps> = ({ formData, onUpdate }) => {
  const [summaryExpanded, setSummaryExpanded] = useState(false);
  const [skillsExpanded, setSkillsExpanded] = useState(false);
  const [languagesExpanded, setLanguagesExpanded] = useState(false);
  const [newSkill, setNewSkill] = useState('');
  const [newLanguage, setNewLanguage] = useState('');

  const skills = formData.skills || [];
  const languages = formData.languages || [];
  const hasSummary = !!formData.summary?.trim();
  const hasSkills = skills.length > 0;
  const hasLanguages = languages.length > 0;

  const removeSkill = (skillToRemove: string) => {
    onUpdate({ skills: skills.filter(skill => skill !== skillToRemove) });
  };

  const addSkill = (skill: string) => {
    if (skill.trim() && !skills.includes(skill.trim())) {
      onUpdate({ skills: [...skills, skill.trim()] });
      setNewSkill('');
    }
  };

  const removeLanguage = (languageToRemove: string) => {
    onUpdate({ languages: languages.filter(lang => lang !== languageToRemove) });
  };

  const addLanguage = (language: string) => {
    if (language.trim() && !languages.includes(language.trim())) {
      onUpdate({ languages: [...languages, language.trim()] });
      setNewLanguage('');
    }
  };

  return (
    <div className="space-y-4">
      {/* Professional Summary Section */}
      <ModernCard className="overflow-visible">
        <button
          onClick={() => setSummaryExpanded(!summaryExpanded)}
          className="w-full flex items-center justify-between p-3 -m-4 mb-0 bg-gray-50 hover:bg-gray-100 transition-colors rounded-t-xl"
        >
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-900">Professional Summary</span>
            <span className="text-xs text-purple-400">Optional</span>
            {hasSummary && <span className="text-xs text-purple-600">✓</span>}
          </div>
          <motion.div
            animate={{ rotate: summaryExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="w-4 h-4 text-gray-500" />
          </motion.div>
        </button>

        <AnimatePresence>
          {summaryExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="pt-4"
            >
              <ModernTextArea
                label=""
                value={formData.summary || ''}
                onChange={value => onUpdate({ summary: value })}
                placeholder="Write a brief overview highlighting your key achievements, expertise, and career goals..."
                rows={4}
                maxLength={500}
                size="sm"
                hint={tooltips.summary}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </ModernCard>

      {/* Skills Section */}
      <ModernCard
        variant="required"
        title="Skills"
        required={true}
        completed={hasSkills}
        className="overflow-visible"
      >
        <button
          onClick={() => setSkillsExpanded(!skillsExpanded)}
          className="w-full flex items-center justify-between p-3 -m-4 mb-0 bg-gray-50 hover:bg-gray-100 transition-colors rounded-b-xl"
        >
          <div className="flex items-center gap-2">
            {hasSkills && <span className="text-xs text-purple-600">✓ {skills.length} added</span>}
          </div>
          <motion.div animate={{ rotate: skillsExpanded ? 180 : 0 }} transition={{ duration: 0.2 }}>
            <ChevronDown className="w-4 h-4 text-gray-500" />
          </motion.div>
        </button>

        <AnimatePresence>
          {skillsExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="pt-4"
            >
              <div className="space-y-3">
                {/* Skills Display */}
                {skills.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {skills.map((skill: string, index: number) => (
                      <motion.span
                        key={index}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="inline-flex items-center gap-1.5 px-3 py-1.5 bg-purple-100 text-purple-700 rounded-full text-sm"
                      >
                        {skill}
                        <button
                          onClick={() => removeSkill(skill)}
                          className="hover:bg-purple-200 rounded-full p-0.5"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </motion.span>
                    ))}
                  </div>
                )}

                {/* Add Skills Input */}
                <input
                  type="text"
                  value={newSkill}
                  onChange={e => setNewSkill(e.target.value)}
                  placeholder="Type a skill and press Enter"
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  onKeyPress={e => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addSkill(newSkill);
                    }
                  }}
                />

                {!hasSkills && <p className="text-xs text-amber-600">Add at least 1 skill</p>}

                <p className="text-xs text-gray-500">{tooltips.skills}</p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </ModernCard>

      {/* Languages Section */}
      <ModernCard className="overflow-visible">
        <button
          onClick={() => setLanguagesExpanded(!languagesExpanded)}
          className="w-full flex items-center justify-between p-3 -m-4 mb-0 bg-gray-50 hover:bg-gray-100 transition-colors rounded-t-xl"
        >
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-900">Languages</span>
            <span className="text-xs text-purple-400">Optional</span>
            {hasLanguages && (
              <span className="text-xs text-purple-600">✓ {languages.length} added</span>
            )}
          </div>
          <motion.div
            animate={{ rotate: languagesExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="w-4 h-4 text-gray-500" />
          </motion.div>
        </button>

        <AnimatePresence>
          {languagesExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="pt-4"
            >
              <div className="space-y-3">
                {/* Languages Display */}
                {languages.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {languages.map((language: string, index: number) => (
                      <motion.span
                        key={index}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="inline-flex items-center gap-1.5 px-3 py-1.5 bg-cyan-100 text-cyan-700 rounded-full text-sm"
                      >
                        {language}
                        <button
                          onClick={() => removeLanguage(language)}
                          className="hover:bg-cyan-200 rounded-full p-0.5"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </motion.span>
                    ))}
                  </div>
                )}

                {/* Add Language Input */}
                <input
                  type="text"
                  value={newLanguage}
                  onChange={e => setNewLanguage(e.target.value)}
                  placeholder="Type a language with proficiency (e.g., English - Native) and press Enter"
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  onKeyPress={e => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      addLanguage(newLanguage);
                    }
                  }}
                />

                <p className="text-xs text-gray-500">{tooltips.languages}</p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </ModernCard>

      {/* Read-only View when all collapsed */}
      {!summaryExpanded && !skillsExpanded && !languagesExpanded && (
        <div className="mt-4 p-4 bg-gradient-to-br from-gray-50/50 to-white rounded-xl border border-gray-100">
          <div className="space-y-3">
            {/* Summary Preview */}
            {hasSummary && (
              <div className="flex items-start gap-3">
                <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-100 to-indigo-100 flex items-center justify-center flex-shrink-0">
                  <FileText className="w-5 h-5 text-purple-600" />
                </div>
                <div className="flex-1">
                  <p className="text-[10px] font-semibold text-purple-600 uppercase tracking-wider mb-1">
                    Professional Summary
                  </p>
                  <p className="text-sm text-gray-700 line-clamp-2">{formData.summary}</p>
                </div>
              </div>
            )}

            {/* Skills Preview */}
            {hasSkills && (
              <div className="flex items-start gap-3">
                <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-100 to-indigo-100 flex items-center justify-center flex-shrink-0">
                  <Code className="w-5 h-5 text-purple-600" />
                </div>
                <div className="flex-1">
                  <p className="text-[10px] font-semibold text-purple-600 uppercase tracking-wider mb-1">
                    Skills ({skills.length})
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {skills.slice(0, 5).map((skill: string, idx: number) => (
                      <span
                        key={idx}
                        className="px-2 py-0.5 bg-purple-50 text-purple-700 rounded-full text-xs"
                      >
                        {skill}
                      </span>
                    ))}
                    {skills.length > 5 && (
                      <span className="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">
                        +{skills.length - 5} more
                      </span>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Languages Preview */}
            {hasLanguages && (
              <div className="flex items-start gap-3">
                <div className="w-10 h-10 rounded-full bg-gradient-to-br from-cyan-100 to-blue-100 flex items-center justify-center flex-shrink-0">
                  <LanguagesIcon className="w-5 h-5 text-cyan-600" />
                </div>
                <div className="flex-1">
                  <p className="text-[10px] font-semibold text-cyan-600 uppercase tracking-wider mb-1">
                    Languages ({languages.length})
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {languages.map((language: string, idx: number) => (
                      <span
                        key={idx}
                        className="px-2 py-0.5 bg-cyan-50 text-cyan-700 rounded-full text-xs"
                      >
                        {language}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfessionalSummaryStep;
