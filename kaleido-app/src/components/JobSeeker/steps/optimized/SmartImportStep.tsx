'use client';

import { motion, AnimatePresence } from 'framer-motion';
import {
  FileUp,
  <PERSON>edin,
  SkipForward,
  Zap,
  Upload,
  Clock,
  ChevronDown,
  ChevronUp,
  Shield,
  Sparkles,
  CheckCircle,
} from 'lucide-react';
import React, { useState } from 'react';
import { BaseStepProps } from '../../types';
import { ResumeUploadStep } from '../ResumeUploadStep';
import { ModernCard, StatCard } from '../../components/ModernCard';
import { LinkedInImportSummary } from '../../components/LinkedInImportSummary';
import { ResumeImportSummary } from '../../components/ResumeImportSummary';
import useUser from '@/hooks/useUser';

interface SmartImportStepProps extends BaseStepProps {
  uploading?: boolean;
  setUploading?: (uploading: boolean) => void;
}

const SmartImportStep: React.FC<SmartImportStepProps> = props => {
  const { onNext, formData } = props;
  const { user } = useUser();
  const [activeTab, setActiveTab] = useState<'resume' | 'linkedin'>('resume');
  const [showImportedData, setShowImportedData] = useState(false);

  const hasResumeData = !!formData.resumeUrl;
  const hasLinkedInData = !!formData.linkedInProfile;
  const totalImported = (hasResumeData ? 1 : 0) + (hasLinkedInData ? 1 : 0);

  // Helper function to check if user has LinkedIn login
  const isLinkedInLogin = (user: any) => {
    return user?.sub?.startsWith('linkedin');
  };

  // Mock data for imported fields (replace with actual data)
  const importedFields = {
    resume: hasResumeData
      ? {
          name:
            props.formData.firstName && props.formData.lastName
              ? `${props.formData.firstName} ${props.formData.lastName}`
              : null,
          email: props.formData.email,
          skills: props.formData.skills?.length || 0,
          experience: props.formData.experience?.length || 0,
          education: props.formData.education?.length || 0,
        }
      : null,
    linkedin: hasLinkedInData
      ? {
          headline: props.formData.linkedInProfile?.headline,
          location: props.formData.location,
          connections: props.formData.linkedInProfile?.connections,
          skills: props.formData.skills?.length || 0,
          experience: props.formData.experience?.length || 0,
        }
      : null,
  };

  return (
    <div className="space-y-4">
      {/* Import Success Status - Compact */}
      {totalImported > 0 && (
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-2 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CheckCircle className="w-4 h-4 text-purple-400" />
            <span className="text-xs font-medium text-purple-700">
              {totalImported === 2
                ? 'Resume + LinkedIn imported'
                : hasResumeData
                  ? 'Resume imported'
                  : 'LinkedIn imported'}
            </span>
          </div>
          <span className="text-xs text-purple-600">
            {Math.round(((hasResumeData ? 10 : 0) + (hasLinkedInData ? 10 : 0)) / 2)} fields added
          </span>
        </div>
      )}

      {/* Simple Tab Interface */}
      <div className="bg-white border border-gray-100 rounded-lg overflow-hidden">
        {/* Tab Headers */}
        <div className="flex border-b border-gray-100">
          <button
            onClick={() => setActiveTab('resume')}
            className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 text-sm font-medium transition-all ${
              activeTab === 'resume'
                ? 'border-b-2 border-purple-400 text-purple-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <FileUp className="w-4 h-4" />
            Upload Resume
            {hasResumeData && <CheckCircle className="w-3 h-3 text-purple-400" />}
          </button>
          <button
            onClick={() => setActiveTab('linkedin')}
            className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 text-sm font-medium transition-all ${
              activeTab === 'linkedin'
                ? 'border-b-2 border-purple-400 text-purple-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <Linkedin className="w-4 h-4" />
            LinkedIn Import
            {hasLinkedInData && <CheckCircle className="w-3 h-3 text-purple-400" />}
          </button>
        </div>

        {/* Tab Content */}
        <div className="p-4">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
            >
              {activeTab === 'resume' ? (
                <div className="space-y-3">
                  {/* Resume Import Summary - Show if has resume data */}
                  {hasResumeData && (
                    <ResumeImportSummary
                      formData={formData}
                      resumeData={
                        formData.resumeData || {
                          firstName: formData.firstName,
                          lastName: formData.lastName,
                          email: formData.email,
                          phone: formData.phone,
                          location: formData.location,
                          skills: formData.skills,
                          experience: formData.experience,
                          education: formData.education,
                        }
                      }
                      jobSeekerId={formData.id}
                    />
                  )}

                  <ResumeUploadStep {...props} hideImportSummaries={true} />
                </div>
              ) : (
                <div className="space-y-3">
                  {/* LinkedIn Import Summary - Show if has LinkedIn data */}
                  {hasLinkedInData && isLinkedInLogin(user) && (
                    <LinkedInImportSummary
                      formData={formData}
                      linkedInData={{
                        ...formData.linkedInProfile,
                        metadata: formData.metadata,
                        firstName: formData.firstName,
                        lastName: formData.lastName,
                        email: formData.email,
                        location: formData.location,
                        profilePicture:
                          formData.myProfileImage || (formData.metadata as any)?.auth0?.picture,
                        skills: formData.skills,
                        positions: formData.experience,
                        educations: formData.education,
                        publicProfileUrl: formData.linkedinUrl,
                        name: `${formData.firstName} ${formData.lastName}`,
                        headline: formData.summary,
                        pictureUrl: formData.myProfileImage,
                      }}
                      jobSeekerId={formData.id}
                    />
                  )}

                  <div className="text-center py-4">
                    <motion.div
                      animate={{ scale: [1, 1.05, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    >
                      <Linkedin className="w-12 h-12 text-purple-400 mx-auto mb-3" />
                    </motion.div>
                    <h3 className="text-sm font-semibold text-gray-900 mb-1">Connect LinkedIn</h3>
                    <p className="text-xs text-gray-600 mb-4">Import your professional data</p>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm font-medium"
                      onClick={() => {
                        // TODO: Implement LinkedIn OAuth flow
                        console.log('LinkedIn import');
                      }}
                    >
                      Connect LinkedIn
                    </motion.button>
                  </div>
                </div>
              )}
            </motion.div>
          </AnimatePresence>
        </div>
      </div>

      {/* Skip Option */}
      <div className="text-center">
        <button
          onClick={onNext}
          className="text-xs text-gray-500 hover:text-gray-700 transition-colors"
        >
          Skip for now
        </button>
      </div>
    </div>
  );
};

export default SmartImportStep;
