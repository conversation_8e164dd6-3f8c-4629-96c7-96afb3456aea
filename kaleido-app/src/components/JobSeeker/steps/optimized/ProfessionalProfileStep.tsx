'use client';

import React from 'react';
import {
  Briefcase,
  GraduationCap,
  FileText,
  X,
  Sparkles,
  Star,
  TrendingUp,
  User,
  CheckCircle2,
} from 'lucide-react';
import { BaseStepProps } from '../../types';
import { EducationStep } from '../EducationStep';
import { ExperienceStep } from '../ExperienceStep';
import { ModernTextArea } from '../../components/ModernFormInput';
import { UnifiedStepLayout, FieldCard } from '../../components/UnifiedStepLayout';

const ProfessionalProfileStep: React.FC<BaseStepProps> = ({ formData, onUpdate, onNext }) => {
  // Ensure skills is always an array, even if undefined or null
  const skills = Array.isArray(formData.skills) ? formData.skills : [];

  // Section configuration
  const sectionConfig = {
    title: 'Build your professional profile',
    subtitle: 'Why this matters',
    icon: <Briefcase className="w-4 h-4" />,
    description: 'Employers focus 80% on experience',
    benefits: [
      { icon: <Star className="w-3.5 h-3.5" />, text: 'Skills boost views' },
      { icon: <TrendingUp className="w-3.5 h-3.5" />, text: 'Show career growth' },
    ],
    requiredFieldLabels: ['Skills'],
  };

  // Mandatory fields for this section
  const mandatoryFields = ['skills'];

  // Render read-only view
  const renderReadOnlyView = () => (
    <div className="bg-gradient-to-br from-white via-gray-50/30 to-white rounded-xl border border-gray-200 p-6 shadow-sm">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Professional Summary - Full Width */}
        <FieldCard
          label="PROFESSIONAL SUMMARY"
          value={formData.summary}
          icon={<User />}
          isEmpty={!formData.summary}
          colSpan={2}
        />

        {/* Skills - Full Width */}
        <FieldCard
          label="SKILLS"
          value={skills.length > 0 ? skills.join(', ') : null}
          icon={<Star />}
          required
          isEmpty={skills.length === 0}
          colSpan={2}
        />

        {/* Experience & Education - Same Row */}
        <FieldCard
          label="EXPERIENCE"
          value={
            formData.experience?.length
              ? `${formData.experience.length} position${formData.experience.length > 1 ? 's' : ''}`
              : null
          }
          icon={<Briefcase />}
          isEmpty={!formData.experience?.length}
        />
        <FieldCard
          label="EDUCATION"
          value={
            formData.education?.length
              ? `${formData.education.length} degree${formData.education.length > 1 ? 's' : ''}`
              : null
          }
          icon={<GraduationCap />}
          isEmpty={!formData.education?.length}
        />
      </div>
    </div>
  );

  // Render edit form
  const renderEditForm = () => {
    // Common skill suggestions based on the user's profile
    const skillSuggestions = [
      'JavaScript',
      'TypeScript',
      'React',
      'Node.js',
      'Python',
      'Java',
      'SQL',
      'AWS',
      'Docker',
      'Git',
      'Agile',
      'REST APIs',
    ].filter(suggestion => !skills.includes(suggestion));

    return (
      <div className="space-y-4">
        {/* Skills Section - Required */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">
            Skills <span className="text-purple-500">*</span>
          </h4>
          <div className="space-y-3">
            {/* Skills Display */}
            {skills.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {skills.map((skill: string, index: number) => (
                  <span
                    key={index}
                    className="inline-flex items-center gap-1.5 px-3 py-1.5 bg-purple-100 text-purple-700 rounded-full text-sm"
                  >
                    {skill}
                    <button
                      type="button"
                      onClick={() => onUpdate({ skills: skills.filter((_, i) => i !== index) })}
                      className="hover:bg-purple-200 rounded-full p-0.5 transition-colors"
                      aria-label={`Remove ${skill}`}
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </span>
                ))}
              </div>
            ) : (
              <div className="bg-gradient-to-br from-purple-50/80 via-indigo-50/50 to-pink-50/80 rounded-lg p-3 border border-purple-100/30">
                <div className="flex items-start gap-2">
                  <Sparkles className="w-4 h-4 text-purple-600 mt-0.5" />
                  <div>
                    <p className="text-xs font-medium text-purple-800">No skills added yet</p>
                    <p className="text-xs text-purple-700 mt-1">
                      Start typing below or click on suggested skills to add them
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Add Skills Input */}
            <input
              type="text"
              placeholder="Type a skill and press Enter (e.g., JavaScript, Project Management)"
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all"
              onKeyPress={e => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  const value = (e.target as HTMLInputElement).value.trim();
                  if (value && !skills.includes(value)) {
                    onUpdate({ skills: [...skills, value] });
                    (e.target as HTMLInputElement).value = '';
                  }
                }
              }}
            />

            {/* Skill Suggestions */}
            {skillSuggestions.length > 0 && skills.length < 3 && (
              <div>
                <p className="text-xs text-gray-600 mb-2">Quick add popular skills:</p>
                <div className="flex flex-wrap gap-2">
                  {skillSuggestions.slice(0, 8).map(suggestion => (
                    <button
                      key={suggestion}
                      type="button"
                      onClick={() => onUpdate({ skills: [...skills, suggestion] })}
                      className="px-3 py-1 text-xs bg-white border border-purple-200 text-purple-700 rounded-full hover:bg-gradient-to-br hover:from-purple-50/80 hover:via-indigo-50/50 hover:to-pink-50/80 hover:border-purple-300 transition-all"
                    >
                      + {suggestion}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {skills.length === 0 && (
              <div className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-purple-500 rounded-full animate-pulse"></div>
                <p className="text-xs text-purple-600 font-medium">
                  Add at least 1 skill to continue
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Professional Summary - Optional */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">
            Professional Summary <span className="text-xs text-gray-500">(Optional)</span>
          </h4>
          <ModernTextArea
            label=""
            value={formData.summary || ''}
            onChange={value => onUpdate({ summary: value })}
            placeholder="Brief summary of your professional background..."
            rows={4}
            maxLength={500}
            size="sm"
          />
        </div>

        {/* Work Experience - Optional */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">
            Work Experience <span className="text-xs text-gray-500">(Optional)</span>
          </h4>
          <ExperienceStep {...{ formData, onUpdate, onNext }} />
        </div>

        {/* Education - Optional */}
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-3">
            Education <span className="text-xs text-gray-500">(Optional)</span>
          </h4>
          <EducationStep {...{ formData, onUpdate, onNext }} />
        </div>
      </div>
    );
  };

  return (
    <UnifiedStepLayout
      formData={formData}
      sectionId="professional"
      sectionConfig={sectionConfig}
      mandatoryFields={mandatoryFields}
      renderReadOnlyView={renderReadOnlyView}
    >
      {renderEditForm()}
    </UnifiedStepLayout>
  );
};

export default ProfessionalProfileStep;
