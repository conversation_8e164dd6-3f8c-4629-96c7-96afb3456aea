import { Building, Calendar, GraduationCap, Pencil, Plus, Trash2 } from 'lucide-react';
import React, { useState } from 'react';

import { BaseStepProps } from '../types';

import { ArrayFieldPagination } from '@/components/common/ArrayFieldPagination';
import { Button } from '@/components/ui/button';
import { ModernFormInput, ModernTextArea } from '../components/ModernFormInput';
import { Education as SharedEducation } from '@/shared/types';

const tooltips = {
  field: `Specify your major or field of study. For multiple majors, list the primary one here`,
  gpa: `Enter your GPA on a 4.0 scale. Leave empty if not applicable or prefer not to share`,
  achievements: `List notable academic achievements, honors, or relevant coursework (e.g., Dean's List, Research Projects)`,
};

interface EducationItemProps {
  edu: SharedEducation;
  index: number;
  startIndex: number;
  onEdit: () => void;
  onRemove: () => void;
}

function EducationViewItem({ edu, index, onEdit, onRemove }: EducationItemProps) {
  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
  };

  return (
    <div className="p-4 rounded-lg bg-white/50 border border-gray-200 shadow-sm relative group">
      <div className="absolute right-3 top-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <button
          onClick={onEdit}
          className="p-1 rounded-full bg-purple-50 text-purple-500 hover:bg-purple-100"
          aria-label="Edit education"
        >
          <Pencil className="w-4 h-4" />
        </button>
        <button
          onClick={onRemove}
          className="p-1 rounded-full bg-red-50 text-red-500 hover:bg-red-100"
          aria-label="Remove education"
        >
          <Trash2 className="w-4 h-4" />
        </button>
      </div>

      <div className="flex items-start gap-3">
        <div className="p-2 bg-gray-100 rounded">
          <GraduationCap className="w-5 h-5 text-gray-600" />
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-gray-900">{edu.degree}</h4>
          <p className="text-sm text-gray-600">{edu.institution}</p>
          <div className="flex items-center gap-2 mt-1 text-xs text-gray-500">
            <Building className="w-3 h-3" />
            <span>{edu.field}</span>
            <span>•</span>
            <Calendar className="w-3 h-3" />
            <span>
              {edu.startDate ? formatDate(edu.startDate.toString()) : 'N/A'} -{' '}
              {edu.endDate ? formatDate(edu.endDate.toString()) : 'Present'}
            </span>
            {edu.gpa && (
              <>
                <span>•</span>
                <span>GPA: {edu.gpa}</span>
              </>
            )}
          </div>
          {edu.description && <p className="mt-2 text-sm text-gray-600">{edu.description}</p>}
          {edu.achievements?.length > 0 && (
            <div className="mt-2 flex flex-wrap gap-2">
              {edu.achievements.map((achievement, i) => (
                <span
                  key={i}
                  className="text-xs bg-purple-50 text-purple-700 px-2 py-1 rounded-full"
                >
                  {achievement}
                </span>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export const EducationStep: React.FC<BaseStepProps> = ({ formData, onUpdate }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const itemsPerPage = 4;

  const handleEducationChange = (index: number, field: string, value: any) => {
    const newEducation = [...(formData.education || [])];
    newEducation[index] = {
      ...newEducation[index],
      [field]: value,
    };
    const update = { education: newEducation };
    onUpdate(update);
  };

  const addEducation = () => {
    const newEducation: SharedEducation = {
      institution: '',
      degree: '',
      field: '',
      startDate: new Date(),
      endDate: null,
      achievements: [],
    };
    const update = {
      education: [...(formData.education || []), newEducation],
    };
    onUpdate(update);
    setEditingIndex(formData.education?.length || 0);
  };

  const removeEducation = (index: number) => {
    const update = {
      education: formData.education?.filter((_, i) => i !== index) || [],
    };
    onUpdate(update);
  };

  const totalPages = Math.ceil((formData.education?.length || 0) / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const visibleEducation = formData.education?.slice(startIndex, startIndex + itemsPerPage) || [];

  return (
    <div className="space-y-6 md:w-full mx-auto">
      {editingIndex !== null ? (
        // Edit Mode - Show full form for single education entry
        <div className="p-6 rounded-xl bg-white/50 border border-gray-200 shadow-sm space-y-6">
          {/* Institution and Degree */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ModernFormInput
              label="Institution"
              value={formData.education[editingIndex].institution}
              onChange={value => handleEducationChange(editingIndex, 'institution', value)}
              placeholder="e.g. University of Oxford"
              prefix={<Building className="w-3.5 h-3.5" />}
              size="sm"
            />
            <ModernFormInput
              label="Degree"
              value={formData.education[editingIndex].degree}
              onChange={value => handleEducationChange(editingIndex, 'degree', value)}
              placeholder="e.g. Bachelor of Science"
              prefix={<GraduationCap className="w-3.5 h-3.5" />}
              size="sm"
            />
          </div>

          {/* Field and GPA */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ModernFormInput
              label="Field of Study"
              value={formData.education[editingIndex].field}
              onChange={value => handleEducationChange(editingIndex, 'field', value)}
              placeholder="e.g. Computer Science"
              hint={tooltips.field}
              prefix={<Building className="w-3.5 h-3.5" />}
              size="sm"
            />
            <ModernFormInput
              label="GPA"
              value={formData.education[editingIndex].gpa?.toString() || ''}
              onChange={value =>
                handleEducationChange(editingIndex, 'gpa', value ? parseFloat(value) : null)
              }
              placeholder="e.g. 3.8"
              hint={tooltips.gpa}
              prefix={<GraduationCap className="w-3.5 h-3.5" />}
              size="sm"
            />
          </div>

          {/* Dates */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ModernFormInput
              label="Start Date"
              type="text"
              value={
                formData.education[editingIndex].startDate
                  ? new Date(formData.education[editingIndex].startDate).toISOString().split('T')[0]
                  : ''
              }
              onChange={value =>
                handleEducationChange(editingIndex, 'startDate', value ? new Date(value) : null)
              }
              placeholder="YYYY-MM-DD"
              prefix={<Calendar className="w-3.5 h-3.5" />}
              size="sm"
            />
            <ModernFormInput
              label="End Date"
              type="text"
              value={
                formData.education[editingIndex].endDate
                  ? new Date(formData.education[editingIndex].endDate).toISOString().split('T')[0]
                  : ''
              }
              onChange={value =>
                handleEducationChange(editingIndex, 'endDate', value ? new Date(value) : null)
              }
              placeholder="YYYY-MM-DD or leave empty for current"
              prefix={<Calendar className="w-3.5 h-3.5" />}
              size="sm"
            />
          </div>

          {/* Description and Achievements */}
          <div className="space-y-4">
            <ModernTextArea
              label="Description"
              value={formData.education[editingIndex].description || ''}
              onChange={value => handleEducationChange(editingIndex, 'description', value)}
              placeholder="Brief description of your studies"
              rows={3}
              size="sm"
            />
            <ModernFormInput
              label="Achievements"
              value={formData.education[editingIndex].achievements?.join(', ') || ''}
              onChange={value =>
                handleEducationChange(
                  editingIndex,
                  'achievements',
                  value.split(',').map(item => item.trim())
                )
              }
              placeholder="List your academic achievements (comma-separated)"
              hint={tooltips.achievements}
              prefix={<GraduationCap className="w-3.5 h-3.5" />}
              size="sm"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={() => setEditingIndex(null)}
              className="px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50 transition-all text-sm font-medium"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={() => setEditingIndex(null)}
              className="px-4 py-2 rounded-lg bg-gradient-to-r from-purple-600 to-pink-600 text-white hover:from-purple-700 hover:to-pink-700 transition-all text-sm font-medium shadow-sm hover:shadow-md"
            >
              Save Changes
            </button>
          </div>
        </div>
      ) : (
        // View Mode - Show list of education entries
        <div className="space-y-4">
          {visibleEducation.map((edu, index) => (
            <EducationViewItem
              key={startIndex + index}
              edu={edu}
              index={index}
              startIndex={startIndex}
              onEdit={() => setEditingIndex(startIndex + index)}
              onRemove={() => removeEducation(startIndex + index)}
            />
          ))}

          <ArrayFieldPagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
          />

          <button
            type="button"
            onClick={addEducation}
            className="w-full flex items-center justify-center gap-2 py-3 px-4 rounded-lg bg-gradient-to-br from-purple-50/80 via-indigo-50/50 to-pink-50/80 border border-purple-200 text-purple-700 hover:from-purple-100 hover:via-indigo-100 hover:to-pink-100 hover:border-purple-300 transition-all text-sm font-medium"
          >
            <Plus className="w-4 h-4" />
            Add Education
          </button>
        </div>
      )}
    </div>
  );
};
