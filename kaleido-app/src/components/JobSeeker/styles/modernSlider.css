/* Modern JobSeeker Setup Slider Styles */

/* Container and Layout */
.modern-slider-container {
  @apply fixed inset-0 z-50 flex bg-white;
}

.modern-slider-left {
  @apply w-full lg:w-1/2 flex flex-col bg-white;
  background: linear-gradient(180deg, #ffffff 0%, #f9fafb 100%);
}

.modern-slider-right {
  @apply hidden lg:flex w-1/2 relative overflow-hidden;
}

/* Header Styles */
.modern-slider-header {
  @apply flex items-center justify-between p-6 lg:p-8 border-b border-gray-100;
}

.modern-slider-back-btn {
  @apply flex items-center text-gray-600 hover:text-gray-900 transition-all duration-200;
  @apply hover:translate-x-[-2px];
}

.modern-slider-close-btn {
  @apply p-2 rounded-full text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-all duration-200;
}

/* Progress Bar */
.modern-progress-container {
  @apply mb-8;
}

.modern-progress-bar {
  @apply h-1 bg-gray-200 rounded-full overflow-hidden;
  background: linear-gradient(90deg, #e5e7eb 0%, #f3f4f6 100%);
}

.modern-progress-fill {
  @apply h-full rounded-full transition-all duration-500 ease-out;
  background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%);
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
}

/* Content Area */
.modern-slider-content {
  @apply flex-1 overflow-y-auto px-6 lg:px-12 pb-8;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
}

.modern-slider-content::-webkit-scrollbar {
  width: 6px;
}

.modern-slider-content::-webkit-scrollbar-track {
  background: transparent;
}

.modern-slider-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.modern-slider-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}

/* Form Container */
.modern-form-container {
  @apply max-w-md mx-auto py-8;
}

/* Title and Subtitle */
.modern-form-title {
  @apply text-3xl lg:text-4xl font-bold text-gray-900 mb-3;
  background: linear-gradient(135deg, #1f2937 0%, #4b5563 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modern-form-subtitle {
  @apply text-lg text-gray-600 mb-8;
  line-height: 1.6;
}

/* Input Fields */
.modern-input-group {
  @apply space-y-6;
}

.modern-input-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.modern-input-field {
  @apply w-full px-4 py-3 border border-gray-300 rounded-xl;
  @apply focus:ring-2 focus:ring-indigo-500 focus:border-transparent;
  @apply transition-all duration-200 bg-white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.modern-input-field:hover {
  border-color: #d1d5db;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.modern-input-field:focus {
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.modern-input-error {
  @apply border-red-300 focus:ring-red-500;
}

.modern-error-message {
  @apply mt-2 text-sm text-red-600 flex items-center;
}

/* Footer */
.modern-slider-footer {
  @apply p-6 lg:p-8 border-t border-gray-100 bg-white;
}

.modern-continue-btn {
  @apply w-full px-6 py-3 font-medium rounded-xl transition-all duration-200;
  @apply bg-gradient-to-r from-indigo-600 to-purple-600 text-white;
  @apply hover:from-indigo-700 hover:to-purple-700;
  @apply disabled:from-gray-300 disabled:to-gray-300 disabled:cursor-not-allowed;
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.2);
}

.modern-continue-btn:hover:not(:disabled) {
  box-shadow: 0 6px 25px rgba(99, 102, 241, 0.3);
  transform: translateY(-1px);
}

.modern-continue-btn:active:not(:disabled) {
  transform: translateY(0);
}

/* Right Panel - Image Section */
.modern-image-container {
  @apply absolute inset-0;
}

.modern-image-overlay {
  @apply absolute inset-0;
  background: linear-gradient(
    180deg,
    transparent 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.4) 100%
  );
}

/* Testimonial Card */
.modern-testimonial-card {
  @apply absolute bottom-8 left-8 right-8 lg:bottom-12 lg:left-12 lg:right-12;
  @apply bg-white/95 backdrop-blur-xl rounded-2xl p-6 lg:p-8;
  @apply shadow-2xl border border-white/50;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
}

.modern-testimonial-content {
  @apply flex items-start space-x-4;
}

.modern-testimonial-avatar {
  @apply flex-shrink-0 w-12 h-12 lg:w-14 lg:h-14 rounded-full overflow-hidden;
  @apply bg-gradient-to-br from-indigo-100 to-purple-100;
  @apply flex items-center justify-center;
}

.modern-testimonial-text {
  @apply flex-1;
}

.modern-testimonial-quote {
  @apply text-gray-700 text-base lg:text-lg leading-relaxed mb-3;
  @apply italic;
}

.modern-testimonial-author {
  @apply text-sm font-semibold text-gray-900;
}

/* Fun Fact Card - Alternative to testimonial */
.modern-funfact-card {
  @apply absolute top-1/2 right-8 transform -translate-y-1/2;
  @apply w-full max-w-md lg:max-w-lg;
  @apply bg-white/20 backdrop-blur-2xl rounded-2xl p-6 lg:p-8;
  @apply border border-white/30 shadow-2xl;
}

.modern-funfact-icon {
  @apply w-14 h-14 lg:w-16 lg:h-16 rounded-full;
  @apply bg-gradient-to-br from-yellow-300 to-orange-500;
  @apply flex items-center justify-center mb-4;
  @apply shadow-lg shadow-yellow-400/30;
}

.modern-funfact-title {
  @apply text-white text-xl lg:text-2xl font-bold mb-3;
  @apply drop-shadow-lg;
}

.modern-funfact-text {
  @apply text-white/95 text-base lg:text-lg leading-relaxed;
  @apply drop-shadow-md font-medium;
}

/* Animations */
@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modern-animate-slide-in {
  animation: slideInFromRight 0.5s ease-out;
}

.modern-animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .modern-slider-left {
    @apply w-full;
  }

  .modern-form-container {
    @apply max-w-full px-4;
  }

  .modern-slider-header {
    @apply p-4;
  }

  .modern-slider-content {
    @apply px-4;
  }

  .modern-slider-footer {
    @apply p-4;
  }
}

/* Loading State */
.modern-loading-spinner {
  @apply inline-block w-5 h-5 border-2 border-white border-t-transparent rounded-full;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Step Indicator */
.modern-step-indicator {
  @apply flex items-center justify-center space-x-2 text-sm text-gray-500;
}

.modern-step-dot {
  @apply w-2 h-2 rounded-full bg-gray-300 transition-all duration-300;
}

.modern-step-dot-active {
  @apply bg-indigo-600 w-8;
}

/* Validation States */
.modern-validation-success {
  @apply border-green-500 focus:ring-green-500;
}

.modern-validation-warning {
  @apply border-yellow-500 focus:ring-yellow-500;
}

/* Optional Step Badge */
.modern-optional-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  @apply bg-gray-100 text-gray-600;
}

/* Completion Animation */
.modern-completion-check {
  @apply w-20 h-20 mx-auto mb-6;
  @apply bg-gradient-to-br from-green-400 to-green-600;
  @apply rounded-full flex items-center justify-center;
  @apply shadow-xl shadow-green-500/30;
  animation: scaleIn 0.5s ease-out;
}

@keyframes scaleIn {
  from {
    transform: scale(0);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
