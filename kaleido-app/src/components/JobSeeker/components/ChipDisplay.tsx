'use client';

import React from 'react';
import { X } from 'lucide-react';

interface ChipProps {
  value: string;
  onRemove?: () => void;
  removable?: boolean;
  size?: 'xs' | 'sm' | 'md';
}

export const Chip: React.FC<ChipProps> = ({ value, onRemove, removable = false, size = 'xs' }) => {
  const sizeClasses = {
    xs: 'px-2 py-0.5 text-xs',
    sm: 'px-2.5 py-1 text-sm',
    md: 'px-3 py-1.5 text-base',
  };

  return (
    <span
      className={`inline-flex items-center gap-1 bg-purple-50 text-gray-700 rounded-full ${sizeClasses[size]}`}
    >
      {value}
      {removable && onRemove && (
        <button
          onClick={onRemove}
          className="hover:bg-purple-100 rounded-full p-0.5 transition-colors"
          aria-label={`Remove ${value}`}
        >
          <X className="w-3 h-3 text-gray-500" />
        </button>
      )}
    </span>
  );
};

interface ChipGroupProps {
  values: string[];
  onRemove?: (value: string) => void;
  removable?: boolean;
  maxDisplay?: number;
  size?: 'xs' | 'sm' | 'md';
  emptyMessage?: string;
}

export const ChipGroup: React.FC<ChipGroupProps> = ({
  values,
  onRemove,
  removable = false,
  maxDisplay = 5,
  size = 'xs',
  emptyMessage = '-',
}) => {
  if (!values || values.length === 0) {
    return <span className="text-sm text-gray-400 italic">{emptyMessage}</span>;
  }

  const displayValues = values.slice(0, maxDisplay);
  const remaining = values.length - maxDisplay;

  return (
    <div className="flex flex-wrap gap-1">
      {displayValues.map((value, idx) => (
        <Chip
          key={`${value}-${idx}`}
          value={value}
          onRemove={removable && onRemove ? () => onRemove(value) : undefined}
          removable={removable}
          size={size}
        />
      ))}
      {remaining > 0 && (
        <span
          className={`inline-flex items-center px-2 py-0.5 bg-gray-100 text-gray-700 rounded-full text-xs font-medium`}
        >
          +{remaining} more
        </span>
      )}
    </div>
  );
};

export default ChipGroup;
