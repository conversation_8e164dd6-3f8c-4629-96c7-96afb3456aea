'use client';

import React from 'react';
import { Info, AlertCircle, CheckCircle } from 'lucide-react';

interface MandatoryFieldsGuideProps {
  title: string;
  mandatoryFields: string[];
  optionalFields?: string[];
  completedMandatory?: string[];
  tips?: string[];
  variant?: 'info' | 'warning' | 'success';
}

export const MandatoryFieldsGuide: React.FC<MandatoryFieldsGuideProps> = ({
  title,
  mandatoryFields,
  optionalFields = [],
  completedMandatory = [],
  tips = [],
  variant = 'info',
}) => {
  const allMandatoryComplete = mandatoryFields.every(field => completedMandatory.includes(field));

  const getIcon = () => {
    if (variant === 'warning') return <AlertCircle className="w-4 h-4 text-amber-600" />;
    if (variant === 'success' || allMandatoryComplete)
      return <CheckCircle className="w-4 h-4 text-purple-600" />;
    return <Info className="w-4 h-4 text-purple-600" />;
  };

  const getBgColor = () => {
    if (variant === 'warning') return 'bg-amber-50';
    if (variant === 'success' || allMandatoryComplete) return 'bg-purple-50';
    return 'bg-purple-50';
  };

  const getTextColor = () => {
    if (variant === 'warning') return 'text-amber-700';
    if (variant === 'success' || allMandatoryComplete) return 'text-purple-700';
    return 'text-purple-700';
  };

  return (
    <div className={`rounded-lg p-3 ${getBgColor()} mb-4`}>
      <div className="flex items-start gap-2">
        <div className="mt-0.5 flex-shrink-0">{getIcon()}</div>
        <div className="flex-1 space-y-2">
          <h4 className={`text-sm font-semibold ${getTextColor()}`}>{title}</h4>

          {mandatoryFields.length > 0 && (
            <div>
              <p className={`text-xs font-medium ${getTextColor()} mb-1`}>
                Required fields {allMandatoryComplete && '✓ All complete!'}
              </p>
              <div className="flex flex-wrap gap-1">
                {mandatoryFields.map((field, idx) => {
                  const isComplete = completedMandatory.includes(field);
                  return (
                    <span
                      key={idx}
                      className={`inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-xs ${
                        isComplete
                          ? 'bg-purple-50 text-gray-700'
                          : 'bg-white/70 text-gray-600 border border-gray-200'
                      }`}
                    >
                      {isComplete && <CheckCircle className="w-3 h-3 text-purple-500" />}
                      {field}
                    </span>
                  );
                })}
              </div>
            </div>
          )}

          {optionalFields.length > 0 && (
            <div>
              <p className={`text-xs font-medium ${getTextColor()} mb-1`}>
                Optional fields (enhance your profile)
              </p>
              <div className="flex flex-wrap gap-1">
                {optionalFields.map((field, idx) => (
                  <span
                    key={idx}
                    className="inline-flex items-center gap-1 px-2 py-0.5 bg-purple-50/50 text-gray-700 rounded-full text-xs"
                  >
                    {field}
                  </span>
                ))}
              </div>
            </div>
          )}

          {tips.length > 0 && (
            <div className="space-y-1 mt-2">
              {tips.map((tip, idx) => (
                <p key={idx} className={`text-xs ${getTextColor()} flex items-start gap-1`}>
                  <span className="text-xs">💡</span>
                  <span>{tip}</span>
                </p>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MandatoryFieldsGuide;
