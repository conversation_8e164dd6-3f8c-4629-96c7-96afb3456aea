'use client';

import React from 'react';

interface ProgressCardProps {
  label: string;
  completed: number;
  total: number;
  percentage: number;
  variant?: 'primary' | 'secondary';
  className?: string;
}

export const ProgressCard: React.FC<ProgressCardProps> = ({
  label,
  completed,
  total,
  percentage,
  variant = 'primary',
  className = '',
}) => {
  const isPrimary = variant === 'primary';

  return (
    <div
      className={`bg-gradient-to-br from-purple-50/80 via-indigo-50/50 to-pink-50/80 rounded-lg p-3 border border-purple-100/30 ${className}`}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <div
              className={`w-2 h-2 ${isPrimary ? 'bg-purple-500' : 'bg-purple-400/70'} rounded-full animate-pulse`}
            ></div>
            <p className="text-xs font-semibold text-gray-700">{label}</p>
          </div>
          <p className={`text-lg font-bold ${isPrimary ? 'text-purple-700' : 'text-purple-600'}`}>
            {completed}/{total}
          </p>
          <p className={`text-xs ${isPrimary ? 'text-purple-600' : 'text-purple-500'} mt-0.5`}>
            {percentage}% complete
          </p>
        </div>
        <div className="relative w-12 h-12">
          <svg className="w-12 h-12 transform -rotate-90" viewBox="0 0 36 36">
            <path
              d="m18,2.0845 a 15.9155,15.9155 0 0,1 0,31.831 a 15.9155,15.9155 0 0,1 0,-31.831"
              fill="none"
              stroke={isPrimary ? 'rgba(147, 51, 234, 0.2)' : 'rgba(168, 85, 247, 0.15)'}
              strokeWidth="3"
            />
            <path
              d="m18,2.0845 a 15.9155,15.9155 0 0,1 0,31.831 a 15.9155,15.9155 0 0,1 0,-31.831"
              fill="none"
              stroke={isPrimary ? 'rgb(147, 51, 234)' : 'rgb(168, 85, 247)'}
              strokeWidth="3"
              strokeLinecap="round"
              strokeDasharray={`${percentage}, 100`}
              className="transition-all duration-700 ease-out"
            />
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <span
              className={`text-xs font-bold ${isPrimary ? 'text-purple-700' : 'text-purple-600'}`}
            >
              {percentage}%
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgressCard;
