'use client';

import React from 'react';
import {
  Edit2,
  Check,
  AlertCircle,
  MapPin,
  Mail,
  Phone,
  Briefcase,
  GraduationCap,
  Globe,
  DollarSign,
  Calendar,
  Award,
  Languages,
  Shield,
  Github,
  Linkedin,
  Video,
  FolderOpen,
  User,
  Building,
  Target,
  Heart,
  Clock,
  Link,
  Info,
  Star,
  X,
  Eye,
  Sparkles,
  TrendingUp,
  Users,
  CheckCircle2,
} from 'lucide-react';
import { IJobSeekerProfile } from '../types';
import { ChipGroup, Chip } from './ChipDisplay';
import { ProfileReadOnlyView } from './ProfileReadOnlyView';

interface CompactProfileViewProps {
  formData: IJobSeekerProfile;
  sectionId: string;
  onEdit: () => void;
  onClose?: () => void;
  mandatoryFields?: string[];
  isEditMode?: boolean;
  children?: React.ReactNode; // Add children prop for edit form
}

interface FieldDisplayProps {
  label: string;
  value: any;
  icon?: React.ReactNode;
  required?: boolean;
  isEmpty?: boolean;
}

const FieldDisplay: React.FC<FieldDisplayProps> = ({ label, value, icon, required, isEmpty }) => {
  const displayValue = isEmpty ? '-' : value;

  return (
    <div className="flex items-start gap-2 py-2 border-b border-gray-50 last:border-0">
      {icon && <div className="w-5 h-5 mt-0.5 text-gray-400 flex-shrink-0">{icon}</div>}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-0.5">
          <p className="text-xs font-medium text-gray-500">{label}</p>
          {required && <span className="text-xs text-purple-500 font-semibold">*</span>}
        </div>
        <p className={`text-sm ${isEmpty ? 'text-gray-400 italic' : 'text-gray-900'} break-words`}>
          {displayValue}
        </p>
      </div>
    </div>
  );
};

const FieldChips: React.FC<{
  label: string;
  values: string[];
  icon?: React.ReactNode;
  required?: boolean;
  color?: string;
}> = ({ label, values, icon, required, color = 'purple' }) => {
  const isEmpty = !values || values.length === 0;

  return (
    <div className="py-2 border-b border-gray-50 last:border-0">
      <div className="flex items-center gap-2 mb-1.5">
        {icon && <div className="w-5 h-5 text-gray-400">{icon}</div>}
        <p className="text-xs font-medium text-gray-500">{label}</p>
        {required && <span className="text-xs text-purple-500 font-semibold">*</span>}
      </div>
      {isEmpty ? (
        <p className="text-sm text-gray-400 italic ml-7">-</p>
      ) : (
        <div className="flex flex-wrap gap-1 ml-7">
          {values.slice(0, 5).map((value, idx) => (
            <span
              key={idx}
              className={`px-2 py-0.5 bg-${color}-50 text-${color}-700 rounded-full text-xs`}
            >
              {value}
            </span>
          ))}
          {values.length > 5 && (
            <span className="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">
              +{values.length - 5} more
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export const CompactProfileView: React.FC<CompactProfileViewProps> = ({
  formData,
  sectionId,
  onEdit,
  onClose,
  mandatoryFields = [],
  isEditMode = false,
  children,
}) => {
  // Check if mandatory fields are missing
  const getMissingMandatoryFields = () => {
    return mandatoryFields.filter(field => {
      const fieldPath = field.split('.');
      let value: any = formData;

      for (const path of fieldPath) {
        value = value?.[path];
      }

      if (Array.isArray(value)) return value.length === 0;
      return !value;
    });
  };

  const missingFields = getMissingMandatoryFields();
  const shouldAutoEdit = missingFields.length > 0;

  // Get section title
  const getSectionTitle = () => {
    switch (sectionId) {
      case 'essentials':
        return 'Complete your essential information';
      case 'professional':
        return 'Build your professional profile';
      case 'preferences':
        return 'Define your job preferences';
      case 'additional':
        return 'Add additional details';
      case 'verification':
        return 'Verify your profile';
      case 'import':
        return 'Import your existing profile';
      default:
        return '';
    }
  };

  // Get advisory content based on section
  const getAdvisoryContent = () => {
    switch (sectionId) {
      case 'essentials':
        return {
          icon: <User className="w-4 h-4" />,
          title: 'Why this matters',
          description: 'Complete profiles receive 5x more interview requests',
          benefits: [
            { icon: <CheckCircle2 className="w-3.5 h-3.5" />, text: 'All fields completed' },
            { icon: <TrendingUp className="w-3.5 h-3.5" />, text: '40% more visibility' },
          ],
          requiredFields: ['First Name', 'Last Name', 'Email'],
        };
      case 'professional':
        return {
          icon: <Briefcase className="w-4 h-4" />,
          title: 'Why this matters',
          description: 'Employers focus 80% on experience',
          benefits: [
            { icon: <Star className="w-3.5 h-3.5" />, text: 'Skills boost views' },
            { icon: <TrendingUp className="w-3.5 h-3.5" />, text: 'Show career growth' },
          ],
          requiredFields: ['Skills'],
        };
      case 'preferences':
        return {
          icon: <Target className="w-4 h-4" />,
          title: 'Why this matters',
          description: '3x better job matches with clear preferences',
          benefits: [
            { icon: <DollarSign className="w-3.5 h-3.5" />, text: 'Salary clarity' },
            { icon: <Globe className="w-3.5 h-3.5" />, text: 'Remote options' },
          ],
          requiredFields: ['Job Types', 'Locations', 'Remote', 'Salary'],
        };
      case 'additional':
        return {
          icon: <Sparkles className="w-4 h-4" />,
          title: 'Stand out',
          description: 'Additional details improve matches',
          benefits: [
            { icon: <Languages className="w-3.5 h-3.5" />, text: 'Global opportunities' },
            { icon: <Award className="w-3.5 h-3.5" />, text: 'Validated expertise' },
          ],
          requiredFields: [],
        };
      case 'verification':
        return {
          icon: <Shield className="w-4 h-4" />,
          title: 'Build trust',
          description: 'Verified profiles get 3x responses',
          benefits: [
            { icon: <Video className="w-3.5 h-3.5" />, text: 'Video engagement' },
            { icon: <CheckCircle2 className="w-3.5 h-3.5" />, text: 'Instant credibility' },
          ],
          requiredFields: [],
        };
      default:
        return null;
    }
  };

  const advisory = getAdvisoryContent();
  const hasAllRequired = missingFields.length === 0;

  // Modern advisory section component
  const AdvisorySection = ({ showEdit = false }: { showEdit?: boolean }) => {
    if (!advisory) return null;

    return (
      <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-purple-50/80 via-indigo-50/50 to-pink-50/80 border border-purple-100/30 p-4">
        <div className="relative z-10">
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 rounded-full bg-white/90 backdrop-blur-sm flex items-center justify-center shadow-sm border border-purple-100/20">
              <div className="text-purple-600">{advisory.icon}</div>
            </div>
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <h3 className="text-sm font-semibold text-gray-900 mb-0.5">
                    {getSectionTitle()}
                  </h3>
                  <div className="flex items-center gap-2 mb-2">
                    <Info className="w-3 h-3 text-purple-500" />
                    <p className="text-xs text-gray-600">{advisory.title}</p>
                  </div>
                  <p className="text-xs text-gray-700 font-medium mb-2">{advisory.description}</p>

                  {/* Required fields indicator */}
                  {advisory.requiredFields.length > 0 && (
                    <div className="flex items-center gap-2 text-xs mb-2">
                      <span className="text-gray-600">Required:</span>
                      <div className="flex items-center gap-1">
                        {hasAllRequired ? (
                          <>
                            <CheckCircle2 className="w-3.5 h-3.5 text-purple-500" />
                            <span className="text-purple-600 font-medium">All complete!</span>
                          </>
                        ) : (
                          <span className="text-purple-600 font-medium">
                            {advisory.requiredFields.join(', ')}
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Benefits */}
                  <div className="flex flex-wrap gap-3">
                    {advisory.benefits.map((benefit, idx) => (
                      <div key={idx} className="flex items-center gap-1.5 text-xs">
                        <span className="text-purple-500">{benefit.icon}</span>
                        <span className="text-gray-700">{benefit.text}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Action buttons */}
                <div className="flex items-center gap-2">
                  {showEdit && (
                    <button
                      onClick={onEdit}
                      className="flex items-center gap-1.5 px-3 py-1.5 text-sm rounded-lg bg-white/90 border border-gray-200 hover:bg-white transition-all shadow-sm"
                      aria-label="Edit section"
                    >
                      <Edit2 className="w-3.5 h-3.5" />
                      <span>Edit</span>
                    </button>
                  )}
                  {!showEdit && onClose && (
                    <button
                      onClick={onClose}
                      className="flex items-center gap-1.5 px-3 py-1.5 text-sm rounded-lg bg-white/90 border border-gray-200 hover:bg-white transition-all shadow-sm"
                      aria-label="Close"
                    >
                      <X className="w-3.5 h-3.5" />
                      <span>Close</span>
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Decorative gradient orbs */}
        <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-purple-300/20 to-pink-300/20 rounded-full blur-2xl" />
        <div className="absolute -bottom-4 -left-4 w-20 h-20 bg-gradient-to-tr from-indigo-300/20 to-purple-300/20 rounded-full blur-2xl" />
      </div>
    );
  };

  // If we have children (edit form) and are in edit mode, show the children with wrapper
  if ((isEditMode || shouldAutoEdit) && children) {
    return (
      <div className="space-y-3">
        <AdvisorySection showEdit={false} />

        {/* Edit form content */}
        <div className="bg-white rounded-lg border border-gray-100">{children}</div>
      </div>
    );
  }

  // View mode sections
  const renderEssentialsSection = () => (
    <div className="space-y-4">
      <AdvisorySection showEdit={true} />
      <ProfileReadOnlyView formData={formData} />
    </div>
  );

  const renderProfessionalSection = () => (
    <div className="space-y-4">
      <AdvisorySection showEdit={true} />

      <div className="bg-gradient-to-br from-white via-gray-50/30 to-white rounded-xl border border-gray-200 p-6 shadow-sm">
        <div className="grid grid-cols-1 gap-4">
          {/* Professional Summary - Full Width */}
          {formData.summary && (
            <div className="bg-gradient-to-br from-gray-50/50 to-white rounded-xl p-4 border border-gray-100">
              <div className="flex items-start gap-3">
                <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-100 to-indigo-100 flex items-center justify-center flex-shrink-0">
                  <User className="w-5 h-5 text-purple-600" />
                </div>
                <div className="flex-1">
                  <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">
                    Professional Summary
                  </p>
                  <p className="text-sm text-gray-700 leading-relaxed">{formData.summary}</p>
                </div>
              </div>
            </div>
          )}

          {/* Skills - Full Width */}
          <FieldChips
            label="Skills"
            values={formData.skills || []}
            icon={<Star />}
            required
            color="purple"
          />

          {/* Experience & Education - Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FieldDisplay
              label="Experience"
              value={
                formData.experience?.length
                  ? `${formData.experience.length} position${formData.experience.length > 1 ? 's' : ''}`
                  : null
              }
              icon={<Briefcase />}
              isEmpty={!formData.experience?.length}
            />
            <FieldDisplay
              label="Education"
              value={
                formData.education?.length
                  ? `${formData.education.length} degree${formData.education.length > 1 ? 's' : ''}`
                  : null
              }
              icon={<GraduationCap />}
              isEmpty={!formData.education?.length}
            />
          </div>
        </div>
      </div>
    </div>
  );

  const renderPreferencesSection = () => {
    const formatSalary = () => {
      if (!formData.preferences?.desiredSalary?.min || !formData.preferences?.desiredSalary?.max) {
        return null;
      }
      const { min, max, currency = 'USD', period = 'yearly' } = formData.preferences.desiredSalary;
      return `${currency} ${min}-${max} ${period}`;
    };

    return (
      <div className="space-y-4">
        <AdvisorySection showEdit={true} />

        <div className="bg-white rounded-lg border border-gray-100 p-4">
          <div className="space-y-0">
            <FieldChips
              label="Job Types"
              values={formData.preferences?.jobTypes || []}
              icon={<Briefcase className="w-4 h-4" />}
              required
              color="blue"
            />
            <FieldChips
              label="Preferred Locations"
              values={formData.preferences?.locations || []}
              icon={<MapPin className="w-4 h-4" />}
              required
              color="green"
            />
            <FieldChips
              label="Industries"
              values={formData.preferences?.industries || []}
              icon={<Building className="w-4 h-4" />}
              color="indigo"
            />
            <FieldDisplay
              label="Remote Preference"
              value={formData.preferences?.remotePreference}
              icon={<Globe className="w-4 h-4" />}
              required
              isEmpty={!formData.preferences?.remotePreference}
            />
            <FieldDisplay
              label="Desired Salary"
              value={formatSalary()}
              icon={<DollarSign className="w-4 h-4" />}
              required
              isEmpty={!formatSalary()}
            />
          </div>
        </div>
      </div>
    );
  };

  const renderAdditionalSection = () => (
    <div className="space-y-4">
      <AdvisorySection showEdit={true} />

      <div className="bg-white rounded-lg border border-gray-100 p-4">
        <div className="space-y-0">
          <FieldChips
            label="Languages"
            values={formData.languages || []}
            icon={<Languages className="w-4 h-4" />}
            color="cyan"
          />
          <FieldDisplay
            label="Certifications"
            value={
              formData.certifications?.length
                ? `${formData.certifications.length} certification${formData.certifications.length > 1 ? 's' : ''}`
                : null
            }
            icon={<Award className="w-4 h-4" />}
            isEmpty={!formData.certifications?.length}
          />
          <FieldChips
            label="My Values"
            values={formData.myValues || []}
            icon={<Heart className="w-4 h-4" />}
            color="pink"
          />
          <FieldDisplay
            label="Work Availability"
            value={
              formData.workAvailability?.immediatelyAvailable
                ? 'Immediately Available'
                : formData.workAvailability?.noticePeriod
                  ? `${formData.workAvailability.noticePeriod} days notice`
                  : null
            }
            icon={<Clock className="w-4 h-4" />}
            isEmpty={!formData.workAvailability}
          />
        </div>
      </div>
    </div>
  );

  const renderVerificationSection = () => (
    <div className="space-y-4">
      <AdvisorySection showEdit={true} />

      <div className="bg-white rounded-lg border border-gray-100 p-4">
        <div className="space-y-0">
          <FieldDisplay
            label="Video Introduction"
            value={formData.videoIntroUrl ? 'Video uploaded' : null}
            icon={<Video className="w-4 h-4" />}
            isEmpty={!formData.videoIntroUrl}
          />
          <FieldDisplay
            label="Portfolio URL"
            value={formData.portfolioUrl}
            icon={<FolderOpen className="w-4 h-4" />}
            isEmpty={!formData.portfolioUrl}
          />
          <FieldDisplay
            label="GitHub Profile"
            value={formData.githubUrl}
            icon={<Github className="w-4 h-4" />}
            isEmpty={!formData.githubUrl}
          />
          <FieldDisplay
            label="LinkedIn Profile"
            value={formData.linkedinUrl}
            icon={<Linkedin className="w-4 h-4" />}
            isEmpty={!formData.linkedinUrl}
          />
        </div>
      </div>
    </div>
  );

  // Render based on section
  const renderSection = () => {
    switch (sectionId) {
      case 'essentials':
        return renderEssentialsSection();
      case 'professional':
        return renderProfessionalSection();
      case 'preferences':
        return renderPreferencesSection();
      case 'additional':
        return renderAdditionalSection();
      case 'verification':
        return renderVerificationSection();
      default:
        return null;
    }
  };

  return <div>{renderSection()}</div>;
};
