import {
  Award,
  Briefcase,
  Download,
  Edit,
  FileText,
  Github,
  Globe,
  GraduationCap,
  Languages,
  Linkedin,
  Mail,
  MapPin,
  Phone,
  Star,
  User,
  X,
} from 'lucide-react';
import { useEffect, useState } from 'react';

import { IJobSeekerProfile } from './types';
import { JobSeekerProfile } from '@/types/jobSeeker';
import JobSeekerProfileStandard from './JobSeekerProfileStandard';
import { JobSeekerSetupSlider } from './JobSeekerSetupSlider';
import { ProfileImageCard } from '../shared/ProfileImageCard';
import { ResumeGenerator } from '../shared/ResumeGenerator';
import { TabNavigation } from '../common/TabNavigation';
import { UserRole } from '@/types/roles';
import { apiClient } from '@/lib/apiHelper';
import { useJobsStore } from '@/stores/unifiedJobStore';
import { useRouter } from 'next/router';

interface ProfileTabsProps {
  profile: JobSeekerProfile;
}

interface PersonalInfoTabProps {
  profile: JobSeekerProfile;
  onEditProfile: () => void;
}

export function ProfileTabs({ profile }: ProfileTabsProps) {
  const router = useRouter();
  const { refreshJobs, invalidateJobsCache } = useJobsStore();
  const [activeTab, setActiveTab] = useState(() => {
    // Initialize from URL if available, otherwise default to 'personal-info'
    return (
      (typeof window !== 'undefined' && new URLSearchParams(window.location.search).get('tab')) ||
      'personal-info'
    );
  });
  const [isProfileViewModalOpen, setIsProfileViewModalOpen] = useState(false);
  const [showEditSlider, setShowEditSlider] = useState(false);

  // Map the profile's workAvailability to the standard format
  const standardProfile: JobSeekerProfile = {
    ...profile,
    workAvailability: profile.workAvailability ? profile.workAvailability : undefined,
  };

  // Convert JobSeekerProfile to IJobSeekerProfile format for the slider
  const convertToSliderFormat = (profile: JobSeekerProfile): Partial<IJobSeekerProfile> => {
    return {
      id: profile.id,
      userId: profile.userId,
      clientId: profile.userId, // Use userId as clientId if available
      firstName: profile.firstName || '',
      lastName: profile.lastName || '',
      email: profile.email || '',
      phone: profile.phone || '',
      location: profile.location || '',
      myProfileImage: profile.myProfileImage,
      summary: profile.summary,
      skills: profile.skills || [],
      experience: (profile.experience || []).map(exp => ({
        ...exp,
        current: false, // Add required field
      })) as any,
      resumeUrl: profile.resumeUrl,
      linkedinUrl: profile.linkedinUrl,
      githubUrl: profile.githubUrl,
      education: (profile.education || []).map(edu => ({
        ...edu,
        startDate: edu.startDate ? new Date(edu.startDate) : undefined,
        endDate: edu.endDate ? new Date(edu.endDate) : undefined,
      })) as any,
      languages: profile.languages || [],
      myValues: profile.myValues || [],
      portfolioUrl: profile.portfolioUrl,
      videoIntroUrl: profile.videoIntroUrl,
      achievements: profile.achievements || [],
      recommendations: profile.recommendations || [],
      workAvailability: profile.workAvailability
        ? ({
            ...profile.workAvailability,
            noticePeriod: profile.workAvailability.noticePeriod
              ? parseInt(profile.workAvailability.noticePeriod as string) || 0
              : undefined,
          } as any)
        : undefined,
      hasCompletedOnboarding: profile.hasCompletedOnboarding,
    };
  };

  const openProfileViewModal = () => {
    setIsProfileViewModalOpen(true);
  };

  const closeProfileViewModal = () => {
    setIsProfileViewModalOpen(false);
  };

  const handleEditProfile = () => {
    setShowEditSlider(true);
  };

  const handleSliderComplete = (_data: IJobSeekerProfile) => {
    setShowEditSlider(false);
    // Refresh data through Zustand stores instead of full page reload
    invalidateJobsCache();
    refreshJobs(true);

    // Clear any cached data
    if (typeof localStorage !== 'undefined') {
      const keys = Object.keys(localStorage);
      const cacheKeys = keys.filter(
        key => key.startsWith('api_cache_') || key.startsWith('recent_fetch_')
      );
      cacheKeys.forEach(key => localStorage.removeItem(key));
    }
  };

  const handleSliderClose = () => {
    setShowEditSlider(false);
  };

  const tabs = [
    {
      id: 'personal-info',
      label: 'Personal Info',
      icon: <User className="w-4 h-4" />,
      content: () => (
        <PersonalInfoTab profile={standardProfile} onEditProfile={handleEditProfile} />
      ),
    },
    {
      id: 'resume-generator',
      label: 'Resume Generator',
      icon: <Download className="w-4 h-4" />,
      content: () => <ResumeGenerator profile={standardProfile} userRole={UserRole.JOB_SEEKER} />,
    },
    {
      id: 'profile-views',
      label: 'Profile Views',
      icon: <FileText className="w-4 h-4" />,
      onClick: openProfileViewModal,
      content: () => (
        <div className="text-center py-8 text-white/60">
          Click to view your profile as others see it
        </div>
      ),
    },
  ];

  // Sync URL changes with active tab
  useEffect(() => {
    const tabFromUrl = router.query.tab as string;
    if (tabFromUrl && tabs.filter(tab => tab.id === tabFromUrl).length > 0) {
      setActiveTab(tabFromUrl);
    }
  }, [router.query.tab, tabs]);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    const selectedTab = tabs.filter(tab => tab.id === tabId)[0];
    if (selectedTab?.onClick) {
      selectedTab.onClick();
    }

    // Update URL with new tab
    const newQuery = { ...router.query, tab: tabId };
    router.push(
      {
        pathname: router.pathname,
        query: newQuery,
      },
      undefined,
      { shallow: true }
    );
  };

  const currentTab = tabs.filter(tab => tab.id === activeTab)[0] || tabs[0];

  return (
    <div className="w-full px-2 sm:px-0">
      {/* Tab Navigation - Without container */}
      <div className="flex items-center h-12 sm:h-14 mb-4">
        <TabNavigation
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={handleTabChange}
          variant="gradient"
          gradientColors={{
            from: 'from-purple-600',
            to: 'to-pink-600',
          }}
          updateUrl={true}
          queryParamName="tab"
        />
      </div>

      <div className="mt-4 rounded-2xl bg-gradient-to-br from-white/10 to-white/5 p-4 lg:p-6 backdrop-blur-xl border border-white/10 shadow-xl">
        {currentTab.content()}
      </div>

      {/* Profile View Modal */}
      {isProfileViewModalOpen && (
        <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center overflow-hidden">
          <div className="w-full h-full flex items-center justify-center">
            <button
              onClick={closeProfileViewModal}
              className="absolute top-4 right-4 z-20 bg-white/10 rounded-full p-2 hover:bg-white/20 transition-colors shadow-md text-white"
              aria-label="Close modal"
            >
              <X size={24} />
            </button>
            <div className="w-full h-full max-h-screen overflow-auto">
              <JobSeekerProfileStandard profile={standardProfile} onClose={closeProfileViewModal} />
            </div>
          </div>
        </div>
      )}

      {/* Job Seeker Setup Slider for editing */}
      {showEditSlider && (
        <JobSeekerSetupSlider
          onComplete={handleSliderComplete}
          onClose={handleSliderClose}
          initialData={convertToSliderFormat(standardProfile)}
          initialStep="basics"
        />
      )}
    </div>
  );
}

function PersonalInfoTab({ profile, onEditProfile }: PersonalInfoTabProps) {
  const [profileImage, setProfileImage] = useState<string | undefined>(profile.myProfileImage);

  const handleImageUpload = async (imageUrl: string) => {
    setProfileImage(imageUrl);

    // Update the profile in the backend
    try {
      if (!profile.userId) {
        // Use authenticated endpoint that doesn't require userId
        await apiClient.patch(`/job-seekers/profile`, {
          myProfileImage: imageUrl,
        });
      } else {
        // Use userId-specific endpoint
        await apiClient.patch(`/job-seekers/profile`, {
          myProfileImage: imageUrl,
        });
      }
    } catch (error) {
      console.error('Error updating profile image:', error);
      alert('Failed to update profile image. Please try again.');
    }
  };

  return (
    <div className="relative space-y-4 lg:space-y-6 text-white">
      {/* Absolute Edit Profile Button */}
      <button
        onClick={onEditProfile}
        className="absolute -top-6 right-0 z-20 flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 rounded-lg text-white font-medium transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-105"
      >
        <Edit className="w-4 h-4" />
        Edit Profile
      </button>

      {/* Main Profile Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Image and Basic Info - Left Column */}
        <div className="lg:col-span-1 space-y-6">
          {/* Profile Image Container */}
          <div className="w-full max-w-[280px] mx-auto">
            <ProfileImageCard
              userId={profile.userId || ''}
              userType={UserRole.JOB_SEEKER}
              currentImageUrl={profileImage}
              userName={`${profile.firstName} ${profile.lastName}`}
              onUploadComplete={handleImageUpload}
            />
          </div>

          {/* Quick Stats Card */}
          <div className="bg-white/2 rounded-xl p-6 border border-white/5 backdrop-blur-sm">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <Star className="w-5 h-5 text-pink-400" />
              Quick Stats
            </h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-white/70 text-sm">Experience</span>
                <span className="text-white font-medium">
                  {profile.experience?.length || 0} positions
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/70 text-sm">Skills</span>
                <span className="text-white font-medium">{profile.skills?.length || 0} skills</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/70 text-sm">Education</span>
                <span className="text-white font-medium">
                  {profile.education?.length || 0} degrees
                </span>
              </div>
              {profile.languages && profile.languages.length > 0 && (
                <div className="flex items-center justify-between">
                  <span className="text-white/70 text-sm">Languages</span>
                  <span className="text-white font-medium">
                    {profile.languages.length} languages
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Contact Information and Professional Details - Right Two Columns */}
        <div className="lg:col-span-2 space-y-6">
          {/* Contact Information Card */}
          <div className="bg-white/2 rounded-xl p-6 border border-white/5 backdrop-blur-sm">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <User className="w-5 h-5 text-pink-400" />
              Contact Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-3 rounded-lg hover:bg-white/5 transition-colors">
                  <div className="p-2 bg-pink-500/20 rounded-lg">
                    <User className="w-4 h-4 text-pink-400" />
                  </div>
                  <div>
                    <p className="text-xs text-white/60 uppercase tracking-wide">Full Name</p>
                    <p className="text-white font-medium">
                      {profile.firstName} {profile.lastName}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 rounded-lg hover:bg-white/5 transition-colors">
                  <div className="p-2 bg-pink-500/20 rounded-lg">
                    <Mail className="w-4 h-4 text-pink-400" />
                  </div>
                  <div>
                    <p className="text-xs text-white/60 uppercase tracking-wide">Email</p>
                    <p className="text-white font-medium break-all">{profile.email}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 rounded-lg hover:bg-white/5 transition-colors">
                  <div className="p-2 bg-pink-500/20 rounded-lg">
                    <Phone className="w-4 h-4 text-pink-400" />
                  </div>
                  <div>
                    <p className="text-xs text-white/60 uppercase tracking-wide">Phone</p>
                    <p className="text-white font-medium">{profile.phone || 'Not provided'}</p>
                  </div>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-center gap-3 p-3 rounded-lg hover:bg-white/5 transition-colors">
                  <div className="p-2 bg-pink-500/20 rounded-lg">
                    <MapPin className="w-4 h-4 text-pink-400" />
                  </div>
                  <div>
                    <p className="text-xs text-white/60 uppercase tracking-wide">Location</p>
                    <p className="text-white font-medium">{profile.location || 'Not specified'}</p>
                  </div>
                </div>
                {profile.linkedinUrl && (
                  <div className="flex items-center gap-3 p-3 rounded-lg hover:bg-white/5 transition-colors">
                    <div className="p-2 bg-pink-500/20 rounded-lg">
                      <Linkedin className="w-4 h-4 text-pink-400" />
                    </div>
                    <div>
                      <p className="text-xs text-white/60 uppercase tracking-wide">LinkedIn</p>
                      <a
                        href={profile.linkedinUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-pink-400 hover:text-pink-300 font-medium text-sm"
                      >
                        View Profile
                      </a>
                    </div>
                  </div>
                )}
                {profile.githubUrl && (
                  <div className="flex items-center gap-3 p-3 rounded-lg hover:bg-white/5 transition-colors">
                    <div className="p-2 bg-pink-500/20 rounded-lg">
                      <Github className="w-4 h-4 text-pink-400" />
                    </div>
                    <div>
                      <p className="text-xs text-white/60 uppercase tracking-wide">GitHub</p>
                      <a
                        href={profile.githubUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-pink-400 hover:text-pink-300 font-medium text-sm"
                      >
                        View Profile
                      </a>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Professional Summary */}
          {profile.summary && (
            <div className="bg-white/2 rounded-xl p-6 border border-white/5 backdrop-blur-sm">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <FileText className="w-5 h-5 text-pink-400" />
                Professional Summary
              </h3>
              <p className="text-white/80 leading-relaxed">{profile.summary}</p>
            </div>
          )}

          {/* Skills Section */}
          {profile.skills && profile.skills.length > 0 && (
            <div className="bg-white/2 rounded-xl p-6 border border-white/5 backdrop-blur-sm">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Star className="w-5 h-5 text-pink-400" />
                Skills
              </h3>
              <ul className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {profile.skills.slice(0, 12).map((skill, index) => (
                  <li key={index} className="flex items-center gap-2 text-white/80">
                    <div className="w-1.5 h-1.5 bg-pink-400 rounded-full flex-shrink-0"></div>
                    <span className="text-sm">{skill}</span>
                  </li>
                ))}
              </ul>
              {profile.skills.length > 12 && (
                <p className="text-white/60 text-sm mt-3">
                  +{profile.skills.length - 12} more skills
                </p>
              )}
            </div>
          )}

          {/* Experience Preview */}
          {profile.experience && profile.experience.length > 0 && (
            <div className="bg-white/2 rounded-xl p-6 border border-white/5 backdrop-blur-sm">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Briefcase className="w-5 h-5 text-pink-400" />
                Recent Experience
              </h3>
              <div className="space-y-4">
                {profile.experience.slice(0, 2).map((exp, index) => (
                  <div key={index} className="border-l-2 border-pink-400/30 pl-4">
                    <h4 className="text-white font-semibold">{exp.title}</h4>
                    <p className="text-pink-400 text-sm">{exp.company}</p>
                    <p className="text-white/60 text-sm mt-1">
                      {exp.startDate} - {exp.endDate || 'Present'}
                    </p>
                    {exp.description && (
                      <p className="text-white/70 text-sm mt-2 line-clamp-2">{exp.description}</p>
                    )}
                  </div>
                ))}
                {profile.experience.length > 2 && (
                  <p className="text-white/60 text-sm">
                    +{profile.experience.length - 2} more positions
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Education Preview */}
          {profile.education && profile.education.length > 0 && (
            <div className="bg-white/2 rounded-xl p-6 border border-white/5 backdrop-blur-sm">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <GraduationCap className="w-5 h-5 text-pink-400" />
                Education
              </h3>
              <div className="space-y-4">
                {profile.education.slice(0, 2).map((edu, index) => (
                  <div key={index} className="border-l-2 border-pink-400/30 pl-4">
                    <h4 className="text-white font-semibold">{edu.degree}</h4>
                    <p className="text-pink-400 text-sm">{edu.institution}</p>
                    {edu.field && <p className="text-white/60 text-sm">{edu.field}</p>}
                    <p className="text-white/60 text-sm mt-1">
                      {edu.startDate} - {edu.endDate}
                    </p>
                  </div>
                ))}
                {profile.education.length > 2 && (
                  <p className="text-white/60 text-sm">
                    +{profile.education.length - 2} more degrees
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Languages and Additional Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Languages */}
            {profile.languages && profile.languages.length > 0 && (
              <div className="bg-white/2 rounded-xl p-6 border border-white/5 backdrop-blur-sm">
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <Languages className="w-5 h-5 text-pink-400" />
                  Languages
                </h3>
                <div className="space-y-2">
                  {profile.languages.map((language, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-pink-400 rounded-full"></div>
                      <span className="text-white/80">{language}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Achievements */}
            {profile.achievements && profile.achievements.length > 0 && (
              <div className="bg-white/2 rounded-xl p-6 border border-white/5 backdrop-blur-sm">
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <Award className="w-5 h-5 text-pink-400" />
                  Achievements
                </h3>
                <div className="space-y-2">
                  {profile.achievements.slice(0, 3).map((achievement, index) => (
                    <div key={index} className="flex items-start gap-2">
                      <Award className="w-4 h-4 text-yellow-400 mt-0.5 flex-shrink-0" />
                      <span className="text-white/80 text-sm">{achievement}</span>
                    </div>
                  ))}
                  {profile.achievements.length > 3 && (
                    <p className="text-white/60 text-sm ml-6">
                      +{profile.achievements.length - 3} more achievements
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Portfolio Link */}
            {profile.portfolioUrl && (
              <div className="bg-white/2 rounded-xl p-6 border border-white/5 backdrop-blur-sm">
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <Globe className="w-5 h-5 text-pink-400" />
                  Portfolio
                </h3>
                <a
                  href={profile.portfolioUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 text-pink-400 hover:text-pink-300 font-medium transition-colors"
                >
                  <Globe className="w-4 h-4" />
                  View Portfolio
                </a>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
