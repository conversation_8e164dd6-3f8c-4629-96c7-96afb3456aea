import {
  Award,
  Briefcase,
  Calendar,
  ChevronRight,
  DollarSign,
  Edit2,
  FileText,
  Github,
  Globe,
  GraduationCap,
  Heart,
  Languages,
  Linkedin,
  Mail,
  MapPin,
  Phone,
  User,
  Wifi,
} from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/router';

import { useState } from 'react';

import { JobSeekerProfile } from '@/types/jobSeeker';

import { JobSeekerSetupSlider } from './JobSeekerSetupSlider';
import SectionHeader from '../ui/SectionHeader';
import { Button } from '../ui/button';
import { Separator } from '../ui/separator';

interface PersonalInfoTabProps {
  profile: JobSeekerProfile;
}

export function PersonalInfoTab({ profile }: PersonalInfoTabProps) {
  const profileData = profile;
  const router = useRouter();
  const [showSetupSlider, setShowSetupSlider] = useState(false);

  // Format date function
  const formatDate = (date: string | Date | null | undefined) => {
    if (!date) return 'Present';
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
  };

  return (
    <>
      <div className="space-y-6 text-white relative">
        {/* Profile Header with Consistent Design */}
        <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-purple-900/20 to-pink-900/20 backdrop-blur-md border border-white/10">
          {/* Edit Button */}
          <div className="absolute top-4 right-4 z-20">
            <Button
              onClick={() => setShowSetupSlider(true)}
              variant="ghost"
              size="sm"
              className="bg-white/10 hover:bg-white/20 text-white border border-white/20 transition-all duration-200 flex items-center gap-2"
            >
              <Edit2 className="w-4 h-4" />
              Edit Profile
            </Button>
          </div>

          {/* Content Container */}
          <div className="relative p-8 z-10">
            <div className="flex flex-col sm:flex-row items-center gap-6">
              {/* Profile Image */}
              <div className="w-32 h-32 flex-shrink-0 relative group">
                {profileData.myProfileImage ? (
                  <img
                    src={profileData.myProfileImage}
                    alt={`${profile.firstName} ${profile.lastName}`}
                    className="w-full h-full rounded-full object-cover border-4 border-white/30 shadow-2xl ring-4 ring-purple-500/20"
                    onError={e => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const parent = target.parentElement;
                      if (parent) {
                        parent.innerHTML = `<div class="w-full h-full rounded-full bg-gradient-to-br from-purple-600/40 to-pink-600/40 flex items-center justify-center border-4 border-white/30 shadow-2xl ring-4 ring-purple-500/20">
                        <svg class="h-16 w-16 text-white/70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                      </div>`;
                      }
                    }}
                  />
                ) : (
                  <div className="w-full h-full rounded-full bg-gradient-to-br from-purple-600/40 to-pink-600/40 flex items-center justify-center border-4 border-white/30 shadow-2xl ring-4 ring-purple-500/20">
                    <User className="h-16 w-16 text-white/70" />
                  </div>
                )}
              </div>

              {/* Profile Info */}
              <div className="flex-grow text-center sm:text-left">
                <h1 className="text-3xl font-bold text-white mb-2">
                  {profile.firstName} {profile.lastName}
                </h1>

                {/* Contact Info */}
                <div className="flex flex-wrap gap-4 items-center text-sm text-white/90 mt-3">
                  {profile.email && (
                    <div className="flex items-center gap-2">
                      <Mail className="w-4 h-4 text-purple-400" />
                      <span className="font-medium">{profile.email}</span>
                    </div>
                  )}
                  {profile.phone && (
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4 text-blue-400" />
                      <span className="font-medium">{profile.phone}</span>
                    </div>
                  )}
                  {profile.location && (
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-pink-400" />
                      <span className="font-medium">{profile.location}</span>
                    </div>
                  )}
                </div>

                {/* Social Links with proper icons */}
                {(profile.linkedinUrl || profile.githubUrl || profile.portfolioUrl) && (
                  <div className="flex gap-3 mt-4 justify-center sm:justify-start">
                    {profile.linkedinUrl && (
                      <a
                        href={profile.linkedinUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2.5 bg-blue-600/20 rounded-lg hover:bg-blue-600/30 transition-all duration-200 shadow-lg hover:shadow-blue-500/20 border border-blue-500/30"
                        aria-label="LinkedIn Profile"
                      >
                        <Linkedin className="w-5 h-5 text-blue-400" />
                      </a>
                    )}
                    {profile.githubUrl && (
                      <a
                        href={profile.githubUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2.5 bg-gray-700/30 rounded-lg hover:bg-gray-700/40 transition-all duration-200 shadow-lg hover:shadow-gray-500/20 border border-gray-500/30"
                        aria-label="GitHub Profile"
                      >
                        <Github className="w-5 h-5 text-gray-300" />
                      </a>
                    )}
                    {profile.portfolioUrl && (
                      <a
                        href={profile.portfolioUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2.5 bg-green-600/20 rounded-lg hover:bg-green-600/30 transition-all duration-200 shadow-lg hover:shadow-green-500/20 border border-green-500/30"
                        aria-label="Portfolio"
                      >
                        <Globe className="w-5 h-5 text-green-400" />
                      </a>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Availability Section */}
            {profileData.workAvailability && (
              <div className="mt-6 pt-6 border-t border-white/20">
                <div className="flex items-center gap-2 mb-3">
                  <Calendar className="w-5 h-5 text-pink-400" />
                  <span className="text-base font-semibold text-white">Work Availability</span>
                </div>
                <div className="flex flex-wrap gap-3">
                  {profileData.workAvailability.immediatelyAvailable && (
                    <div className="bg-green-500/20 text-green-300 px-4 py-2 rounded-lg flex items-center gap-2 border border-green-500/30">
                      <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
                      <span className="font-medium">Immediately Available</span>
                    </div>
                  )}
                  {profileData.workAvailability.noticePeriod &&
                    Number(profileData.workAvailability.noticePeriod) > 0 && (
                      <div className="bg-purple-500/20 text-purple-300 px-4 py-2 rounded-lg border border-purple-500/30">
                        <span className="font-medium">
                          Notice Period: {profileData.workAvailability.noticePeriod} days
                        </span>
                      </div>
                    )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className="space-y-6 px-1">
          {/* Summary Section */}
          {profileData.summary && (
            <div className="relative">
              <SectionHeader title="Summary" icon={FileText} iconClassName="text-pink-400" />
              <p className="text-white/80 text-sm leading-relaxed">{profileData.summary}</p>
            </div>
          )}

          {/* Skills Section */}
          {profileData.skills && profileData.skills.length > 0 && (
            <div className="relative">
              <SectionHeader title="Technical Skills" icon={Award} iconClassName="text-pink-400" />
              <ul className="grid grid-cols-2 md:grid-cols-3 gap-x-6 gap-y-2">
                {profileData.skills.map((skill, index) => (
                  <li key={index} className="flex items-center gap-2 text-white/90">
                    <ChevronRight className="w-4 h-4 text-pink-400 flex-shrink-0" />
                    <span>{skill}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Experience Section */}
          {profileData.experience && profileData.experience.length > 0 && (
            <div className="relative">
              <SectionHeader title="Experience" icon={Briefcase} iconClassName="text-pink-400" />
              <ul className="space-y-4">
                {profileData.experience.map((exp, index) => (
                  <li key={index} className="pb-4">
                    {index !== profileData.experience.length - 1 && (
                      <Separator className="bg-white/10 mt-4" />
                    )}
                    <div className="flex justify-between items-start">
                      <h4 className="text-sm font-medium text-white">{exp.title}</h4>
                      <span className="text-xs text-white/60">
                        {formatDate(exp.startDate)} - {formatDate(exp.endDate)}
                      </span>
                    </div>
                    <p className="text-sm text-white/80 mt-1">{exp.company}</p>
                    {exp.description && (
                      <p className="text-xs text-white/70 mt-2 leading-relaxed">
                        {exp.description}
                      </p>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Education Section */}
          {profileData.education && profileData.education.length > 0 && (
            <div className="relative">
              <SectionHeader title="Education" icon={GraduationCap} iconClassName="text-pink-400" />
              <ul className="space-y-4">
                {profileData.education.map((edu, index) => (
                  <li key={index} className="pb-4">
                    {index !== profileData.education.length - 1 && (
                      <Separator className="bg-white/10 mt-4" />
                    )}
                    <div className="flex justify-between items-start">
                      <h4 className="text-sm font-medium text-white">{edu.degree}</h4>
                      <span className="text-xs text-white/60">
                        {formatDate(edu.startDate)} - {formatDate(edu.endDate)}
                      </span>
                    </div>
                    <p className="text-sm text-white/80 mt-1">{edu.institution}</p>
                    {edu.field && <p className="text-xs text-white/70 mt-1">{edu.field}</p>}
                    {edu.description && (
                      <p className="text-xs text-white/70 mt-2 leading-relaxed">
                        {edu.description}
                      </p>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Certifications Section */}
          {profileData.certifications && profileData.certifications.length > 0 && (
            <div className="relative">
              <SectionHeader title="Certifications" icon={Award} iconClassName="text-pink-400" />
              <ul className="space-y-3">
                {profileData.certifications.map((cert, index) => (
                  <li key={index} className="pb-3">
                    {index !== profileData.certifications.length - 1 && (
                      <Separator className="bg-white/10 mt-3" />
                    )}
                    <h4 className="text-sm font-medium text-white">{cert.name}</h4>
                    <p className="text-xs text-white/80 mt-1">
                      {cert.issuer} - {formatDate(cert.issueDate)}
                    </p>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Languages Section */}
          {profileData.languages && profileData.languages.length > 0 && (
            <div className="relative">
              <SectionHeader title="Languages" icon={Languages} iconClassName="text-pink-400" />
              <ul className="grid grid-cols-2 md:grid-cols-3 gap-x-6 gap-y-2">
                {profileData.languages.map((language, index) => (
                  <li key={index} className="flex items-center gap-2 text-white/90">
                    <ChevronRight className="w-4 h-4 text-pink-400 flex-shrink-0" />
                    <span>{language}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Values Section */}
          {profileData.myValues && profileData.myValues.length > 0 && (
            <div className="relative">
              <SectionHeader title="Core Values" icon={Heart} iconClassName="text-pink-400" />
              <ul className="grid grid-cols-2 md:grid-cols-3 gap-x-6 gap-y-2">
                {profileData.myValues.map((value, index) => (
                  <li key={index} className="flex items-center gap-2 text-white/90">
                    <ChevronRight className="w-4 h-4 text-pink-400 flex-shrink-0" />
                    <span>{value}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Job Preferences Section */}
          {profileData.preferences && (
            <div className="relative">
              <SectionHeader
                title="Job Preferences"
                icon={Briefcase}
                iconClassName="text-pink-400"
              />

              <div className="space-y-4">
                {/* Job Types */}
                {profileData.preferences.jobTypes &&
                  profileData.preferences.jobTypes.length > 0 && (
                    <div>
                      <h4 className="text-sm font-semibold text-white/80 mb-2">Desired Roles</h4>
                      <ul className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-2">
                        {profileData.preferences.jobTypes.map((type, index) => (
                          <li key={index} className="flex items-center gap-2 text-white/90">
                            <ChevronRight className="w-4 h-4 text-pink-400 flex-shrink-0" />
                            <span>{type}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                {/* Preferred Locations */}
                {profileData.preferences.locations &&
                  profileData.preferences.locations.length > 0 && (
                    <div>
                      <h4 className="text-sm font-semibold text-white/80 mb-2">
                        Preferred Locations
                      </h4>
                      <ul className="grid grid-cols-2 md:grid-cols-3 gap-x-6 gap-y-2">
                        {profileData.preferences.locations.map((location, index) => (
                          <li key={index} className="flex items-center gap-2 text-white/90">
                            <ChevronRight className="w-4 h-4 text-pink-400 flex-shrink-0" />
                            <span>{location}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                {/* Industries */}
                {profileData.preferences.industries &&
                  profileData.preferences.industries.length > 0 && (
                    <div>
                      <h4 className="text-sm font-semibold text-white/80 mb-2">
                        Target Industries
                      </h4>
                      <ul className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-2">
                        {profileData.preferences.industries.map((industry, index) => (
                          <li key={index} className="flex items-center gap-2 text-white/90">
                            <ChevronRight className="w-4 h-4 text-pink-400 flex-shrink-0" />
                            <span>{industry}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                {/* Salary Expectations */}
                {profileData.preferences.desiredSalary && (
                  <div>
                    <h4 className="text-sm font-semibold text-white/80 mb-2">
                      Salary Expectations
                    </h4>
                    <ul className="space-y-1">
                      <li className="flex items-center gap-2 text-white/90">
                        <ChevronRight className="w-4 h-4 text-pink-400 flex-shrink-0" />
                        <span>
                          {profileData.preferences.desiredSalary.currency}{' '}
                          {profileData.preferences.desiredSalary.min} -{' '}
                          {profileData.preferences.desiredSalary.max}
                        </span>
                      </li>
                      <li className="flex items-center gap-2 text-white/90">
                        <ChevronRight className="w-4 h-4 text-pink-400 flex-shrink-0" />
                        <span className="capitalize">
                          {profileData.preferences.desiredSalary.period} Payment
                        </span>
                      </li>
                    </ul>
                  </div>
                )}

                {/* Remote Preference */}
                {profileData.preferences.remotePreference && (
                  <div>
                    <h4 className="text-sm font-semibold text-white/80 mb-2">Work Preference</h4>
                    <ul>
                      <li className="flex items-center gap-2 text-white/90">
                        <ChevronRight className="w-4 h-4 text-pink-400 flex-shrink-0" />
                        <span className="capitalize">
                          {profileData.preferences.remotePreference}
                        </span>
                      </li>
                    </ul>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* JobSeeker Setup Slider */}
      {showSetupSlider && (
        <JobSeekerSetupSlider
          onComplete={() => {
            setShowSetupSlider(false);
            // Refresh the page to show updated data
            window.location.reload();
          }}
          onClose={() => setShowSetupSlider(false)}
          initialData={profileData}
        />
      )}
    </>
  );
}
