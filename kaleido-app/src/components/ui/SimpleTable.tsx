'use client';

import React, { useState, useMemo } from 'react';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface SimpleTableColumn {
  key: string;
  label: string;
  width?: string;
  align?: 'left' | 'center' | 'right';
  className?: string;
}

export interface SimpleTableProps {
  columns: SimpleTableColumn[];
  data: Record<string, any>[];
  className?: string;
  compact?: boolean;
  striped?: boolean;
  hoverable?: boolean;
  borderless?: boolean;
  headerClassName?: string;
  rowClassName?: string;
  cellClassName?: string;
  itemsPerPage?: number;
  showPagination?: boolean;
  paginationClassName?: string;
}

export function SimpleTable({
  columns,
  data,
  className = '',
  compact = true,
  striped = false,
  hoverable = true,
  borderless = false,
  headerClassName = '',
  rowClassName = '',
  cellClassName = '',
  itemsPerPage = 10,
  showPagination = false,
  paginationClassName = '',
}: SimpleTableProps) {
  const [currentPage, setCurrentPage] = useState(1);

  // Calculate pagination values
  const totalPages = Math.ceil(data.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;

  // Get paginated data
  const paginatedData = useMemo(() => {
    if (!showPagination) return data;
    return data.slice(startIndex, endIndex);
  }, [data, startIndex, endIndex, showPagination]);

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages: (number | string)[] = [];
    const maxPagesToShow = 5;

    if (totalPages <= maxPagesToShow) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      if (currentPage <= 3) {
        for (let i = 1; i <= 4; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      } else if (currentPage >= totalPages - 2) {
        pages.push(1);
        pages.push('...');
        for (let i = totalPages - 3; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        pages.push(1);
        pages.push('...');
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pages.push(i);
        }
        pages.push('...');
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };
  const getAlignmentClass = (align?: 'left' | 'center' | 'right') => {
    switch (align) {
      case 'center':
        return 'text-center';
      case 'right':
        return 'text-right';
      default:
        return 'text-left';
    }
  };

  return (
    <div className={cn('w-full overflow-x-auto', className)}>
      <table className="w-full">
        <thead>
          <tr
            className={cn(
              'border-b border-gray-700/30',
              borderless && 'border-transparent',
              headerClassName
            )}
          >
            {columns.map(column => (
              <th
                key={column.key}
                style={{ width: column.width }}
                className={cn(
                  'font-medium text-xs uppercase tracking-wider text-gray-400',
                  compact ? 'px-3 py-2' : 'px-4 py-3',
                  getAlignmentClass(column.align),
                  column.className
                )}
              >
                {column.label}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {paginatedData.length === 0 ? (
            <tr>
              <td colSpan={columns.length} className="text-center py-8 text-gray-500 text-sm">
                No data available
              </td>
            </tr>
          ) : (
            paginatedData.map((row, rowIndex) => (
              <tr
                key={rowIndex}
                className={cn(
                  'border-b border-gray-800/20 transition-colors',
                  borderless && 'border-transparent',
                  hoverable && 'hover:bg-white/[0.02]',
                  striped && rowIndex % 2 === 1 && 'bg-white/[0.01]',
                  rowClassName
                )}
              >
                {columns.map(column => (
                  <td
                    key={column.key}
                    className={cn(
                      'text-sm text-gray-300',
                      compact ? 'px-3 py-2.5' : 'px-4 py-3.5',
                      getAlignmentClass(column.align),
                      cellClassName,
                      column.className
                    )}
                  >
                    {row[column.key] || '-'}
                  </td>
                ))}
              </tr>
            ))
          )}
        </tbody>
      </table>

      {/* Pagination Controls */}
      {showPagination && totalPages > 1 && (
        <div
          className={cn(
            'flex items-center justify-between px-4 py-3 border-t border-gray-700/30',
            paginationClassName
          )}
        >
          <div className="text-sm text-gray-400">
            Showing {startIndex + 1} to {Math.min(endIndex, data.length)} of {data.length} results
          </div>

          <div className="flex items-center gap-1">
            {/* First page */}
            <button
              onClick={() => handlePageChange(1)}
              disabled={currentPage === 1}
              className={cn(
                'p-1.5 rounded-md transition-colors',
                currentPage === 1
                  ? 'text-gray-600 cursor-not-allowed'
                  : 'text-gray-400 hover:bg-white/5 hover:text-white'
              )}
              aria-label="First page"
            >
              <ChevronsLeft className="w-4 h-4" />
            </button>

            {/* Previous page */}
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className={cn(
                'p-1.5 rounded-md transition-colors',
                currentPage === 1
                  ? 'text-gray-600 cursor-not-allowed'
                  : 'text-gray-400 hover:bg-white/5 hover:text-white'
              )}
              aria-label="Previous page"
            >
              <ChevronLeft className="w-4 h-4" />
            </button>

            {/* Page numbers */}
            <div className="flex items-center gap-1 mx-2">
              {getPageNumbers().map((page, index) => (
                <button
                  key={index}
                  onClick={() => typeof page === 'number' && handlePageChange(page)}
                  disabled={page === '...'}
                  className={cn(
                    'min-w-[32px] h-8 px-2 rounded-md text-sm transition-colors',
                    page === currentPage
                      ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white font-medium'
                      : page === '...'
                        ? 'text-gray-600 cursor-default'
                        : 'text-gray-400 hover:bg-white/5 hover:text-white'
                  )}
                >
                  {page}
                </button>
              ))}
            </div>

            {/* Next page */}
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className={cn(
                'p-1.5 rounded-md transition-colors',
                currentPage === totalPages
                  ? 'text-gray-600 cursor-not-allowed'
                  : 'text-gray-400 hover:bg-white/5 hover:text-white'
              )}
              aria-label="Next page"
            >
              <ChevronRight className="w-4 h-4" />
            </button>

            {/* Last page */}
            <button
              onClick={() => handlePageChange(totalPages)}
              disabled={currentPage === totalPages}
              className={cn(
                'p-1.5 rounded-md transition-colors',
                currentPage === totalPages
                  ? 'text-gray-600 cursor-not-allowed'
                  : 'text-gray-400 hover:bg-white/5 hover:text-white'
              )}
              aria-label="Last page"
            >
              <ChevronsRight className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default SimpleTable;
