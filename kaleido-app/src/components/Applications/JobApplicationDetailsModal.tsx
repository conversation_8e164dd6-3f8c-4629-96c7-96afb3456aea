'use client';

import React, { Fragment, useState } from 'react';

import { AnimatePresence, motion } from 'framer-motion';
import {
  Activity,
  Banknote,
  Briefcase,
  Building2,
  Calendar,
  CheckCircle,
  Clock,
  FileText,
  GraduationCap,
  Languages,
  MapPin,
  Sparkles,
  Users,
  X,
  XCircle,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ApplicationCard } from '../JobSeeker/ApplicationCard';
import { WithdrawApplicationButton } from '@/components/shared/WithdrawApplicationButton';

interface JobApplicationDetailsModalProps {
  application: any;
  isOpen: boolean;
  onClose: () => void;
  onWithdraw?: (applicationId: string) => void;
}

const JobApplicationDetailsModal: React.FC<JobApplicationDetailsModalProps> = ({
  application,
  isOpen,
  onClose,
  onWithdraw,
}) => {
  const [activeTab, setActiveTab] = useState('details');

  if (!application) return null;

  const { job } = application;

  const tabs = [
    { id: 'details', label: 'Job Details', icon: FileText },
    { id: 'status', label: 'Application Status', icon: Activity },
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Full screen backdrop with blur - extra layer for better coverage */}
          <div className="fixed inset-0 bg-black/10 z-40" />
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/20 backdrop-blur-md z-50"
            onClick={onClose}
            style={{ top: '-10px', bottom: '-10px', left: '-10px', right: '-10px' }}
          />

          {/* Modal sliding from bottom */}
          <motion.div
            initial={{ y: '100%', opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: '100%', opacity: 0 }}
            transition={{
              type: 'spring',
              damping: 25,
              stiffness: 300,
              opacity: { duration: 0.3 },
            }}
            className="fixed inset-x-0 bottom-0 max-h-[90vh] sm:max-h-[85vh] bg-gradient-to-r from-slate-900/95 via-purple-950/95 to-slate-900/95 backdrop-blur-xl rounded-t-2xl sm:rounded-t-3xl z-[60] overflow-hidden flex flex-col border-t border-purple-400/20"
            style={{
              boxShadow:
                '0 -20px 50px -20px rgba(168, 85, 247, 0.5), 0 -10px 20px -10px rgba(168, 85, 247, 0.3)',
              minHeight: '70vh',
              height: 'auto',
              transition: 'height 0.3s ease-in-out',
            }}
          >
            {/* Enhanced Header with Image Background */}
            <div className="relative flex-shrink-0">
              {/* Gradient shadows emitting from edges */}
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600/40 via-transparent to-purple-600/40 blur-3xl transform scale-110" />
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/30 via-transparent to-purple-500/30 blur-2xl" />

              {/* Background image with overlay */}
              <div className="relative h-64 overflow-hidden">
                <div
                  className="absolute inset-0 bg-cover bg-center blur-sm"
                  style={{
                    backgroundImage: `url('/images/landing/exclusive-2.webp')`,
                  }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-slate-900 via-slate-900/80 to-transparent" />
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 via-transparent to-pink-600/20" />

                {/* Content over image */}
                <div className="relative z-10 h-full flex flex-col justify-between p-6">
                  {/* Top Section - Close button */}
                  <div className="flex justify-end">
                    <button
                      onClick={onClose}
                      className="p-2 rounded-full bg-white/10 backdrop-blur-md hover:bg-white/20 transition-all border border-white/20"
                      aria-label="Close modal"
                    >
                      <X className="w-5 h-5 text-white" />
                    </button>
                  </div>

                  {/* Middle Section - Job Info */}
                  <div className="pb-20">
                    <h2 className="text-2xl sm:text-3xl font-bold text-white tracking-tight mb-2">
                      {job.jobType}
                    </h2>
                    <div className="flex flex-wrap items-center gap-3 text-sm text-white/80 mb-4">
                      <div className="flex items-center gap-1.5">
                        <Building2 className="w-4 h-4" />
                        <span>{job.companyName}</span>
                      </div>
                      {job.department && (
                        <>
                          <span className="text-white/40">•</span>
                          <div className="flex items-center gap-1.5">
                            <Briefcase className="w-4 h-4" />
                            <span>{job.department}</span>
                          </div>
                        </>
                      )}
                      <span className="text-white/40">•</span>
                      <div className="flex items-center gap-1.5">
                        <Calendar className="w-4 h-4" />
                        <span>
                          {new Date(application.createdAt).toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric',
                            year: 'numeric',
                          })}
                        </span>
                      </div>
                    </div>

                    {/* Job Details Row - Like in PageHeader */}
                    <div className="flex flex-wrap items-center gap-3">
                      {/* Quick info badges */}
                      {job.tldr?.salary && (
                        <Badge
                          variant="outline"
                          className="bg-white/10 text-white border-white/20 backdrop-blur-sm px-3 py-1"
                        >
                          <Banknote className="w-3.5 h-3.5 mr-1.5" />
                          {job.tldr.salary}
                        </Badge>
                      )}
                      {job.tldr?.experience && (
                        <Badge
                          variant="outline"
                          className="bg-white/10 text-white border-white/20 backdrop-blur-sm px-3 py-1"
                        >
                          <Clock className="w-3.5 h-3.5 mr-1.5" />
                          {job.tldr.experience}
                        </Badge>
                      )}
                      {job.typeOfJob && (
                        <Badge
                          variant="outline"
                          className="bg-white/10 text-white border-white/20 backdrop-blur-sm px-3 py-1"
                        >
                          {job.typeOfJob}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Section Tabs with Withdraw Button */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-slate-900 via-slate-900/80 to-transparent pt-8 z-30">
                <div className="px-6 pb-0 flex items-center justify-between">
                  {/* Custom Tab Navigation with proper spacing */}
                  <div className="flex gap-0 overflow-x-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
                    {tabs.map((tab, index) => (
                      <Fragment key={tab.id}>
                        {index > 0 && (
                          <div className="w-px bg-white/10 self-center h-6 hidden sm:block" />
                        )}
                        <button
                          onClick={() => setActiveTab(tab.id)}
                          className={`
                            relative px-3 sm:px-4 h-12 flex items-center gap-1 sm:gap-2 text-xs sm:text-sm font-medium transition-all whitespace-nowrap flex-shrink-0
                            ${
                              activeTab === tab.id
                                ? 'text-white'
                                : 'text-white/60 hover:text-white/90 hover:bg-white/5'
                            }
                          `}
                        >
                          {/* Gradient background for active tab */}
                          {activeTab === tab.id && (
                            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-purple-600/20 to-pink-600/30" />
                          )}

                          {/* Content */}
                          <span className="relative z-10 flex items-center gap-1 sm:gap-2">
                            <tab.icon className="w-3.5 sm:w-4 h-3.5 sm:h-4" />
                            <span>{tab.label}</span>
                          </span>

                          {/* Bottom border for active tab */}
                          {activeTab === tab.id && (
                            <div className="absolute bottom-0 left-0 right-0 h-[2px] bg-gradient-to-r from-purple-600 to-pink-600" />
                          )}
                        </button>
                      </Fragment>
                    ))}
                  </div>

                  {/* Withdraw Button - Same level as tabs with proper spacing */}
                  {application.status !== 'WITHDRAWN' && onWithdraw && (
                    <div className="ml-auto pl-4 relative z-50">
                      <WithdrawApplicationButton onClick={() => onWithdraw(application.id)} />
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Content with scrollable area */}
            <motion.div
              className="flex-1 overflow-y-auto bg-slate-900/50 custom-scrollbar"
              animate={{ height: 'auto' }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
            >
              <AnimatePresence mode="wait">
                {activeTab === 'details' && (
                  <motion.div
                    key="details"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    className="p-4 sm:p-6 space-y-4"
                  >
                    {/* Quick Summary (TLDR) - Compact */}
                    {job.tldr && (
                      <div className="bg-gradient-to-br from-purple-600/10 to-pink-600/10 backdrop-blur-sm rounded-lg p-4 border border-purple-400/20">
                        <div className="flex items-start gap-2.5">
                          <Sparkles className="w-4 h-4 text-purple-400 mt-0.5 flex-shrink-0" />
                          <div className="flex-1">
                            <p className="text-white/80 text-sm leading-relaxed mb-2">
                              {job.tldr.summary}
                            </p>
                            <div className="flex flex-wrap gap-1.5">
                              {job.tldr.salary && (
                                <Badge
                                  variant="outline"
                                  className="text-xs text-white/70 border-white/20 px-2 py-0.5"
                                >
                                  <Banknote className="w-3 h-3 mr-1" />
                                  {job.tldr.salary}
                                </Badge>
                              )}
                              {job.tldr.experience && (
                                <Badge
                                  variant="outline"
                                  className="text-xs text-white/70 border-white/20 px-2 py-0.5"
                                >
                                  <Clock className="w-3 h-3 mr-1" />
                                  {job.tldr.experience}
                                </Badge>
                              )}
                              {job.tldr.typeOfJob && (
                                <Badge
                                  variant="outline"
                                  className="text-xs text-white/70 border-white/20 px-2 py-0.5"
                                >
                                  {job.tldr.typeOfJob}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Location, Experience, Type and Salary Cards */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      {/* Location */}
                      {job.location && job.location.length > 0 && (
                        <div className="bg-gradient-to-br from-blue-600/10 to-blue-500/5 backdrop-blur-sm rounded-xl p-4 border border-blue-400/20">
                          <div className="flex items-center gap-2 mb-2">
                            <MapPin className="w-4 h-4 text-blue-400" />
                            <h4 className="text-sm font-semibold text-white">Location</h4>
                          </div>
                          <div className="space-y-1">
                            {job.location.slice(0, 3).map((loc: string, idx: number) => (
                              <p key={idx} className="text-sm text-white/80">
                                {loc}
                              </p>
                            ))}
                            {job.location.length > 3 && (
                              <p className="text-xs text-white/60">
                                +{job.location.length - 3} more
                              </p>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Experience */}
                      {job.experience && (
                        <div className="bg-gradient-to-br from-green-600/10 to-green-500/5 backdrop-blur-sm rounded-xl p-4 border border-green-400/20">
                          <div className="flex items-center gap-2 mb-2">
                            <Briefcase className="w-4 h-4 text-green-400" />
                            <h4 className="text-sm font-semibold text-white">Experience</h4>
                          </div>
                          <p className="text-sm text-white/80">{job.experience}</p>
                        </div>
                      )}

                      {/* Type */}
                      <div className="bg-gradient-to-br from-purple-600/10 to-purple-500/5 backdrop-blur-sm rounded-xl p-4 border border-purple-400/20">
                        <div className="flex items-center gap-2 mb-2">
                          <Calendar className="w-4 h-4 text-purple-400" />
                          <h4 className="text-sm font-semibold text-white">Type</h4>
                        </div>
                        <p className="text-sm text-white/80 uppercase">{job.typeOfJob || 'N/A'}</p>
                      </div>

                      {/* Salary */}
                      <div className="bg-gradient-to-br from-yellow-600/10 to-yellow-500/5 backdrop-blur-sm rounded-xl p-4 border border-yellow-400/20">
                        <div className="flex items-center gap-2 mb-2">
                          <Banknote className="w-4 h-4 text-yellow-400" />
                          <h4 className="text-sm font-semibold text-white">Salary</h4>
                        </div>
                        <p className="text-sm text-white/80">
                          {job.salaryRange || job.tldr?.salary || 'Negotiable'}
                        </p>
                      </div>
                    </div>

                    {/* Company Description - Compact */}
                    {job.companyDescription && (
                      <div className="bg-white/5 rounded-lg p-4 border border-white/10">
                        <div className="flex items-center gap-2 mb-2">
                          <Building2 className="w-4 h-4 text-indigo-400" />
                          <h3 className="text-sm font-semibold text-white">
                            About {job.companyName}
                          </h3>
                        </div>
                        <p className="text-white/70 text-xs leading-relaxed line-clamp-3">
                          {job.companyDescription}
                        </p>
                      </div>
                    )}

                    {/* Skills & Responsibilities - Compact Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {/* Skills Required */}
                      {job.skills && job.skills.length > 0 && (
                        <div className="bg-white/5 rounded-lg p-4 border border-white/10">
                          <div className="flex items-center gap-2 mb-2">
                            <Users className="w-4 h-4 text-orange-400" />
                            <h3 className="text-sm font-semibold text-white">Required Skills</h3>
                          </div>
                          <div className="space-y-1 max-h-32 overflow-y-auto">
                            {job.skills.slice(0, 5).map((skill: string, idx: number) => (
                              <div key={idx} className="flex items-start gap-1.5">
                                <div className="w-1 h-1 rounded-full bg-orange-400 mt-1" />
                                <span className="text-white/70 text-xs leading-relaxed">
                                  {skill}
                                </span>
                              </div>
                            ))}
                            {job.skills.length > 5 && (
                              <span className="text-xs text-white/50 pl-2.5">
                                +{job.skills.length - 5} more
                              </span>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Responsibilities */}
                      {job.jobResponsibilities && job.jobResponsibilities.length > 0 && (
                        <div className="bg-white/5 rounded-lg p-4 border border-white/10">
                          <div className="flex items-center gap-2 mb-2">
                            <CheckCircle className="w-4 h-4 text-teal-400" />
                            <h3 className="text-sm font-semibold text-white">
                              Key Responsibilities
                            </h3>
                          </div>
                          <div className="space-y-1 max-h-32 overflow-y-auto">
                            {job.jobResponsibilities
                              .slice(0, 5)
                              .map((resp: string, idx: number) => (
                                <div key={idx} className="flex items-start gap-1.5">
                                  <div className="w-1 h-1 rounded-full bg-teal-400 mt-1" />
                                  <span className="text-white/70 text-xs leading-relaxed">
                                    {resp}
                                  </span>
                                </div>
                              ))}
                            {job.jobResponsibilities.length > 5 && (
                              <span className="text-xs text-white/50 pl-2.5">
                                +{job.jobResponsibilities.length - 5} more
                              </span>
                            )}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Additional Requirements - Compact */}
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {/* Education */}
                      {job.education && job.education.length > 0 && (
                        <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                          <div className="flex items-center gap-1.5 mb-1.5">
                            <GraduationCap className="w-3.5 h-3.5 text-pink-400" />
                            <h4 className="text-xs font-semibold text-white">Education</h4>
                          </div>
                          <div className="space-y-0.5">
                            {job.education.map((edu: string, idx: number) => (
                              <p key={idx} className="text-white/70 text-xs">
                                {edu}
                              </p>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Languages */}
                      {job.language && job.language.length > 0 && (
                        <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                          <div className="flex items-center gap-1.5 mb-1.5">
                            <Languages className="w-3.5 h-3.5 text-violet-400" />
                            <h4 className="text-xs font-semibold text-white">Languages</h4>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {job.language.map((lang: string, idx: number) => (
                              <Badge
                                key={idx}
                                variant="secondary"
                                className="text-xs bg-violet-500/10 text-violet-300 border-violet-500/20 px-1.5 py-0"
                              >
                                {lang}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Benefits */}
                      {job.benefits && job.benefits.length > 0 && (
                        <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                          <div className="flex items-center gap-1.5 mb-1.5">
                            <Sparkles className="w-3.5 h-3.5 text-green-400" />
                            <h4 className="text-xs font-semibold text-white">Benefits</h4>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {job.benefits.slice(0, 3).map((benefit: string, idx: number) => (
                              <Badge
                                key={idx}
                                variant="secondary"
                                className="text-xs bg-green-500/10 text-green-300 border-green-500/20 px-1.5 py-0"
                              >
                                {benefit}
                              </Badge>
                            ))}
                            {job.benefits.length > 3 && (
                              <Badge
                                variant="secondary"
                                className="text-xs bg-green-500/10 text-green-300 border-green-500/20 px-1.5 py-0"
                              >
                                +{job.benefits.length - 3}
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Full Job Description - Optional Expandable */}
                    {job.generatedJD && (
                      <details className="bg-white/5 rounded-lg border border-white/10">
                        <summary className="p-4 cursor-pointer">
                          <div className="flex items-center gap-2 inline-flex">
                            <FileText className="w-4 h-4 text-cyan-400" />
                            <h3 className="text-sm font-semibold text-white">
                              Full Job Description
                            </h3>
                          </div>
                        </summary>
                        <div className="px-4 pb-4 pt-0">
                          <div className="text-white/70 text-xs leading-relaxed whitespace-pre-wrap max-h-64 overflow-y-auto">
                            {job.generatedJD}
                          </div>
                        </div>
                      </details>
                    )}
                  </motion.div>
                )}

                {activeTab === 'status' && (
                  <motion.div
                    key="status"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    className="p-4 sm:p-6"
                  >
                    {/* ApplicationCard component */}
                    <ApplicationCard
                      application={{
                        id: application.id,
                        jobId: application.jobId || job.id,
                        status: application.status,
                        createdAt: application.createdAt,
                        updatedAt: application.updatedAt || application.createdAt,
                        statusTimeline: application.statusTimeline || [
                          {
                            status: application.status,
                            timestamp: application.createdAt,
                            note:
                              application.status === 'WITHDRAWN'
                                ? application.withdrawalReason
                                : undefined,
                          },
                        ],
                        job: {
                          id: job.id,
                          jobType: job.jobType,
                          companyName: job.companyName,
                          department: job.department || '',
                          location: job.location || [],
                          salaryRange: job.salaryRange || job.tldr?.salary || '',
                          experience: job.experience || job.tldr?.experience || '',
                          status: job.status || 'ACTIVE',
                        },
                      }}
                      onWithdraw={(applicationId: string) => {
                        if (onWithdraw) {
                          onWithdraw(applicationId);
                        }
                      }}
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default JobApplicationDetailsModal;
