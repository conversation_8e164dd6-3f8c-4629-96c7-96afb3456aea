import { useUser } from '@auth0/nextjs-auth0/client';
import { useEffect, useState } from 'react';

/**
 * Custom hook for handling authentication on public pages
 * Addresses Auth0 session sharing issues between tabs
 */
export const usePublicAuth = () => {
  const { user: auth0User, isLoading: auth0Loading, error: auth0Error } = useUser();
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<any>(null);

  useEffect(() => {
    const checkAuth = async () => {
      // If Auth0 is still loading, wait
      if (auth0Loading) {
        setIsLoading(true);
        return;
      }

      // If there's an Auth0 error, propagate it
      if (auth0Error) {
        setError(auth0Error);
        setIsLoading(false);
        return;
      }

      // Check if we have a valid user from Auth0
      const hasValidAuth0User = auth0User && auth0User.sub && (auth0User.name || auth0User.email);

      if (hasValidAuth0User) {
        // We have valid user data from Auth0
        setUser(auth0User);
        setError(null);
        setIsLoading(false);
        return;
      }

      // If Auth0 user exists but is empty (common session sharing issue)
      if (auth0User && !auth0User.sub) {
        console.log('Empty Auth0 user detected, checking server session...');

        try {
          // Try to fetch user session from server
          const response = await fetch('/api/auth/me', {
            credentials: 'include',
            headers: {
              'Content-Type': 'application/json',
            },
          });

          if (response.ok) {
            const userData = await response.json();
            if (userData && userData.sub) {
              console.log('Got user data from server:', userData);
              setUser(userData);
              setError(null);
              setIsLoading(false);
              return;
            }
          }
        } catch (serverError) {
          console.log('Server session check failed:', serverError);
        }
      }

      // No valid user found
      setUser(null);
      setError(null);
      setIsLoading(false);
    };

    checkAuth();
  }, [auth0User, auth0Loading, auth0Error]);

  return {
    user,
    isLoading,
    error,
  };
};
