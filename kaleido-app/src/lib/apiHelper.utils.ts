// API URLs from environment variables
export const BACKEND_API_URL = process.env.NEXT_PUBLIC_API_URL_BASE || 'http://localhost:8080/api';

// List of public endpoints that don't require authentication
export const PUBLIC_ENDPOINTS = [
  // Auth endpoints
  '/auth/login',
  '/auth/register',
  '/auth/callback',
  // Note: /auth/me is NOT public - it requires authentication

  // Legacy public endpoints
  '/companies/public',
  '/jobs/public',
  '/health',
  '/videos/upload',
  '/companies/upload-logo',

  // Public company profile endpoints
  '/companies/profile/',

  // Public assessment endpoints
  '/assessments/questions',
  '/assessments',
  '/companies/by-user',

  // Public job endpoints
  '/jobs/company/public',
  '/jobs/slug/',
  '/jobs/public/search',
  '/jobs/open-jobs',
  '/jobs/', // This covers /jobs/{id}/public-application and other job detail endpoints

  // Public profile endpoints
  '/job-seekers/public/',
  '/graduates/public/',
  '/candidate-profiles/public/',

  // Public candidate download endpoints
  '/candidates/download-profile-simple',
  '/candidates/download-profile',

  // Public candidate profile endpoints
  '/candidates/public-profile/',
  '/public/candidate/',

  // Public video response endpoints
  '/video-responses/upload',
  '/video-responses/culture-fit',

  // Team invitation acceptance endpoint
  '/company/accept-invitation',
];

// List of endpoints that accept temporary user IDs for onboarding
export const TEMP_USER_ENDPOINTS = [
  '/job-seekers/temp',
  '/job-seekers/temp/',
  '/companies/temp',
  '/companies/temp/',
  '/auth/merge-temp-user',
];

// List of worker task endpoints that should bypass circuit breaker
// These are typically long-running tasks that poll for status updates
export const WORKER_TASK_ENDPOINTS = [
  '/candidates/upload-status/',
  '/candidates/match-status/',
  '/candidates/scout-status/', // Scout status polling
  '/jobs/video-jd-status/',
  '/video-jd/status/', // Video JD status polling
  '/scout/status/',
  '/jobs/match-rank-status/',
  '/career-insights/status/', // Career insights status polling
  '/comparisons/status/', // Comparison status polling
];

// List of endpoints that should have a higher debounce time
export const DEBOUNCED_ENDPOINTS = [
  '/feedback', // Feedback endpoint needs more debouncing
  '/candidates/search', // Search endpoints need debouncing
  '/jobs/search', // Job search needs debouncing
  '/companies/search', // Company search needs debouncing
  '/scout-candidates', // Scout candidate searches
  '/scouted-candidates', // Scouted candidates fetching needs debouncing
  '/candidates/match', // Candidate matching
  '/dashboard/stats', // Dashboard stats
  '/auth/me', // User profile checks
  '/jobs/', // Job endpoints need higher limits to prevent circuit breaker on save operations
];

// Specific endpoint patterns that need request cancellation handling
export const CANCELLABLE_ENDPOINT_PATTERNS = [
  /^\/jobs\/[a-zA-Z0-9-]+$/, // Direct job detail endpoints like /jobs/{id}
  /^\/jobs\/[a-zA-Z0-9-]+\/criteria/, // Job criteria endpoints like /jobs/{id}/criteria
];

// Regex patterns for more precise matching of worker task endpoints with IDs
export const WORKER_TASK_PATTERNS = [
  /^\/candidates\/upload-status\/[a-zA-Z0-9-]+$/,
  /^\/candidates\/match-status\/[a-zA-Z0-9-]+$/,
  /^\/candidates\/scout-status\/[a-zA-Z0-9-]+$/, // Matches /candidates/scout-status/{id}
  /^\/jobs\/video-jd-status\/[a-zA-Z0-9-]+$/,
  /^\/video-jd\/status\/[a-zA-Z0-9-]+$/, // Matches /video-jd/status/{id}
  /^\/video-jd\/conversion-status\/[a-zA-Z0-9-]+$/, // Matches /video-jd/conversion-status/{jobId}
  /^\/scout\/status\/[a-zA-Z0-9-]+$/,
  /^\/jobs\/match-rank-status\/[a-zA-Z0-9-]+$/,
  /^\/career-insights\/status\/[a-zA-Z0-9-]+$/, // Matches /career-insights/status/{id}
  /^\/comparisons\/status\/[a-zA-Z0-9-]+$/, // Matches /comparisons/status/{jobId}
  /^\/scouted-candidates$/, // Matches /scouted-candidates (used for refreshing scout results)
];

export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: string;
  videoUrl?: string;
  videoId?: string;
  synthesiaVideoId?: string;
  script?: string;
  skills?: string[];
  responsibilities?: string[];
  result?: any;
}

// Track in-flight requests to prevent duplicate calls
export const pendingRequests: Record<string, Promise<any>> = {};

// Simple cache for GET requests with TTL
interface CacheEntry {
  data: any;
  timestamp: number;
  ttl: number;
}

const requestCache: { [key: string]: CacheEntry } = {};

// Cache helper functions
export const getCachedResponse = (key: string): any | null => {
  const entry = requestCache[key];
  if (!entry) return null;

  const now = Date.now();
  if (now - entry.timestamp > entry.ttl) {
    delete requestCache[key];
    return null;
  }

  return entry.data;
};

export const setCachedResponse = (key: string, data: any, ttl: number = 5000) => {
  requestCache[key] = {
    data,
    timestamp: Date.now(),
    ttl,
  };
};

export const clearCache = (pattern?: string) => {
  if (pattern) {
    Object.keys(requestCache).forEach(key => {
      if (key.includes(pattern)) {
        delete requestCache[key];
      }
    });
  } else {
    Object.keys(requestCache).forEach(key => {
      delete requestCache[key];
    });
  }
};

// Track AbortControllers for request cancellation
export const requestAbortControllers: Record<string, AbortController> = {};

// Track API call frequency to detect infinite loops
export interface ApiCallTracker {
  count: number;
  timestamps: number[];
  lastErrorTimestamp?: number;
  consecutiveErrors: number;
  circuitBroken: boolean;
  circuitBrokenUntil?: number;
  isPollingEndpoint?: boolean; // Flag to identify known polling endpoints
  lastPollingTimestamp?: number; // Track last polling time
}

export const API_CALL_TRACKERS: Record<string, ApiCallTracker> = {};

// Enhanced request deduplication with timeout handling
export const REQUEST_DEDUPLICATION_CACHE: Record<
  string,
  {
    promise: Promise<any>;
    timestamp: number;
    timeout: NodeJS.Timeout;
  }
> = {};
export const MAX_CALLS_PER_MINUTE = 10; // Maximum number of calls to the same endpoint per minute
export const CIRCUIT_BREAKER_THRESHOLD = 5; // Number of consecutive errors before breaking the circuit
export const CIRCUIT_BREAKER_RESET_TIME = 30000; // 30 seconds before resetting the circuit breaker

// Debounce time configurations for different endpoint types
export const DEBOUNCE_TIMES = {
  SEARCH: 800, // Search endpoints (user typing)
  STATUS_POLL: 1000, // Status polling endpoints
  NAVIGATION: 300, // Navigation/auth endpoints
  FEEDBACK: 1500, // Feedback endpoints
  JOB_DETAILS: 2000, // Job details endpoints - increased for matchRankDetails
  DEFAULT: 500, // Default debounce time
} as const;

// Check if an endpoint is public
export const isPublicEndpoint = (endpoint: string): boolean => {
  const cleanEndpoint = endpoint.split('?')[0];
  return PUBLIC_ENDPOINTS.some(
    publicPath => cleanEndpoint.startsWith(publicPath) || cleanEndpoint.includes('/public/')
  );
};

// Check if an endpoint accepts temporary user IDs
export const isTempUserEndpoint = (endpoint: string): boolean => {
  const cleanEndpoint = endpoint.split('?')[0];
  return TEMP_USER_ENDPOINTS.some(tempPath => cleanEndpoint.startsWith(tempPath));
};

// Check if an endpoint is a worker task endpoint
export const isWorkerTaskEndpoint = (endpoint: string): boolean => {
  const cleanEndpoint = endpoint.split('?')[0];

  // First check against regex patterns for precise matching with IDs
  const matchesPattern = WORKER_TASK_PATTERNS.some(pattern => pattern.test(cleanEndpoint));
  if (matchesPattern) {
    return true;
  }

  // Then check for prefix matches (for backward compatibility)
  const isExactMatch = WORKER_TASK_ENDPOINTS.some(workerPath => {
    // If the endpoint contains an ID, check if it starts with the worker path
    if (cleanEndpoint.includes('/status/')) {
      return cleanEndpoint.startsWith(workerPath.split('/status/')[0] + '/status/');
    }
    return cleanEndpoint.startsWith(workerPath);
  });

  return isExactMatch;
};

// Check if an endpoint needs request cancellation
export const isCancellableEndpoint = (endpoint: string): boolean => {
  const cleanEndpoint = endpoint.split('?')[0];

  // Use specific patterns to match only the endpoints we want to cancel
  return CANCELLABLE_ENDPOINT_PATTERNS.some(pattern => pattern.test(cleanEndpoint));
};

// Helper function to track API calls and detect infinite loops
export const trackApiCall = (
  endpoint: string,
  method: string,
  isError: boolean = false,
  _statusCode?: number
): boolean => {
  // Only track GET requests for now as they're most likely to cause infinite loops
  if (method !== 'GET') return true;

  // Create a clean endpoint key without query parameters
  const endpointKey = endpoint.split('?')[0];
  const now = Date.now();

  // Check if this is a worker task endpoint - these should bypass circuit breaker
  const isPollingEndpoint = isWorkerTaskEndpoint(endpoint);
  if (isPollingEndpoint) {
    // For polling endpoints, we still want to track them but with different rules

    // Initialize tracker if it doesn't exist
    if (!API_CALL_TRACKERS[endpointKey]) {
      API_CALL_TRACKERS[endpointKey] = {
        count: 0,
        timestamps: [],
        consecutiveErrors: 0,
        circuitBroken: false,
        isPollingEndpoint: true,
        lastPollingTimestamp: now,
      };
    } else {
      // Mark existing tracker as a polling endpoint
      API_CALL_TRACKERS[endpointKey].isPollingEndpoint = true;
      API_CALL_TRACKERS[endpointKey].lastPollingTimestamp = now;
    }

    // For polling endpoints, we always allow the call to proceed
    // We just track it for monitoring purposes
    return true;
  }

  // Initialize tracker if it doesn't exist
  if (!API_CALL_TRACKERS[endpointKey]) {
    API_CALL_TRACKERS[endpointKey] = {
      count: 0,
      timestamps: [],
      consecutiveErrors: 0,
      circuitBroken: false,
      isPollingEndpoint: false,
    };
  }

  const tracker = API_CALL_TRACKERS[endpointKey];

  // If this was previously marked as a polling endpoint but isn't being detected as one now,
  // we'll still treat it as a polling endpoint to maintain consistency
  if (tracker.isPollingEndpoint === true) {
    tracker.lastPollingTimestamp = now;
    return true;
  }

  // Check if circuit is broken
  if (tracker.circuitBroken) {
    if (tracker.circuitBrokenUntil && now > tracker.circuitBrokenUntil) {
      // Reset circuit breaker after timeout
      tracker.circuitBroken = false;
      tracker.consecutiveErrors = 0;
      tracker.circuitBrokenUntil = undefined;
    } else {
      // Circuit is still broken
      console.error(`🚫 Circuit breaker active for ${endpointKey} - blocking request`);
      return false;
    }
  }

  // Update tracker
  tracker.count++;
  tracker.timestamps.push(now);

  // Remove timestamps older than 1 minute
  tracker.timestamps = tracker.timestamps.filter(timestamp => now - timestamp < 60000);

  // Check for too many calls in the last minute
  // For endpoints that might be called frequently during navigation, we'll be more lenient
  const isNavigationEndpoint =
    endpointKey.startsWith('/companies/client') ||
    endpointKey === '/auth/me' ||
    endpointKey === '/dashboard/stats';

  // Check if this is an endpoint that needs special handling for call limits
  const needsExtraAllowance = DEBOUNCED_ENDPOINTS.some(path => endpointKey.startsWith(path));

  // Adjust max calls allowed based on endpoint type
  const maxCallsAllowed = needsExtraAllowance
    ? MAX_CALLS_PER_MINUTE * 3
    : isNavigationEndpoint
      ? MAX_CALLS_PER_MINUTE * 2
      : MAX_CALLS_PER_MINUTE;

  if (tracker.timestamps.length > maxCallsAllowed) {
    console.error(
      `🚫 Too many calls to ${endpointKey} (${tracker.timestamps.length} in the last minute) - breaking circuit`
    );

    // For navigation endpoints, we'll set a shorter circuit breaker time
    const resetTime = isNavigationEndpoint
      ? CIRCUIT_BREAKER_RESET_TIME / 2
      : CIRCUIT_BREAKER_RESET_TIME;

    tracker.circuitBroken = true;
    tracker.circuitBrokenUntil = now + resetTime;
    return false;
  }

  // Track errors
  if (isError) {
    tracker.lastErrorTimestamp = now;
    tracker.consecutiveErrors++;

    // If we have too many consecutive errors, break the circuit
    // For navigation endpoints, we'll be more lenient with error thresholds
    const errorThreshold = isNavigationEndpoint
      ? CIRCUIT_BREAKER_THRESHOLD * 2
      : CIRCUIT_BREAKER_THRESHOLD;

    if (tracker.consecutiveErrors >= errorThreshold) {
      console.error(
        `🚫 Too many consecutive errors (${tracker.consecutiveErrors}) for ${endpointKey} - breaking circuit`
      );
      tracker.circuitBroken = true;
      tracker.circuitBrokenUntil = now + CIRCUIT_BREAKER_RESET_TIME;
      return false;
    }
  } else {
    // Reset consecutive errors on success
    tracker.consecutiveErrors = 0;
  }

  return true;
};

// Helper function to implement debouncing for repeated calls
export const debounceApiCall = (endpoint: string, method: string): boolean => {
  // Only debounce GET requests
  if (method !== 'GET') return true;

  const endpointKey = endpoint.split('?')[0];
  const now = Date.now();

  // Get the tracker for this endpoint
  const tracker = API_CALL_TRACKERS[endpointKey];

  // Don't debounce polling endpoints
  if (tracker && tracker.isPollingEndpoint === true) {
    // For polling endpoints, we might want to enforce a minimum interval
    // between calls, but we don't want to block them completely
    if (tracker.lastPollingTimestamp) {
      const timeSinceLastPoll = now - tracker.lastPollingTimestamp;

      // If polling too frequently (less than 500ms), log it but still allow
      if (timeSinceLastPoll < 500) {
      }
    }

    // Update the last polling timestamp
    tracker.lastPollingTimestamp = now;
    return true;
  }

  // Don't debounce worker task endpoints that might not be in the tracker yet
  if (isWorkerTaskEndpoint(endpoint)) {
    return true;
  }

  // Check if this is a navigation-related endpoint
  const isNavigationEndpoint =
    endpointKey.startsWith('/companies/client') ||
    endpointKey === '/auth/me' ||
    endpointKey === '/dashboard/stats' ||
    endpointKey === '/dashboard/enhanced-stats';

  // Determine the appropriate debounce time based on endpoint type
  let debounceTime: number = DEBOUNCE_TIMES.DEFAULT;

  // Check for job details endpoints (most problematic) using specific patterns
  if (CANCELLABLE_ENDPOINT_PATTERNS.some(pattern => pattern.test(endpointKey))) {
    debounceTime = DEBOUNCE_TIMES.JOB_DETAILS;
  }
  // Check for search endpoints
  else if (endpointKey.includes('/search') || endpointKey.includes('/scout-candidates')) {
    debounceTime = DEBOUNCE_TIMES.SEARCH;
  }
  // Check for status polling endpoints
  else if (isWorkerTaskEndpoint(endpointKey)) {
    debounceTime = DEBOUNCE_TIMES.STATUS_POLL;
  }
  // Check for navigation endpoints
  else if (isNavigationEndpoint) {
    debounceTime = DEBOUNCE_TIMES.NAVIGATION;
  }
  // Check for feedback endpoints
  else if (endpointKey.includes('/feedback')) {
    debounceTime = DEBOUNCE_TIMES.FEEDBACK;
  }
  // Check for other endpoints that need extra debouncing
  else if (DEBOUNCED_ENDPOINTS.some(path => endpointKey.startsWith(path))) {
    debounceTime = DEBOUNCE_TIMES.SEARCH; // Use search debounce time for other high-frequency endpoints
  }

  // Check for very recent calls for non-polling endpoints
  if (tracker && tracker.timestamps.length > 0) {
    const mostRecentCall = Math.max(...tracker.timestamps);
    if (now - mostRecentCall < debounceTime) {
      return false;
    }
  }

  return true;
};

// Enhanced request deduplication function
export const manageRequestDeduplication = (
  requestKey: string,
  requestPromise: Promise<any>,
  timeoutMs: number = 30000 // 30 seconds default timeout
): Promise<any> => {
  // Check if there's already a pending request for this key
  if (REQUEST_DEDUPLICATION_CACHE[requestKey]) {
    const cached = REQUEST_DEDUPLICATION_CACHE[requestKey];

    // If the cached request is still fresh (less than 5 seconds old), return it
    if (Date.now() - cached.timestamp < 5000) {
      return cached.promise;
    } else {
      // Clean up the old request
      clearTimeout(cached.timeout);
      delete REQUEST_DEDUPLICATION_CACHE[requestKey];
    }
  }

  // Create a timeout to clean up the cache entry
  const timeout = setTimeout(() => {
    delete REQUEST_DEDUPLICATION_CACHE[requestKey];
  }, timeoutMs);

  // Store the new request
  REQUEST_DEDUPLICATION_CACHE[requestKey] = {
    promise: requestPromise,
    timestamp: Date.now(),
    timeout,
  };

  // Clean up when the promise resolves or rejects
  requestPromise.finally(() => {
    clearTimeout(timeout);
    delete REQUEST_DEDUPLICATION_CACHE[requestKey];
  });

  return requestPromise;
};

// Helper function to cancel previous requests for the same endpoint
export const cancelPreviousRequest = (endpoint: string): void => {
  const cleanEndpoint = endpoint.split('?')[0];

  // Cancel any existing request for this endpoint
  if (requestAbortControllers[cleanEndpoint]) {
    requestAbortControllers[cleanEndpoint].abort();
    delete requestAbortControllers[cleanEndpoint];
  }
};

// Helper function to create and track an AbortController for cancellable endpoints
export const createAbortController = (endpoint: string): AbortController | undefined => {
  const cleanEndpoint = endpoint.split('?')[0];

  if (isCancellableEndpoint(endpoint)) {
    // Cancel any existing request first
    cancelPreviousRequest(endpoint);

    // Create new AbortController
    const controller = new AbortController();
    requestAbortControllers[cleanEndpoint] = controller;

    return controller;
  }

  return undefined;
};

// Helper function to cleanup AbortController after request completion
export const cleanupAbortController = (endpoint: string): void => {
  const cleanEndpoint = endpoint.split('?')[0];

  if (requestAbortControllers[cleanEndpoint]) {
    delete requestAbortControllers[cleanEndpoint];
  }
};

// Function to get onboarding context for mid-onboarding authentication
export const getOnboardingContext = (): { context: string; role: string } | null => {
  if (typeof window === 'undefined') return null;

  const context = localStorage.getItem('onboarding_context');
  const role = localStorage.getItem('onboarding_role');

  if (context && role) {
    return { context, role };
  }

  return null;
};

// Function to set onboarding context
export const setOnboardingContext = (context: string, role: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('onboarding_context', context);
    localStorage.setItem('onboarding_role', role);
    localStorage.setItem('onboarding_timestamp', Date.now().toString());
  }
};

// Function to clear onboarding context
export const clearOnboardingContext = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('onboarding_context');
    localStorage.removeItem('onboarding_role');
    localStorage.removeItem('onboarding_timestamp');
  }
};
