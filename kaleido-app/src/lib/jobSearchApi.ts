import apiHelper from './apiHelper';

export const searchJobs = async (params: any, user: any = null) => {
  // Default search parameters
  const {
    companyId,
    page = 1,
    limit = 50,
    filters,
    search,
    all = false,
    applied,
    postedByMe,
  } = params;

  let response: any;

  try {
    // If user is logged in, use the authenticated endpoint
    if (user?.sub) {
      try {
        // Prepare the request body for POST
        const requestBody = {
          page: Number(page),
          limit: Number(limit),
          all: all === true,
          applied: applied,
          postedByMe: postedByMe,
          search: search || undefined,
          filters: filters && Object.keys(filters).length > 0 ? filters : undefined,
        };

        // Check if user is an employer from localStorage
        if (postedByMe === undefined || postedByMe === null) {
          try {
            const userRoleKey = `userRole_${user.sub}`;
            const roleData = localStorage.getItem(userRoleKey);
            if (roleData) {
              const parsedData = JSON.parse(roleData);
              if (parsedData.role === 'employer') {
                requestBody.postedByMe = 'true';
              }
            }
          } catch (error) {
            console.error('Error checking user role:', error);
          }
        }

        // Call the authenticated endpoint using POST
        response = await apiHelper.post('/jobs/job-seeker/search', requestBody);
      } catch (error) {
        console.error('Error with authenticated endpoint:', error);
        // Fallback to public endpoint if the authenticated one fails
        response = await searchWithPublicEndpoint(companyId, page, limit, filters, search, all, user);
      }
    } else {
      // For public/anonymous users, use the public endpoint
      response = await searchWithPublicEndpoint(companyId, page, limit, filters, search, all, user);
    }

    // For authenticated users, add application status to each job
    if (user?.sub && response?.data && Array.isArray(response.data)) {
      // Get user applications
      const userApplications = await getUserApplications(user);
      const appliedJobIds = new Set(userApplications.map((app: any) => app.jobId));

      // Enhance the response with application status
      response.data = response.data.map((job: any) => ({
        ...job,
        hasApplied: job.hasApplied || appliedJobIds.has(job.id) || false,
        alreadyApplied: job.alreadyApplied || appliedJobIds.has(job.id) || false,
      }));
    }

    return response;
  } catch (error) {
    console.error('Error searching jobs:', error);
    // Return empty data structure on error
    return {
      data: [],
      metadata: {
        total: 0,
        filtered: 0,
        preferences: {
          jobTypes: [],
          departments: [],
          locations: [],
        },
      },
    };
  }
};

async function searchWithPublicEndpoint(
  companyId: string,
  page: number,
  limit: number,
  filters: any,
  search: string,
  all: boolean = false,
  user?: any
) {
  // Prepare the request body for POST
  const requestBody = {
    companyId: companyId || undefined,
    page: Number(page),
    limit: Number(limit),
    search: search || undefined,
    filters: filters && Object.keys(filters).length > 0 ? filters : undefined,
    userId: user?.sub || undefined, // Include userId for hasApplied check
  };

  // Call the open-jobs endpoint using POST
  return await apiHelper.post('/jobs/open-jobs', requestBody);
}

// In-memory cache for user applications to prevent excessive API calls
let userApplicationsCache: {
  data: any[];
  timestamp: number;
  userId: string;
} | null = null;

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

async function getUserApplications(user: any) {
  if (!user?.sub) return [];

  const now = Date.now();
  const cacheKey = 'user_applications';
  const timestampKey = 'user_applications_timestamp';

  try {
    // Check in-memory cache first (fastest)
    if (
      userApplicationsCache &&
      userApplicationsCache.userId === user.sub &&
      now - userApplicationsCache.timestamp < CACHE_DURATION
    ) {
      return userApplicationsCache.data;
    }

    // Check sessionStorage cache with timestamp validation
    const cachedData = sessionStorage.getItem(cacheKey);
    const cachedTimestamp = sessionStorage.getItem(timestampKey);

    if (cachedData && cachedTimestamp) {
      const cacheAge = now - parseInt(cachedTimestamp);
      if (cacheAge < CACHE_DURATION) {
        const applications = JSON.parse(cachedData);
        // Update in-memory cache
        userApplicationsCache = {
          data: applications,
          timestamp: parseInt(cachedTimestamp),
          userId: user.sub,
        };
        return applications;
      } else {
        // Cache expired, remove it
        sessionStorage.removeItem(cacheKey);
        sessionStorage.removeItem(timestampKey);
      }
    }

    // Fetch from API only if cache is invalid or expired
    const response = await apiHelper.get('/job-seekers/applications');
    const applications = response?.data || [];

    // Update both caches
    const timestamp = now.toString();
    sessionStorage.setItem(cacheKey, JSON.stringify(applications));
    sessionStorage.setItem(timestampKey, timestamp);

    userApplicationsCache = {
      data: applications,
      timestamp: now,
      userId: user.sub,
    };

    return applications;
  } catch (error) {
    console.error('Error fetching user applications:', error);
    // Return cached data if available, even if expired, as fallback
    if (userApplicationsCache && userApplicationsCache.userId === user.sub) {
      return userApplicationsCache.data;
    }
    return [];
  }
}

export const getJobById = async (jobId: string, user: any = null) => {
  try {
    const response = await apiHelper.get(`/jobs/${jobId}/public-application`);

    // Add application status if user is logged in
    if (user?.sub && response) {
      const userApplications = await getUserApplications(user);
      const hasApplied = userApplications.some((app: any) => app.jobId === jobId);
      return {
        ...response,
        hasApplied,
        alreadyApplied: hasApplied,
      };
    }

    return response;
  } catch (error) {
    console.error('Error fetching job:', error);
    return null;
  }
};

// Function to clear user applications cache (call when applications are updated)
export const clearUserApplicationsCache = () => {
  userApplicationsCache = null;
  sessionStorage.removeItem('user_applications');
  sessionStorage.removeItem('user_applications_timestamp');
};
