import { Target, TrendingUp, Bar<PERSON><PERSON>3, <PERSON>riefcase, DollarSign } from 'lucide-react';

export enum InsightType {
  SKILL_GAP_ANALYSIS = 'SKILL_GAP_ANALYSIS',
  CAREER_PATH_RECOMMENDATION = 'CAREER_PATH_RECOMMENDATION',
  MARKET_TREND_ANALYSIS = 'MARKET_TREND_ANALYSIS',
  ROLE_TRANSITION_GUIDANCE = 'ROLE_TRANSITION_GUIDANCE',
  COMPENSATION_BENCHMARK = 'COMPENSATION_BENCHMARK',
}

export enum InsightStatus {
  DRAFT = 'DRAFT',
  PROCESSING = 'PROCESSING',
  READY = 'READY',
  ARCHIVED = 'ARCHIVED',
}

export interface InsightFormField {
  id: string;
  label: string;
  type: 'text' | 'textarea' | 'radio';
  options?: string[];
  required?: boolean;
  placeholder?: string;
}

export interface InsightStep {
  id: string;
  title: string;
  description: string;
  fields: InsightF<PERSON><PERSON>ield[];
}

export const insightTypeConfig = {
  [InsightType.SKILL_GAP_ANALYSIS]: {
    title: 'Skill Gap',
    icon: Target,
    color: 'from-blue-500 to-cyan-500',
    bgColor: 'bg-blue-500/10',
    borderColor: 'border-blue-500/20',
    description: 'Identify skills you need to reach your career goals',
  },
  [InsightType.CAREER_PATH_RECOMMENDATION]: {
    title: 'Career Path',
    icon: TrendingUp,
    color: 'from-purple-500 to-pink-500',
    bgColor: 'bg-purple-500/10',
    borderColor: 'border-purple-500/20',
    description: 'Discover personalized career paths based on your profile',
  },
  [InsightType.MARKET_TREND_ANALYSIS]: {
    title: 'Market Trends',
    icon: BarChart3,
    color: 'from-green-500 to-emerald-500',
    bgColor: 'bg-green-500/10',
    borderColor: 'border-green-500/20',
    description: 'Stay updated with industry trends and opportunities',
  },
  [InsightType.ROLE_TRANSITION_GUIDANCE]: {
    title: 'Role Transition',
    icon: Briefcase,
    color: 'from-orange-500 to-red-500',
    bgColor: 'bg-orange-500/10',
    borderColor: 'border-orange-500/20',
    description: 'Get guidance for transitioning to your dream role',
  },
  [InsightType.COMPENSATION_BENCHMARK]: {
    title: 'Compensation',
    icon: DollarSign,
    color: 'from-yellow-500 to-amber-500',
    bgColor: 'bg-yellow-500/10',
    borderColor: 'border-yellow-500/20',
    description: 'Understand your market value and salary benchmarks',
  },
};

export const insightSteps: Record<InsightType, InsightStep[]> = {
  [InsightType.SKILL_GAP_ANALYSIS]: [
    {
      id: 'target',
      title: 'What role are you targeting?',
      description: 'Tell us about your career aspirations',
      fields: [
        {
          id: 'targetRole',
          label: 'Target Role',
          type: 'text',
          required: true,
          placeholder: 'e.g., Senior Software Engineer',
        },
        {
          id: 'targetCompany',
          label: 'Target Company (Optional)',
          type: 'text',
          required: false,
          placeholder: 'e.g., Google, Meta, or Any',
        },
      ],
    },
    {
      id: 'timeline',
      title: "What's your timeline?",
      description: 'When do you want to achieve this goal?',
      fields: [
        {
          id: 'timeline',
          label: 'Timeline',
          type: 'radio',
          options: ['Less than 3 months', '3-6 months', '6-12 months', 'More than 1 year'],
          required: true,
        },
      ],
    },
  ],
  [InsightType.CAREER_PATH_RECOMMENDATION]: [
    {
      id: 'interests',
      title: 'What are your interests?',
      description: 'Help us understand your career preferences',
      fields: [
        {
          id: 'interests',
          label: 'Your Interests',
          type: 'textarea',
          required: true,
          placeholder: 'Describe your professional interests and what motivates you',
        },
        {
          id: 'workStyle',
          label: 'Preferred Work Style',
          type: 'radio',
          options: ['Remote', 'Hybrid', 'On-site', 'Flexible'],
          required: true,
        },
      ],
    },
    {
      id: 'goals',
      title: 'What are your long-term goals?',
      description: 'Where do you see yourself in 5 years?',
      fields: [
        {
          id: 'longTermGoals',
          label: 'Long-term Career Goals',
          type: 'textarea',
          required: true,
          placeholder: 'Describe your career aspirations for the next 5 years',
        },
      ],
    },
  ],
  [InsightType.MARKET_TREND_ANALYSIS]: [
    {
      id: 'industry',
      title: 'Select your industry',
      description: 'Which industry are you interested in?',
      fields: [
        {
          id: 'industry',
          label: 'Industry',
          type: 'text',
          required: true,
          placeholder: 'e.g., Technology, Finance, Healthcare',
        },
        {
          id: 'location',
          label: 'Location',
          type: 'text',
          required: true,
          placeholder: 'e.g., San Francisco, Remote, Europe',
        },
      ],
    },
  ],
  [InsightType.ROLE_TRANSITION_GUIDANCE]: [
    {
      id: 'current',
      title: "What's your current role?",
      description: 'Tell us about your current position',
      fields: [
        {
          id: 'currentRole',
          label: 'Current Role',
          type: 'text',
          required: true,
          placeholder: 'e.g., Junior Developer',
        },
        {
          id: 'currentCompany',
          label: 'Current Company',
          type: 'text',
          required: false,
          placeholder: 'e.g., Startup XYZ',
        },
      ],
    },
    {
      id: 'target',
      title: 'What role do you want to transition to?',
      description: 'Describe your target position',
      fields: [
        {
          id: 'targetRole',
          label: 'Target Role',
          type: 'text',
          required: true,
          placeholder: 'e.g., Product Manager',
        },
        {
          id: 'whyTransition',
          label: 'Why do you want to transition?',
          type: 'textarea',
          required: true,
          placeholder: 'Explain your motivation for this career change',
        },
      ],
    },
  ],
  [InsightType.COMPENSATION_BENCHMARK]: [
    {
      id: 'current',
      title: 'Current position details',
      description: 'Help us benchmark your compensation',
      fields: [
        {
          id: 'currentRole',
          label: 'Current Role',
          type: 'text',
          required: true,
          placeholder: 'e.g., Senior Software Engineer',
        },
        {
          id: 'location',
          label: 'Location',
          type: 'text',
          required: true,
          placeholder: 'e.g., San Francisco, CA',
        },
        {
          id: 'experienceLevel',
          label: 'Experience Level',
          type: 'radio',
          options: ['Entry Level', 'Mid Level', 'Senior Level', 'Executive'],
          required: true,
        },
      ],
    },
  ],
};
