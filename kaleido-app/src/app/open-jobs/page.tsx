'use client';

// Import polyfills for Turbopack compatibility
import '@/lib/turbopack-polyfills';

import { useEffect, useState } from 'react';

import { useRouter } from 'next/navigation';

import { JobSeekerSetupSlider } from '@/components/JobSeeker/JobSeekerSetupSlider';
import { showToast } from '@/components/Toaster';
import { JobSearchProvider, useJobSearch } from '@/contexts/jobSearch/JobSearchContext';
import apiHelper from '@/lib/apiHelper';
import { useJobSearchStore } from '@/stores/jobSearchStore';
import { UserRole } from '@/types/roles';
import { useUser } from '@/hooks/useUser';
import { usePublicAuth } from '@/hooks/usePublicAuth';

import { HeroSection } from './components/HeroSection';
import { JobInfo } from './components/JobInfo';
import { JobSearchEmptyState } from './components/JobSearchEmptyState';
import { JobSkeleton } from './components/JobSkeleton';
import { OpenJobsList } from './components/OpenJobsList';
import { SearchFilters } from './components/SearchFilters';
// Import the dedicated styles for open jobs
import styles from './styles/openJobs.module.css';

// Safe router wrapper component
function SafeRouterComponent({ children }: { children: React.ReactNode }) {
  // No need to use router here, just checking if it's ready
  const [isRouterReady, setIsRouterReady] = useState(false);

  useEffect(() => {
    // Router is available when the component mounts
    setIsRouterReady(true);
  }, []);

  if (!isRouterReady) {
    return null; // Or a loading spinner
  }

  return <>{children}</>;
}

export default function OpenJobsPage() {
  // Auth state - using the improved public auth hook
  const { user, isLoading: userLoading } = usePublicAuth();
  const [userRole, setUserRole] = useState<string | null>(null);

  // Job seeker setup state
  const [showSetupSlider, setShowSetupSlider] = useState(false);
  const [profileValidation, setProfileValidation] = useState<any>(null);
  const [initialSliderData, setInitialSliderData] = useState<any>({});

  // Check user role and profile validation when user is loaded
  useEffect(() => {
    const checkUserRole = async () => {
      // Wait for auth to finish loading
      if (userLoading) {
        return;
      }

      // Check if we have a valid user with actual data
      const hasValidUser = user && user.sub && (user.name || user.email);

      if (hasValidUser) {
        try {
          const validationResponse = await apiHelper.post('/job-seekers/validate-profile', user);
          if (validationResponse) {
            // If validation response has a role, use it; otherwise infer from successful validation
            const detectedRole =
              validationResponse.role ||
              (validationResponse.record && validationResponse.isValid !== false
                ? UserRole.JOB_SEEKER
                : null);
            setUserRole(detectedRole);
            setInitialSliderData(validationResponse);
            setProfileValidation(validationResponse);

            // Only show setup slider for job seekers with invalid profiles
            if (
              !validationResponse.isValid &&
              (validationResponse.role === UserRole.JOB_SEEKER ||
                detectedRole === UserRole.JOB_SEEKER)
            ) {
              setShowSetupSlider(true);
            }
          }
        } catch (error) {
          console.error('Error checking user role:', error);
          // Don't let profile validation errors prevent the page from loading
        }
      }
    };

    checkUserRole();
  }, [user, userLoading]);

  // Handle job application
  const onJobApply = async (jobId: string) => {
    if (!user) {
      showToast({
        message: 'Please sign in to apply for jobs',
        type: 'info',
      });
      throw new Error('User not authenticated');
    }

    if (userRole === UserRole.EMPLOYER || userRole === UserRole.ADMIN) {
      showToast({
        message: 'Employers cannot apply for jobs. Switch to a job seeker account to apply.',
        type: 'error',
      });
      throw new Error('Employers cannot apply for jobs');
    }

    if (userRole !== UserRole.JOB_SEEKER && userRole !== UserRole.GRADUATE) {
      showToast({
        message: 'Only job seekers and graduates can apply for jobs',
        type: 'error',
      });
      throw new Error('Invalid user role for job application');
    }

    if (!profileValidation?.isValid) {
      setShowSetupSlider(true);
      throw new Error('Profile validation required');
    }

    try {
      await apiHelper.post(`/job-seekers/apply/${jobId}`, {});
      showToast({
        message: 'Your application has been submitted successfully',
        type: 'success',
      });
    } catch (error) {
      console.error('Error applying for job:', error);
      showToast({
        message: 'Failed to submit your application. Please try again.',
        type: 'error',
      });
      throw error; // Re-throw so ApplyButton knows it failed
    }
  };

  // Handle profile setup completion
  const onSetupComplete = () => {
    setShowSetupSlider(false);
  };

  return (
    <JobSearchProvider initialPublic={true}>
      <SafeRouterComponent>
        <div className="min-h-screen">
          {showSetupSlider && (
            <JobSeekerSetupSlider
              onClose={() => setShowSetupSlider(false)}
              onComplete={onSetupComplete}
              initialData={initialSliderData}
              validationResponse={profileValidation}
            />
          )}
          <OpenJobsContent
            showFilters={!showSetupSlider}
            onJobApply={onJobApply}
            userRole={userRole}
          />
        </div>
      </SafeRouterComponent>
    </JobSearchProvider>
  );
}

// Content component that consumes the JobSearch context
function OpenJobsContent({
  // showFilters is not used but kept for API compatibility
  showFilters: _,
  onJobApply,
  userRole,
}: {
  showFilters: boolean;
  onJobApply: (jobId: string) => Promise<void>;
  userRole: string | null;
}) {
  const router = useRouter();
  const {
    searchTerm,
    filters,
    setFilters,
    isPublicView,
    switchToPrivateView,
    showAllJobs,
    setSearchTerm,
    setShowAllJobs,
  } = useJobSearch();
  const { user } = useUser();
  const {
    jobs,
    selectedJobId,
    isLoading,
    totalJobs,
    availableFilters,
    fetchJobs,
    refreshJobs,
    getSearchParamsFromUrl,
  } = useJobSearchStore();

  // Switch to private view when user logs in
  useEffect(() => {
    if (user && isPublicView) {
      switchToPrivateView();
    }
  }, [user, isPublicView, switchToPrivateView]);

  // Refresh job data when user auth state changes
  useEffect(() => {
    if (jobs.length > 0) {
      refreshJobs(user);
    }
  }, [user?.sub, refreshJobs, jobs.length]);

  // Initialize state from URL parameters on mount
  useEffect(() => {
    const {
      searchTerm: urlSearchTerm,
      showAllJobs: urlShowAllJobs,
      filters: urlFilters,
    } = getSearchParamsFromUrl();

    // Update context state from URL parameters
    if (urlSearchTerm) setSearchTerm(urlSearchTerm);
    setShowAllJobs(urlShowAllJobs);

    // Convert to the expected type structure before setting filters
    if (Object.keys(urlFilters).length > 0) {
      const typedFilters = {
        location: urlFilters.location || '',
        salary: urlFilters.salary || '',
        currency: urlFilters.currency || '',
        jobType: urlFilters.jobType || '',
      };
      setFilters(typedFilters);
    }
  }, [getSearchParamsFromUrl, setSearchTerm, setShowAllJobs, setFilters]);

  // Fetch jobs when filters or search term change
  useEffect(() => {
    fetchJobs({
      filters: filters as unknown as Record<string, string>, // Safe type assertion since we control the filter values
      searchTerm,
      showAllJobs,
      user,
    });
  }, [filters, searchTerm, showAllJobs, user, fetchJobs]);

  // Get the selected job from the jobs array
  const selectedJob = jobs.find(job => job.id === selectedJobId);

  return (
    <div className={styles.openJobsContent}>
      {/* Hero Section with Overlay Search */}
      <HeroSection>
        <SearchFilters
          totalJobs={totalJobs}
          availableFilters={{
            ...availableFilters,
            industries: [], // Add required industries field
          }}
        />
      </HeroSection>

      {/* Job Listings Section */}
      <div className="bg-gradient-to-b from-gray-50/50 to-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {jobs.length === 0 && !isLoading ? (
            <JobSearchEmptyState
              totalJobs={totalJobs}
              hasActiveFilters={Boolean(
                searchTerm ||
                  filters.location ||
                  filters.salary ||
                  filters.currency ||
                  filters.jobType
              )}
              onClearFilters={() => {
                setSearchTerm('');
                setFilters({
                  location: '',
                  salary: '',
                  currency: '',
                  jobType: '',
                });
              }}
            />
          ) : (
            <div className="grid grid-cols-1 xl:grid-cols-5 gap-6 lg:gap-8">
              {/* Job List */}
              <div className="xl:col-span-2 space-y-4">
                {isLoading ? (
                  <div className="space-y-4">
                    {[...Array(5)].map((_, i) => (
                      <JobSkeleton key={i} type="list" />
                    ))}
                  </div>
                ) : (
                <OpenJobsList
                  jobs={jobs.map(
                    job =>
                      ({
                        ...job,
                        createdAt: job.createdAt?.toString(),
                        matchDetails: job.matchDetails
                          ? {
                              skillsMatch: {
                                score: job.matchDetails.skillsMatch?.score || 0,
                                feedback: 'Skills match information',
                              },
                              experienceMatch: {
                                score: job.matchDetails.experienceMatch?.score || 0,
                                feedback: 'Experience match information',
                              },
                              locationMatch: {
                                score: job.matchDetails.locationMatch?.score || 0,
                                feedback: 'Location match information',
                              },
                              matchFeedback: job.matchDetails.matchFeedback || '',
                            }
                          : undefined,
                      }) as any
                  )}
                  selectedJob={
                    selectedJob &&
                    ({
                      ...selectedJob,
                      createdAt: selectedJob.createdAt?.toString(),
                      matchDetails: selectedJob.matchDetails
                        ? {
                            skillsMatch: {
                              score: selectedJob.matchDetails.skillsMatch?.score || 0,
                              feedback: 'Skills match information',
                            },
                            experienceMatch: {
                              score: selectedJob.matchDetails.experienceMatch?.score || 0,
                              feedback: 'Experience match information',
                            },
                            locationMatch: {
                              score: selectedJob.matchDetails.locationMatch?.score || 0,
                              feedback: 'Location match information',
                            },
                            matchFeedback: selectedJob.matchDetails.matchFeedback || '',
                          }
                        : undefined,
                    } as any)
                  }
                  setSelectedJob={job => {
                    if (job) {
                      router.push(`${job.id}`, { scroll: false });
                    }
                  }}
                  onJobApply={onJobApply}
                  userRole={userRole}
                />
              )}
            </div>

              {/* Job Details - Enhanced layout */}
              <div className="hidden xl:block xl:col-span-3 bg-white rounded-2xl shadow-lg border border-gray-100" id="job-details">
                {selectedJob ? (
                  <div className="sticky top-8 max-h-[calc(100vh-6rem)] overflow-y-auto">
                    <JobInfo
                      job={{
                        ...selectedJob,
                        createdAt: selectedJob.createdAt?.toString(),
                      }}
                      onJobApply={onJobApply}
                      currentUserRole={userRole}
                    />
                  </div>
                ) : (
                  <div className="p-8 flex items-center justify-center h-96">
                    <div className="text-center space-y-4">
                      <div className="w-16 h-16 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full flex items-center justify-center mx-auto">
                        <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">Select a Job</h3>
                        <p className="text-gray-600 text-sm">Click on any job from the list to view details</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
