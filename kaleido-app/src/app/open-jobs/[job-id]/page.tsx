'use client';

import { useEffect, useState } from 'react';

import Image from 'next/image';
import { useParams, useRouter } from 'next/navigation';

import { JobSeekerSetupSlider } from '@/components/JobSeeker/JobSeekerSetupSlider';
import { showToast } from '@/components/Toaster';
import { JobSearchProvider } from '@/contexts/jobSearch/JobSearchContext';
import { getJobForPublicApplication } from '@/lib/api/jobs';
import apiHelper from '@/lib/apiHelper';
import { UserRole } from '@/types/roles';
import { useUser } from '@/hooks/useUser';
import { usePublicAuth } from '@/hooks/usePublicAuth';
import { LightUserProfileWidget } from '@/components/layout/LightUserProfileWidget';

import ColourfulLoader from '@/components/Layouts/ColourfulLoader';
import { JobInfo } from '../components/JobInfo';

interface JobDetailPageProps {}

export default function JobDetailPage({}: JobDetailPageProps) {
  const params = useParams();
  const router = useRouter();
  const { user, isLoading: userLoading, error: userError } = usePublicAuth();
  const jobId = params['job-id'] as string;

  // Job data state
  const [job, setJob] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // User profile state
  const [userRole, setUserRole] = useState<string | null>(null);
  const [showSetupSlider, setShowSetupSlider] = useState(false);
  const [profileValidation, setProfileValidation] = useState<any>(null);
  const [initialSliderData, setInitialSliderData] = useState<any>({});

  // Fetch job data
  useEffect(() => {
    const fetchJob = async () => {
      if (!jobId) return;

      try {
        setIsLoading(true);
        setError(null);

        // Use the public endpoint to fetch job details
        const jobData = await getJobForPublicApplication(jobId);

        if (jobData) {
          setJob(jobData);
        } else {
          setError('Job not found');
        }
      } catch (err) {
        console.error('Error fetching job:', err);
        setError('Failed to load job details. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchJob();
  }, [jobId]);

  // Check user role and profile validation when user is loaded
  useEffect(() => {
    const checkUserRole = async () => {
      // Wait for Auth0 to finish loading
      if (userLoading) {
        return;
      }

      // Check if we have a valid user with actual data
      const hasValidUser = user && user.sub && (user.name || user.email);

      if (hasValidUser) {
        try {
          const validationResponse = await apiHelper.post('/job-seekers/validate-profile', user);

          if (validationResponse) {
            // If validation response has a role, use it; otherwise infer from successful validation
            const userRole =
              validationResponse.role ||
              (validationResponse.record && validationResponse.isValid !== false
                ? UserRole.JOB_SEEKER
                : null);
            setUserRole(userRole);
            setInitialSliderData(validationResponse);
            setProfileValidation(validationResponse);
          }

          // Only show setup slider for job seekers with invalid profiles
          if (!validationResponse.isValid && userRole === UserRole.JOB_SEEKER) {
            setShowSetupSlider(true);
          }
        } catch (error) {
          console.error('Error checking user role:', error);
        }
      }
    };

    checkUserRole();
  }, [user, userLoading]);

  // Handle job application
  const onJobApply = async (jobId: string) => {
    if (!user) {
      showToast({
        message: 'Please sign in to apply for jobs',
        isSuccess: false,
      });
      throw new Error('User not authenticated');
    }

    if (userRole === UserRole.EMPLOYER || userRole === UserRole.ADMIN) {
      showToast({
        message: 'Employers cannot apply for jobs. Switch to a job seeker account to apply.',
        isSuccess: false,
      });
      throw new Error('Employers cannot apply for jobs');
    }

    if (userRole !== UserRole.JOB_SEEKER && userRole !== UserRole.GRADUATE) {
      showToast({
        message: 'Only job seekers and graduates can apply for jobs',
        isSuccess: false,
      });
      throw new Error('Invalid user role for job application');
    }

    if (!profileValidation?.isValid) {
      setShowSetupSlider(true);
      throw new Error('Profile validation required');
    }

    try {
      await apiHelper.post(`/job-seekers/apply/${jobId}`, {});
      showToast({
        message: 'Your application has been submitted successfully',
        isSuccess: true,
      });
    } catch (error) {
      console.error('Error applying for job:', error);
      showToast({
        message: 'Failed to submit your application. Please try again.',
        isSuccess: false,
      });
      throw error; // Re-throw so handleLocalApply knows it failed
    }
  };

  // Handle profile setup completion
  const onSetupComplete = () => {
    setShowSetupSlider(false);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <ColourfulLoader />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen flex flex-col">
        {/* Header with Logo */}
        <div className="max-w-6xl mx-auto w-full px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between mb-8 pb-6 border-b border-gray-200">
            <div className="flex-shrink-0">
              <Image
                src="/images/logos/kaleido-logo-full.webp"
                alt="Kaleido"
                width={140}
                height={45}
                className="h-10 w-auto"
                priority
              />
            </div>
            {/* User Profile Widget or Login Button */}
            {user && user.sub && (user.name || user.email) ? (
              <LightUserProfileWidget simplified={true} />
            ) : userLoading ? (
              <div className="flex items-center px-4 py-2 bg-gray-200 text-gray-600 rounded-lg animate-pulse">
                Loading...
              </div>
            ) : (
              <button
                type="button"
                onClick={() => {
                  const returnTo = encodeURIComponent(window.location.href);
                  router.push(`/api/auth/login?returnTo=${returnTo}`);
                }}
                className="flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors font-medium shadow-md"
              >
                Sign In
              </button>
            )}
          </div>
        </div>

        {/* Error Content */}
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Job Not Found</h1>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              type="button"
              onClick={() => router.push('/open-jobs')}
              className="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            >
              Browse All Jobs
            </button>
          </div>
        </div>
      </div>
    );
  }

  // No job found
  if (!job) {
    return (
      <div className="min-h-screen flex flex-col">
        {/* Header with Logo */}
        <div className="max-w-6xl mx-auto w-full px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between mb-8 pb-6 border-b border-gray-200">
            <div className="flex-shrink-0">
              <Image
                src="/images/logos/kaleido-logo-full.webp"
                alt="Kaleido"
                width={140}
                height={45}
                className="h-10 w-auto"
                priority
              />
            </div>
            {/* User Profile Widget or Login Button */}
            {user && user.sub && (user.name || user.email) ? (
              <LightUserProfileWidget simplified={true} />
            ) : userLoading ? (
              <div className="flex items-center px-4 py-2 bg-gray-200 text-gray-600 rounded-lg animate-pulse">
                Loading...
              </div>
            ) : (
              <button
                type="button"
                onClick={() => {
                  const returnTo = encodeURIComponent(window.location.href);
                  router.push(`/api/auth/login?returnTo=${returnTo}`);
                }}
                className="flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors font-medium shadow-md"
              >
                Sign In
              </button>
            )}
          </div>
        </div>

        {/* No Job Content */}
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Job Not Found</h1>
            <p className="text-gray-600 mb-6">
              The job you're looking for doesn't exist or has been removed.
            </p>
            <button
              type="button"
              onClick={() => router.push('/open-jobs')}
              className="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
            >
              Browse All Jobs
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <JobSearchProvider>
      <div className="min-h-screen">
        {showSetupSlider && (
          <JobSeekerSetupSlider
            onClose={() => setShowSetupSlider(false)}
            onComplete={onSetupComplete}
            initialData={initialSliderData}
            validationResponse={profileValidation}
          />
        )}

        {/* Main Content */}
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header with Logo, Back Button, and User Profile */}
          <div className="flex items-center justify-between mb-8 pb-6 border-b border-gray-200">
            {/* Logo */}
            <div className="flex-shrink-0">
              <Image
                src="/images/logos/kaleido-logo-full.webp"
                alt="Kaleido"
                width={140}
                height={45}
                className="h-10 w-auto"
                priority
              />
            </div>

            {/* Right Section: Back Button and User Profile */}
            <div className="flex items-center gap-4">
              {/* Back Button */}
              <button
                type="button"
                onClick={() => router.push('/open-jobs')}
                className="flex items-center px-4 py-2 text-indigo-600 hover:text-indigo-700 hover:bg-indigo-50 rounded-lg transition-all duration-200 font-medium"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
                Back to Jobs
              </button>

              {/* User Profile Widget - Only show when user is logged in */}
              {user && <LightUserProfileWidget simplified={true} />}
            </div>
          </div>

          {/* Job Details */}
          <JobInfo
            job={{
              ...job,
              createdAt: job.createdAt?.toString(),
            }}
            onJobApply={onJobApply}
            currentUserRole={userRole}
          />
        </div>
      </div>
    </JobSearchProvider>
  );
}
