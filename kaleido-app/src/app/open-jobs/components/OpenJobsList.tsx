'use client';

import { useEffect, useMemo, useState } from 'react';

import { BriefcaseIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';

import { JobCard } from '@/app/company-profile/[slug]/jobs/components/JobCard';
import { LayoutStyles } from '@/app/company-profile/[slug]/jobs/types';
import useUser from '@/hooks/useUser';
import { UserRole } from '@/shared/types';
import { useJobSearchStore } from '@/stores/jobSearchStore';

import styles from '../styles/openJobs.module.css';
import { JobData } from '../types';
import { MobileJobAccordion } from './MobileJobAccordion';

// Define default styles for cards - fixed styling independent of theme
const defaultStyles: LayoutStyles = {
  background: styles.jobCard,
  card: styles.jobCard,
  accent: 'bg-indigo-600',
  badge: 'bg-indigo-50',
  icon: 'text-indigo-600',
  iconColor: '#4F46E5',
};

// Helper function to smooth scroll to the expanded accordion on mobile
const scrollToAccordion = (jobId: string) => {
  // Only scroll if on mobile/tablet
  if (window.innerWidth < 1024) {
    // Use a slight delay to ensure DOM updates first
    setTimeout(() => {
      const accordionEl = document.getElementById(`job-accordion-${jobId}`);
      if (accordionEl) {
        accordionEl.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }
    }, 100);
  }
};

// Adapter function to convert from OpenJobs JobData to ProfileJobData
const adaptJobData = (job: JobData) => {
  const tldr = {
    jobType: job.tldr?.jobType || job.jobType || '',
    experience: job.tldr?.experience || job.experience || '',
    location: Array.isArray(job.tldr?.location)
      ? job.tldr.location
      : job.tldr?.location
        ? [job.tldr.location]
        : [],
    salary: job.tldr?.salary || job.salaryRange || '',
    typeOfJob: job.tldr?.typeOfJob || job.typeOfJob || '',
    typeOfHiring: job.tldr?.typeOfHiring || job.typeOfHiring || '',
    summary: job.tldr?.summary || '',
    keyPoints: [],
  };

  const adaptedJob = {
    ...job,
    // Required properties from Job type that may not exist in OpenJobs JobData
    companyName: job.company?.companyName || '',
    companyDescription: job.companyDescription || '',
    hiringManagerDescription: job.hiringManagerDescription || '',
    currency: '',
    paymentPeriod: '',
    status: '',
    finalDraft: job.finalDraft || '',
    requirements: job.requirements || [],
    typeOfHiring: job.typeOfHiring || 'EMPLOYMENT',
    typeOfJob: job.typeOfJob || 'FULL_TIME',
    jobResponsibilities: job.jobResponsibilities || [],
    cultureFitDescription: job.cultureFitDescription || '',
    cultureFitQuestions: (job.cultureFitQuestions || []).map(q => ({
      id: q.id || '',
      question: '',
      duration: q.duration || 0,
    })),
    companyValues: [],
    culturalFit: [],
    education: [],
    language: [],
    softSkills: [],
    location: Array.isArray(job.location) ? job.location : job.location ? [job.location] : [],
    benefits: [],
    careerGrowth: [],
    alreadyApplied: job.hasApplied || false,
    experience: job.experience || 'Not specified',
    salaryRange: job.salaryRange || 'Not specified',
    createdAt: new Date(job.createdAt),
    updatedAt: new Date(),
    tldr,
    // Add videoJD if it exists
    hasVideo:
      job.hasVideo ||
      job.videoUrl ||
      ((job as any).videoJD && (job as any).videoJD.videoUrl ? true : false),
    videoUrl: job.videoUrl || ((job as any).videoJD ? (job as any).videoJD.videoUrl : undefined),
    videoJDs: (job as any).videoJDs || ((job as any).videoJD ? [(job as any).videoJD] : []),
    matchDetails: job.matchDetails
      ? {
          ...job.matchDetails,
          skillsMatch: job.matchDetails.skillsMatch
            ? {
                ...job.matchDetails.skillsMatch,
                feedback: 'Skills match analysis',
              }
            : undefined,
          experienceMatch: job.matchDetails.experienceMatch
            ? {
                ...job.matchDetails.experienceMatch,
                feedback: 'Experience match analysis',
              }
            : undefined,
          locationMatch: job.matchDetails.locationMatch
            ? {
                ...job.matchDetails.locationMatch,
                feedback: 'Location match analysis',
              }
            : undefined,
        }
      : undefined,
  };

  return adaptedJob;
};

interface JobsListProps {
  jobs: JobData[];
  selectedJob: JobData | null;
  setSelectedJob: (job: JobData | null) => void;
  onJobApply: (jobId: string) => Promise<void>;
  userRole?: string | null;
}

export const OpenJobsList = ({ jobs, setSelectedJob, onJobApply, userRole }: JobsListProps) => {
  const router = useRouter();
  const { selectedJobId, setSelectedJobId, showAppliedJobs, setShowAppliedJobs } =
    useJobSearchStore();
  const { user } = useUser();

  // State to track which job accordion is expanded on mobile
  const [expandedJobId, setExpandedJobId] = useState<string | null>(null);
  // State to track if we're on mobile
  const [isMobile, setIsMobile] = useState(false);

  // Check if the current user is an employer
  const isEmployer = useMemo(() => {
    if (!user) return false;

    // Check direct role property
    if (user.role === UserRole.EMPLOYER) return true;

    // Check Auth0 namespaced roles
    if (user['https://headstart.com/roles']?.includes(UserRole.EMPLOYER)) return true;

    // Check localStorage for cached role
    try {
      if (user.sub) {
        const cachedRoleData = localStorage.getItem(`userRole_${user.sub}`);
        if (cachedRoleData) {
          const { role } = JSON.parse(cachedRoleData);
          if (role === UserRole.EMPLOYER) return true;
        }
      }
    } catch (error) {
      console.error('Error checking cached role:', error);
    }

    return false;
  }, [user]);

  // Check if we're on mobile when component mounts and when window resizes
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    // Initial check
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  // Update URL when checkbox changes
  const updateUrlParams = (isChecked: boolean) => {
    const urlSearchParams = new URLSearchParams(window.location.search);

    if (isChecked) {
      if (isEmployer) {
        urlSearchParams.set('postedByMe', 'true');
      } else {
        urlSearchParams.set('applied', 'false');
      }
    } else {
      if (isEmployer) {
        urlSearchParams.delete('postedByMe');
      } else {
        urlSearchParams.delete('applied');
      }
    }

    // Create the new URL with updated parameters
    const newUrl = `${window.location.pathname}?${urlSearchParams.toString()}`;
    // App Router doesn't need shallow option - it's optimized by default
    router.push(newUrl);
  };

  const handleFilterToggle = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked;
    setShowAppliedJobs(!isChecked); // Invert the value since we're now hiding applied jobs
    updateUrlParams(isChecked);

    // Refresh the jobs list with the new filter
    const urlParams = new URLSearchParams(window.location.search);
    const searchTerm = urlParams.get('search') || '';
    const showAllJobs = urlParams.get('all') === 'true';

    // Create filters object from URL params
    const filters: Record<string, string> = {};
    ['location', 'jobType', 'salary', 'currency', 'department', 'industry', 'companySize'].forEach(
      key => {
        const value = urlParams.get(key);
        if (value) filters[key] = value;
      }
    );

    // Fetch jobs with updated filters
    useJobSearchStore.getState().fetchJobs({
      filters,
      searchTerm,
      showAllJobs,
      showAppliedJobs: !isChecked, // Invert the value for the store
      applied: isChecked ? false : null, // Send 'false' when hiding applied jobs
      user,
    });
  };

  return (
    <div className="lg:col-span-4 space-y-4">
      {/* Filter Checkbox - Only show for logged in users */}
      {user && !isEmployer && (
        <div className="flex justify-start mb-4 w-full">
          <label className="flex items-center gap-2 cursor-pointer w-full">
            <input
              type="checkbox"
              checked={!showAppliedJobs} // Invert the checked state
              onChange={handleFilterToggle}
              className="form-checkbox h-4 w-4 text-indigo-600 rounded border-gray-300 focus:ring-indigo-500"
            />
            <div className="flex items-center justify-between w-full">
              <span className="flex items-center gap-1.5 text-black text-sm font-medium">
                <BriefcaseIcon className="h-4 w-4" />
                Hide jobs I have applied for
              </span>
              <span className="text-sm text-gray-500 ml-4 font-medium">{jobs.length} jobs</span>
            </div>
          </label>
        </div>
      )}

      <div>
        {jobs.map(job => {
          // Adapt the job data for the JobCard component
          const adaptedJob = adaptJobData(job);

          // Preserve hasApplied property if it exists
          if (job.hasApplied) {
            adaptedJob.alreadyApplied = job.hasApplied;
          }

          // Preserve matchPercentage if it exists
          if (job.matchPercentage) {
            adaptedJob.matchPercentage = job.matchPercentage;
            // matchDetails is already properly adapted in adaptJobData
          }

          // Prepare the company data for the card
          const company = {
            id: job.company?.id || '',
            clientId: job.company?.clientId || '',
            companyName: job.company?.companyName || '',
            logo: job.company?.logo || null,
          };

          // Check if this job's accordion is expanded
          const isAccordionExpanded = expandedJobId === job.id;

          // Toggle function for the accordion
          const toggleAccordion = () => {
            const newExpandedId = isAccordionExpanded ? null : job.id;
            setExpandedJobId(newExpandedId);

            // If we're expanding the accordion, scroll to it
            if (newExpandedId && isMobile) {
              scrollToAccordion(job.id);
            }
          };

          return (
            <div key={job.id} className="mb-4">
              <div className={`overflow-hidden ${isAccordionExpanded ? 'shadow-md' : ''}`}>
                <JobCard
                  job={adaptedJob}
                  company={company}
                  styles={defaultStyles}
                  isSelected={selectedJobId === job.id}
                  onSelect={profileJob => {
                    // Convert back to original JobData type when handling selection
                    const originalJob = jobs.find(j => j.id === profileJob.id);
                    if (originalJob) {
                      // On mobile, toggle the accordion instead of scrolling
                      if (isMobile) {
                        toggleAccordion();
                      } else {
                        // On desktop, use the original behavior
                        setSelectedJob(originalJob);
                        setSelectedJobId(originalJob.id);
                        // Use router directly for Next.js 15 compatibility
                        router.push(`${originalJob.id}`);
                      }
                    }
                  }}
                  slug={job.company?.id || ''}
                />

                {/* Mobile Accordion for Job Details - Inside the card */}
                {isMobile && (
                  <MobileJobAccordion
                    job={{
                      ...job,
                      createdAt: job.createdAt?.toString(),
                    }}
                    isOpen={isAccordionExpanded}
                    onToggle={toggleAccordion}
                    onJobApply={onJobApply}
                    userRole={userRole}
                  />
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
