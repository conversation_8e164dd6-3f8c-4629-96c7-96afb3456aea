'use client';

import React, { useEffect } from 'react';

import { AnimatePresence, motion } from 'framer-motion';
import { Filter, Grid3x3, MapPin, Search, Sparkles, X } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';

import { useJobSearch } from '@/contexts/jobSearch/JobSearchContext';
import useUser from '@/hooks/useUser';
import { useJobSearchStore } from '@/stores/jobSearchStore';
import {
  AcademicCapIcon,
  BriefcaseIcon,
  BuildingLibraryIcon,
  BuildingOfficeIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline';

// Filter options for dropdowns
const EXPERIENCE_LEVELS = ['Entry Level', 'Mid Level', 'Senior Level', 'Executive'];
const JOB_TYPES = ['Full-time', 'Part-time', 'Contract', 'Temporary', 'Internship', 'Remote'];
const INDUSTRY_TYPES = [
  'Technology',
  'Healthcare',
  'Finance',
  'Education',
  'Retail',
  'Manufacturing',
];
const COMPANY_SIZES = ['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+'];

// Interface for filter state is managed by the JobSearchContext

interface SearchFiltersProps {
  totalJobs: number;
  availableFilters: {
    jobTypes: string[];
    departments: string[];
    locations: string[];
    industries: string[];
    companySizes: string[];
  };
}

export const SearchFilters = ({ totalJobs, availableFilters }: SearchFiltersProps) => {
  const [showFilters, setShowFilters] = React.useState(false);
  const { searchTerm, setSearchTerm, filters, setFilters, showAllJobs, setShowAllJobs } =
    useJobSearch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useUser();
  const { isEligibleToApply } = useJobSearchStore();
  const initializedFromUrl = React.useRef(false);

  // Local state for search input - only updates context on submit
  const [localSearchTerm, setLocalSearchTerm] = React.useState(searchTerm);

  // Update URL when filters change
  const updateUrlParams = (params: Record<string, string | null>) => {
    const urlSearchParams = new URLSearchParams(searchParams.toString());

    // Update or remove parameters
    Object.entries(params).forEach(([key, value]) => {
      if (value === null) {
        urlSearchParams.delete(key);
      } else {
        urlSearchParams.set(key, value);
      }
    });

    // App Router doesn't need shallow option - it's optimized by default
    router.push(`${window.location.pathname}?${urlSearchParams.toString()}`);
  };

  // Initialize from URL params on component mount
  useEffect(() => {
    // Only run this effect once to prevent infinite loops
    if (initializedFromUrl.current) return;

    const all = searchParams.get('all');
    if (all !== null) {
      setShowAllJobs(all === 'true');
    }

    const search = searchParams.get('search');
    if (search !== null) {
      setSearchTerm(search);
      setLocalSearchTerm(search);
    }

    const location = searchParams.get('location');
    const jobType = searchParams.get('jobType');
    const salary = searchParams.get('salary');
    const currency = searchParams.get('currency');
    const department = searchParams.get('department');
    const experienceLevel = searchParams.get('experienceLevel');
    const industry = searchParams.get('industry');
    const companySize = searchParams.get('companySize');

    // Update filters from URL params
    if (
      location ||
      jobType ||
      salary ||
      currency ||
      department ||
      experienceLevel ||
      industry ||
      companySize
    ) {
      const newFilters = { ...filters };
      if (location) newFilters.location = location;
      if (jobType) newFilters.jobType = jobType;
      if (salary) newFilters.salary = salary;
      if (currency) newFilters.currency = currency;
      if (department) newFilters.department = department;
      if (experienceLevel) newFilters.experienceLevel = experienceLevel;
      if (industry) newFilters.industry = industry;
      if (companySize) newFilters.companySize = companySize;
      setFilters(newFilters);
    }

    // Mark as initialized
    initializedFromUrl.current = true;
  }, [searchParams, setShowAllJobs, setSearchTerm, setFilters]);

  // Check user eligibility when component mounts
  useEffect(() => {
    if (user?.sub) {
      // Use the checkUserEligibility function from jobSearchStore
      const { checkUserEligibility } = useJobSearchStore.getState();
      checkUserEligibility(user.sub);
    }
  }, [user]);

  // Sync local search term when context search term changes (e.g., from clear filters)
  useEffect(() => {
    setLocalSearchTerm(searchTerm);
  }, [searchTerm]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Update the context search term with local value
    setSearchTerm(localSearchTerm);
    // Apply all filters and search
    applyFilters();
  };

  const handleFilterChange = (key: string, value: string | number | null) => {
    // Update local filters state without triggering API call
    const newFilters = { ...filters };

    if (key === 'location') {
      newFilters.location = (value as string) || '';
    } else if (key === 'jobType') {
      newFilters.jobType = (value as string) || '';
    } else if (key === 'salary') {
      newFilters.salary = (value as string) || '';
    } else if (key === 'currency') {
      newFilters.currency = (value as string) || '';
    } else if (key === 'department') {
      newFilters.department = (value as string) || '';
    } else if (key === 'experienceLevel') {
      newFilters.experienceLevel = (value as string) || '';
    } else if (key === 'industry') {
      newFilters.industry = (value as string) || '';
    } else if (key === 'companySize') {
      newFilters.companySize = (value as string) || '';
    }

    setFilters(newFilters);
  };

  const applyFilters = () => {
    // Create a new URLSearchParams instance based on the current search params
    const params = new URLSearchParams(searchParams?.toString() || '');

    // Update all filter params
    Object.entries(filters).forEach(([key, value]) => {
      if (value === null || value === '') {
        params.delete(key);
      } else {
        params.set(key, String(value));
      }
    });

    // Add search term if present
    if (localSearchTerm) {
      params.set('search', localSearchTerm);
    } else {
      params.delete('search');
    }

    // Reset to page 1 when filters change
    params.delete('page');

    // Build the new URL with updated search parameters
    const newUrl = window.location.pathname + '?' + params.toString();

    // App Router doesn't need shallow option - it's optimized by default
    router.push(newUrl);
  };

  const clearFilters = () => {
    setFilters({
      location: '',
      salary: '',
      currency: '',
      jobType: '',
      department: '',
      experienceLevel: '',
      industry: '',
      companySize: '',
    });
    setSearchTerm('');
    setLocalSearchTerm('');

    // Clear all filter params from URL
    const params = new URLSearchParams(searchParams?.toString() || '');
    params.delete('location');
    params.delete('salary');
    params.delete('currency');
    params.delete('jobType');
    params.delete('department');
    params.delete('experienceLevel');
    params.delete('industry');
    params.delete('companySize');
    params.delete('search');
    params.delete('page');

    const newUrl = window.location.pathname + (params.toString() ? '?' + params.toString() : '');
    router.push(newUrl);
  };

  // Helper function to count active filters
  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.location) count++;
    if (filters.jobType) count++;
    if (filters.department) count++;
    if (filters.experienceLevel) count++;
    if (filters.industry) count++;
    if (filters.companySize) count++;
    return count;
  };

  // Helper function to get active filter labels with their keys
  const getActiveFilters = () => {
    const activeFilters: { key: string; label: string }[] = [];
    if (filters.location) activeFilters.push({ key: 'location', label: filters.location });
    if (filters.jobType) activeFilters.push({ key: 'jobType', label: filters.jobType });
    if (filters.department) activeFilters.push({ key: 'department', label: filters.department });
    if (filters.experienceLevel)
      activeFilters.push({ key: 'experienceLevel', label: filters.experienceLevel });
    if (filters.industry) activeFilters.push({ key: 'industry', label: filters.industry });
    if (filters.companySize)
      activeFilters.push({ key: 'companySize', label: `${filters.companySize} employees` });
    return activeFilters;
  };

  // Helper function to remove a single filter
  const removeFilter = (filterKey: string) => {
    const newFilters = { ...filters };
    // Reset the specific filter
    if (filterKey === 'location') newFilters.location = '';
    else if (filterKey === 'jobType') newFilters.jobType = '';
    else if (filterKey === 'department') newFilters.department = '';
    else if (filterKey === 'experienceLevel') newFilters.experienceLevel = '';
    else if (filterKey === 'industry') newFilters.industry = '';
    else if (filterKey === 'companySize') newFilters.companySize = '';

    setFilters(newFilters);

    // Update URL immediately
    const params = new URLSearchParams(searchParams?.toString() || '');
    params.delete(filterKey);
    params.delete('page'); // Reset pagination

    const newUrl = window.location.pathname + '?' + params.toString();
    router.push(newUrl);
  };

  return (
    <div className="w-full space-y-4">
      {/* Header Section */}
      <div className="text-center space-y-3">

        {/* Job Type Toggle */}
        {user && isEligibleToApply !== false && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="flex justify-center"
          >
            <div className="flex bg-white/10 backdrop-blur-md rounded-full p-1 border border-white/20">
              <motion.button
                type="button"
                onClick={() => {
                  setShowAllJobs(false);
                  updateUrlParams({ all: null });
                }}
                className={`relative px-5 py-2.5 rounded-full text-sm font-medium transition-all duration-300 ${
                  !showAllJobs
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                    : 'text-white/70 hover:text-white hover:bg-white/10'
                }`}
                whileTap={{ scale: 0.95 }}
              >
                <div className="flex items-center gap-2">
                  <Sparkles className="h-4 w-4" />
                  <span>For You</span>
                </div>
              </motion.button>
              <motion.button
                type="button"
                onClick={() => {
                  setShowAllJobs(true);
                  updateUrlParams({ all: 'true' });
                }}
                className={`relative px-5 py-2.5 rounded-full text-sm font-medium transition-all duration-300 ${
                  showAllJobs
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
                    : 'text-white/70 hover:text-white hover:bg-white/10'
                }`}
                whileTap={{ scale: 0.95 }}
              >
                <div className="flex items-center gap-2">
                  <Grid3x3 className="h-4 w-4" />
                  <span>All Jobs</span>
                </div>
              </motion.button>
            </div>
          </motion.div>
        )}
      </div>

      {/* Glassmorphic Search Bar */}
      <div className="bg-white/20 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/30 p-4 bg-gradient-to-r from-white/10 to-white/5">
        <form onSubmit={handleSearch} className="flex flex-col lg:flex-row gap-3">
          {/* Search Input */}
          <div className="flex-1 relative group">
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-purple-600 transition-colors">
              <Search className="h-5 w-5" />
            </div>
            <input
              type="text"
              placeholder="Search job title, company, or keywords..."
              value={localSearchTerm}
              onChange={e => setLocalSearchTerm(e.target.value)}
              onKeyDown={e => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleSearch(e);
                }
              }}
              className="w-full pl-12 pr-4 py-4 rounded-xl bg-white/80 backdrop-blur-sm border-2 border-white/50 focus:bg-white/90 focus:border-white/70 focus:outline-none transition-all duration-200 text-gray-900 placeholder:text-gray-600"
            />
          </div>

          {/* Location Input */}
          <div className="flex-1 lg:max-w-xs relative group">
            <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-purple-600 transition-colors">
              <MapPin className="h-5 w-5" />
            </div>
            <input
              type="text"
              placeholder="City, state, or remote"
              value={filters.location}
              onChange={e => handleFilterChange('location', e.target.value)}
              onKeyDown={e => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  handleSearch(e);
                }
              }}
              className="w-full pl-12 pr-4 py-4 rounded-xl bg-white/80 backdrop-blur-sm border-2 border-white/50 focus:bg-white/90 focus:border-white/70 focus:outline-none transition-all duration-200 text-gray-900 placeholder:text-gray-600"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <motion.button
              type="submit"
              className="flex-1 lg:flex-initial bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-4 rounded-xl font-semibold flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transition-all duration-200"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Search className="h-5 w-5" />
              <span>Search</span>
            </motion.button>

            <motion.button
              type="button"
              className={`relative flex items-center justify-center px-6 py-4 rounded-xl font-medium transition-all duration-200 ${
                showFilters
                  ? 'bg-purple-100 text-purple-700 border-2 border-purple-200'
                  : 'bg-white text-gray-700 border-2 border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => setShowFilters(!showFilters)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Filter className="h-5 w-5" />
              <span className="hidden sm:inline ml-2">Filters</span>
              {getActiveFiltersCount() > 0 && (
                <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-bold">
                  {getActiveFiltersCount()}
                </span>
              )}
            </motion.button>
          </div>
        </form>
      </div>

      {/* Active filters display - Modern pills */}
      <AnimatePresence>
        {getActiveFiltersCount() > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-4"
          >
            <div className="flex flex-wrap items-center gap-2">
              <span className="text-sm text-white/80 font-medium">Active:</span>
              {getActiveFilters().map(filter => (
                <motion.span
                  key={filter.key}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full bg-white/20 backdrop-blur-sm text-white border border-white/30 hover:border-white/50 transition-all group"
                >
                  <span className="text-sm font-medium">{filter.label}</span>
                  <button
                    type="button"
                    onClick={() => removeFilter(filter.key)}
                    className="text-white/80 hover:text-white transition-colors"
                  >
                    <X className="h-3.5 w-3.5" />
                  </button>
                </motion.span>
              ))}
              <motion.button
                type="button"
                onClick={clearFilters}
                className="px-3 py-1.5 rounded-full text-sm font-medium text-white/70 hover:text-white hover:bg-white/10 transition-all"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Clear all
              </motion.button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Collapsible filters section - Modern design */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="mt-4 bg-white/90 backdrop-blur-md rounded-2xl shadow-lg border border-white/30 p-6"
          >
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {/* Job Type Filter */}
              <div className="space-y-2">
                <label
                  htmlFor="jobType"
                  className="flex items-center text-sm font-semibold text-gray-700"
                >
                  <BriefcaseIcon className="h-4 w-4 mr-2 text-purple-600" />
                  Job Type
                </label>
                <select
                  id="jobType"
                  value={filters.jobType}
                  onChange={e => handleFilterChange('jobType', e.target.value)}
                  className="w-full rounded-lg border-2 border-gray-200 px-4 py-2.5 text-sm focus:border-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-100 text-gray-900 bg-white hover:border-gray-300 transition-colors"
                >
                  <option value="">All Types</option>
                  {availableFilters.jobTypes?.length > 0
                    ? availableFilters.jobTypes.map((type: string) => (
                        <option key={type} value={type}>
                          {type}
                        </option>
                      ))
                    : JOB_TYPES.map(type => (
                        <option key={type} value={type}>
                          {type}
                        </option>
                      ))}
                </select>
              </div>

              {/* Department Filter */}
              <div className="space-y-2">
                <label
                  htmlFor="department"
                  className="flex items-center text-sm font-semibold text-gray-700"
                >
                  <BuildingOfficeIcon className="h-4 w-4 mr-2 text-purple-600" />
                  Department
                </label>
                <select
                  id="department"
                  value={filters.department || ''}
                  onChange={e => handleFilterChange('department', e.target.value)}
                  className="w-full rounded-lg border-2 border-gray-200 px-4 py-2.5 text-sm focus:border-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-100 text-gray-900 bg-white hover:border-gray-300 transition-colors"
                >
                  <option value="">All Departments</option>
                  {availableFilters.departments?.map((dept: string) => (
                    <option key={dept} value={dept}>
                      {dept}
                    </option>
                  ))}
                </select>
              </div>

              {/* Experience Level Filter */}
              <div className="space-y-2">
                <label
                  htmlFor="experienceLevel"
                  className="flex items-center text-sm font-semibold text-gray-700"
                >
                  <AcademicCapIcon className="h-4 w-4 mr-2 text-purple-600" />
                  Experience
                </label>
                <select
                  id="experienceLevel"
                  value={filters.experienceLevel || ''}
                  onChange={e => handleFilterChange('experienceLevel', e.target.value)}
                  className="w-full rounded-lg border-2 border-gray-200 px-4 py-2.5 text-sm focus:border-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-100 text-gray-900 bg-white hover:border-gray-300 transition-colors"
                >
                  <option value="">All Levels</option>
                  {EXPERIENCE_LEVELS.map(level => (
                    <option key={level} value={level}>
                      {level}
                    </option>
                  ))}
                </select>
              </div>

              {/* Industry Filter */}
              <div className="space-y-2">
                <label
                  htmlFor="industry"
                  className="flex items-center text-sm font-semibold text-gray-700"
                >
                  <BuildingLibraryIcon className="h-4 w-4 mr-2 text-purple-600" />
                  Industry
                </label>
                <select
                  id="industry"
                  value={filters.industry || ''}
                  onChange={e => handleFilterChange('industry', e.target.value)}
                  className="w-full rounded-lg border-2 border-gray-200 px-4 py-2.5 text-sm focus:border-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-100 text-gray-900 bg-white hover:border-gray-300 transition-colors"
                >
                  <option value="">All Industries</option>
                  {availableFilters.industries?.length > 0
                    ? availableFilters.industries.map((industry: string) => (
                        <option key={industry} value={industry}>
                          {industry}
                        </option>
                      ))
                    : INDUSTRY_TYPES.map(industry => (
                        <option key={industry} value={industry}>
                          {industry}
                        </option>
                      ))}
                </select>
              </div>

              {/* Company Size Filter */}
              <div className="space-y-2">
                <label
                  htmlFor="companySize"
                  className="flex items-center text-sm font-semibold text-gray-700"
                >
                  <UserGroupIcon className="h-4 w-4 mr-2 text-purple-600" />
                  Company Size
                </label>
                <select
                  id="companySize"
                  value={filters.companySize || ''}
                  onChange={e => handleFilterChange('companySize', e.target.value)}
                  className="w-full rounded-lg border-2 border-gray-200 px-4 py-2.5 text-sm focus:border-purple-300 focus:outline-none focus:ring-2 focus:ring-purple-100 text-gray-900 bg-white hover:border-gray-300 transition-colors"
                >
                  <option value="">All Sizes</option>
                  {availableFilters.companySizes?.length > 0
                    ? availableFilters.companySizes.map((size: string) => (
                        <option key={size} value={size}>
                          {size}
                        </option>
                      ))
                    : COMPANY_SIZES.map(size => (
                        <option key={size} value={size}>
                          {size}
                        </option>
                      ))}
                </select>
              </div>

              {/* Apply and Clear Filters Buttons */}
              <div className="col-span-1 sm:col-span-2 lg:col-span-3 xl:col-span-4 mt-6 pt-6 border-t border-gray-100">
                <div className="flex flex-col sm:flex-row gap-3">
                  <motion.button
                    type="button"
                    onClick={applyFilters}
                    className="flex-1 flex items-center justify-center px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-lg shadow-md hover:shadow-lg text-sm font-semibold transition-all"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    Apply Filters
                  </motion.button>
                  <motion.button
                    type="button"
                    onClick={clearFilters}
                    className="flex-1 flex items-center justify-center px-6 py-3 border-2 border-gray-200 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-300 transition-all"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <X className="h-4 w-4 mr-2" />
                    <span>Reset All</span>
                  </motion.button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
