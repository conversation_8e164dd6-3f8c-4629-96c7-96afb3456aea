'use client';

import React from 'react';

import styles from '../styles/openJobs.module.css';
import { Header } from './Header';
import { HeroCarousel } from './HeroCarousel';

interface HeroSectionProps {
  children?: React.ReactNode;
}

export const HeroSection = ({ children }: HeroSectionProps) => {
  return (
    <div className={`relative ${styles.heroSection}`}>
      <Header />
      <div className="relative">
        <HeroCarousel totalJobs={0} />
        {/* Overlay container for search filters */}
        {children && (
          <div className="absolute inset-0 z-50 flex items-center justify-center">
            <div className="w-full max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mt-32">
              {children}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
