'use client';

import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDown } from 'lucide-react';

import { JobInfo } from './JobInfo';

interface MobileJobAccordionProps {
  job: any;
  isOpen: boolean;
  onToggle: () => void;
  onJobApply: (jobId: string) => Promise<void>;
  userRole?: string | null;
}

export const MobileJobAccordion = ({
  job,
  isOpen,
  onToggle,
  onJobApply,
  userRole,
}: MobileJobAccordionProps) => {
  // Create an ID for this accordion for scrolling purposes
  const accordionId = `job-accordion-${job.id}`;
  return (
    <div id={accordionId} className="lg:hidden border-t border-gray-100">
      <div className="flex items-center justify-center py-2">
        <button
          type="button"
          onClick={onToggle}
          className="flex items-center text-gray-500 hover:text-indigo-600 text-sm font-medium transition-colors duration-200"
        >
          <span>{isOpen ? 'Hide Details' : 'View Details'}</span>
          <ChevronDown
            className={`ml-1 h-4 w-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
          />
        </button>
      </div>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="px-4 pb-4">
              <JobInfo job={job} onJobApply={onJobApply} currentUserRole={userRole} />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
