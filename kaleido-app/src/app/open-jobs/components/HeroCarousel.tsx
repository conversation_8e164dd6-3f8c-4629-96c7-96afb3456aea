'use client';

import { useEffect, useState } from 'react';

import { AnimatePresence, motion } from 'framer-motion';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import Image from 'next/image';

// Define the slide data structure
export interface CarouselSlide {
  id: number;
  title: string;
  subtitle: string;
  highlightWords: string[];
  description: string;
  imagePath: string;
}

// Create the carousel content
export const carouselSlides: CarouselSlide[] = [
  {
    id: 1,
    title: 'Discover Your Perfect Match',
    subtitle: 'AI-Powered Job Matching',
    highlightWords: ['Perfect Match', 'AI-Powered'],
    description:
      'Our intelligent algorithm finds roles that align with your unique skills and experience, eliminating irrelevant opportunities.',
    imagePath: '/images/landing/open-jobs/open-jobs-1.webp',
  },
  {
    id: 2,
    title: 'See Beyond the Description',
    subtitle: 'Interactive Video Job Previews',
    highlightWords: ['Beyond', 'Video'],
    description:
      'Experience the workplace culture and meet potential colleagues through our immersive video job descriptions.',
    imagePath: '/images/landing/open-jobs/open-jobs-2.webp',
  },
  {
    id: 3,
    title: 'Track Your Journey',
    subtitle: 'Real-time Application Status',
    highlightWords: ['Track', 'Real-time'],
    description:
      'Stay informed with transparent updates on your application progress, from submission to final decision.',
    imagePath: '/images/landing/open-jobs/open-jobs-3.webp',
  },
  {
    id: 4,
    title: 'Context-Aware Applications',
    subtitle: 'Tailored to Each Opportunity',
    highlightWords: ['Context-Aware', 'Tailored'],
    description:
      'Showcase the most relevant aspects of your experience for each position, increasing your chances of success.',
    imagePath: '/images/landing/open-jobs/open-jobs-4.webp',
  },
  {
    id: 5,
    title: 'Quality Over Quantity',
    subtitle: 'Curated Job Opportunities',
    highlightWords: ['Quality', 'Curated'],
    description:
      'Browse through carefully vetted positions from top employers, no spam or irrelevant listings.',
    imagePath: '/images/landing/open-jobs/open-jobs-5.webp',
  },
  {
    id: 6,
    title: 'Your Career, Simplified',
    subtitle: 'Streamlined Job Search Experience',
    highlightWords: ['Simplified', 'Streamlined'],
    description:
      'Navigate your job search with ease through our intuitive interface designed to save you time and reduce stress.',
    imagePath: '/images/landing/open-jobs/open-jobs-6.webp',
  },
  {
    id: 7,
    title: 'Personalized Final Summary',
    subtitle: 'Jobs That Fit Your Profile',
    highlightWords: ['Personalized', 'Fit'],
    description:
      'Receive suggestions tailored to your career goals, skills, and preferences, helping you find your ideal role faster.',
    imagePath: '/images/landing/open-jobs/open-jobs-7.webp',
  },
  {
    id: 8,
    title: 'Complete Transparency',
    subtitle: 'Full Visibility Into Your Applications',
    highlightWords: ['Transparency', 'Visibility'],
    description:
      'Access detailed insights about your application status and feedback to continuously improve your job search strategy.',
    imagePath: '/images/landing/open-jobs/open-jobs-8.webp',
  },
];

interface HeroCarouselProps {
  totalJobs: number;
}

export const HeroCarousel = ({ totalJobs }: HeroCarouselProps) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [slides, setSlides] = useState<CarouselSlide[]>([]);
  const [direction, setDirection] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);

  // Randomize slides on initial load
  useEffect(() => {
    const shuffledSlides = [...carouselSlides].sort(() => Math.random() - 0.5);
    setSlides(shuffledSlides);
  }, []);

  // Auto-advance slides
  useEffect(() => {
    if (!isAutoPlaying || slides.length === 0) return;

    const interval = setInterval(() => {
      setDirection(1);
      setCurrentIndex(prevIndex => (prevIndex + 1) % slides.length);
    }, 6000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, slides.length, currentIndex]);

  // Handle navigation
  const handlePrevious = () => {
    setIsAutoPlaying(false);
    setDirection(-1);
    setCurrentIndex(prevIndex => (prevIndex - 1 + slides.length) % slides.length);
  };

  const handleNext = () => {
    setIsAutoPlaying(false);
    setDirection(1);
    setCurrentIndex(prevIndex => (prevIndex + 1) % slides.length);
  };

  // Handle touch events for swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (touchStart - touchEnd > 50) {
      // Swipe left
      handleNext();
    }

    if (touchStart - touchEnd < -50) {
      // Swipe right
      handlePrevious();
    }
  };

  // Variants for framer-motion animations - updated for smoother transitions
  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? '100%' : '-100%',
      opacity: 0.5,
      position: 'absolute' as const,
      width: '100%',
      height: '100%',
    }),
    center: {
      x: 0,
      opacity: 1,
      position: 'relative' as const,
      width: '100%',
      height: '100%',
    },
    exit: (direction: number) => ({
      x: direction < 0 ? '100%' : '-100%',
      opacity: 0.5,
      position: 'absolute' as const,
      width: '100%',
      height: '100%',
    }),
  };

  if (slides.length === 0) {
    return <div className="min-h-[500px] flex items-center justify-center">Loading...</div>;
  }

  const currentSlide = slides[currentIndex];

  return (
    <div
      className="relative min-h-[450px] w-full overflow-hidden"
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Carousel container with images */}
      <div className="absolute inset-0 z-10">
        <AnimatePresence initial={false} custom={direction} mode="sync">
          <motion.div
            key={currentIndex}
            custom={direction}
            variants={slideVariants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{
              x: { type: 'spring', stiffness: 300, damping: 30 },
              opacity: { duration: 0.2 },
            }}
            className="w-full h-full"
          >
            {currentSlide && (
              <div className="relative w-full h-full">
                <Image
                  src={currentSlide.imagePath}
                  alt={currentSlide.title}
                  fill
                  priority
                  className="object-cover object-top"
                  sizes="100vw"
                  onError={() => {
                    if (process.env.NODE_ENV !== 'production') {
                      console.error(`Failed to load image: ${currentSlide.imagePath}`);
                    }
                  }}
                />
                {/* Soft gradient overlay for bottom edge that blends into white background */}
                <div className="absolute bottom-0 left-0 right-0 h-[15vh] bg-gradient-to-t from-white via-white/5 to-transparent z-20"></div>
                {/* Elegant caption at top right after header */}
                <motion.div
                  className="absolute z-30 top-20 right-4 sm:right-6 lg:right-8"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.4 }}
                >
                  <div className="flex items-center gap-2">
                    <div className="w-0.5 h-3.5 bg-gradient-to-b from-purple-400 via-pink-400 to-purple-500"></div>
                    <p className="text-white text-xs font-light tracking-wide">
                      {currentSlide.subtitle}
                    </p>
                  </div>
                </motion.div>
              </div>
            )}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Modern Navigation Controls */}
      <div className="absolute right-4 lg:right-8 bottom-6 z-40 flex items-center gap-3">
        {/* Navigation buttons */}
        <div className="flex items-center gap-2 bg-black/20 backdrop-blur-sm rounded-full p-1 border border-white/20">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={handlePrevious}
            className="p-2 rounded-full bg-white/20 hover:bg-white/30 text-white transition-all"
            aria-label="Previous slide"
          >
            <ChevronLeft className="w-4 h-4" />
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={handleNext}
            className="p-2 rounded-full bg-white/20 hover:bg-white/30 text-white transition-all"
            aria-label="Next slide"
          >
            <ChevronRight className="w-4 h-4" />
          </motion.button>
        </div>

        {/* Pagination indicators */}
        <div className="flex items-center gap-1.5 bg-black/20 backdrop-blur-sm rounded-full px-3 py-2 border border-white/20">
          {slides.map((_, index) => (
            <motion.button
              key={index}
              onClick={() => {
                setIsAutoPlaying(false);
                setDirection(index > currentIndex ? 1 : -1);
                setCurrentIndex(index);
              }}
              className={`h-1.5 rounded-full transition-all duration-300 ${
                index === currentIndex 
                  ? 'bg-gradient-to-r from-purple-400 to-pink-400 w-6' 
                  : 'bg-white/40 w-1.5 hover:bg-white/60'
              }`}
              whileHover={{ scale: 1.2 }}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      </div>

      {/* Smooth bottom gradient transition */}
      <div className="absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-white via-white/90 to-transparent z-40" />
    </div>
  );
};
