'use client';

import { motion, useScroll, useTransform } from 'framer-motion';

import useEnhancedUserData from '@/hooks/useEnhancedUserData';
import { useUser } from '@/hooks/useUser';
import { usePublicAuth } from '@/hooks/usePublicAuth';
import { UserRole } from '@/types/roles';
import { UserCircleIcon } from '@heroicons/react/24/outline';
import { LightUserProfileWidget } from '@/components/layout/LightUserProfileWidget';
import Image from 'next/image';
import Link from 'next/link';

export const Header = () => {
  const { user, isLoading } = useUser();
  const { user: publicUser, isLoading: publicUserLoading } = usePublicAuth();
  const { userData } = useEnhancedUserData();
  const { scrollY } = useScroll();
  const backgroundColor = useTransform(
    scrollY,
    [0, 100],
    ['rgba(255, 255, 255, 0)', 'rgba(255, 255, 255, 1)']
  );
  const textColor = useTransform(
    scrollY,
    [0, 100],
    ['rgba(255, 255, 255, 1)', 'rgba(55, 65, 81, 1)']
  );
  const hoverColor = useTransform(
    scrollY,
    [0, 100],
    ['rgba(236, 72, 153, 1)', 'rgba(219, 39, 119, 1)'] // pink-300 to pink-600
  );
  const shadowOpacity = useTransform(scrollY, [0, 100], [0, 0.1]);

  // Determine dashboard URL based on authoritative backend user role
  const getDashboardUrl = () => {
    if (!user) return '/dashboard';

    // Use the authoritative role from backend instead of Auth0 user claims
    const role = userData?.userRole || user.role || user['https://headstart.com/roles']?.[0];

    if (role === UserRole.EMPLOYER || role === UserRole.ADMIN || role === UserRole.SUPER_ADMIN)
      return '/dashboard';
    if (role === UserRole.JOB_SEEKER) return '/dashboard';
    if (role === UserRole.GRADUATE) return '/graduate/dashboard';

    return '/dashboard';
  };

  return (
    <>
      <motion.header
        className="fixed top-0 left-0 right-0 z-50"
        style={{
          backgroundColor,
          boxShadow: shadowOpacity.get()
            ? `0 1px 3px rgba(0, 0, 0, ${shadowOpacity.get()})`
            : 'none',
        }}
      >
        <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-18">
            <div className="flex items-center">
              <Link href="/" className="flex items-center group">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: 'spring', stiffness: 400, damping: 10 }}
                  className="relative"
                >
                  <Image
                    src={'/images/logos/kaleido-logo-full.webp'}
                    alt={'Company logo'}
                    width={120}
                    height={40}
                    className="h-10 w-auto transition-all duration-200"
                  />
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-400/10 to-pink-400/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                </motion.div>
              </Link>
            </div>

            <div className="flex items-center space-x-3">
              {/* Use publicUser for better session detection */}
              {!publicUserLoading &&
              publicUser &&
              publicUser.sub &&
              (publicUser.name || publicUser.email) ? (
                <>
                  {/* Dashboard Button - clean styling */}
                  <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                    <Link
                      href={getDashboardUrl()}
                      className="flex items-center px-4 py-2.5 rounded-lg hover:bg-white/10 transition-all duration-200"
                      style={{ color: textColor.get() }}
                    >
                      <UserCircleIcon className="h-4 w-4 mr-2" />
                      <span className="hidden sm:inline font-medium text-sm">Dashboard</span>
                    </Link>
                  </motion.div>

                  {/* Profile Widget - clean */}
                  <LightUserProfileWidget simplified={true} />
                </>
              ) : publicUserLoading ? (
                <div className="flex items-center px-4 py-2.5 text-gray-600 rounded-lg animate-pulse">
                  <div className="w-4 h-4 bg-gray-300 rounded mr-2 animate-pulse"></div>
                  <span className="text-sm">Loading...</span>
                </div>
              ) : (
                <>
                  <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                    <Link
                      href="/job-seeker-holding?from=open-jobs"
                      className="px-4 py-2.5 rounded-lg font-semibold text-sm transition-all duration-200 hover:bg-white/10"
                      style={{ color: textColor.get() }}
                    >
                      Log In
                    </Link>
                  </motion.div>
                  <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                    <Link
                      href="/job-seeker-holding?from=open-jobs"
                      className="text-white inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-purple-600 via-pink-600 to-purple-700 rounded-xl font-semibold text-sm hover:from-purple-700 hover:via-pink-700 hover:to-purple-800 transition-all duration-300 shadow-lg hover:shadow-xl"
                    >
                      <span>Get Started</span>
                      <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </motion.div>
                </>
              )}
            </div>
          </div>
        </nav>
      </motion.header>
    </>
  );
};
