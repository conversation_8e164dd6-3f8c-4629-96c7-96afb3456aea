/**
 * Open Jobs Global Styles
 * 
 * This file contains all global styling for the open-jobs page and its children,
 * completely separate from the main application's global.css and theming system.
 * 
 * This ensures the open-jobs page maintains its own visual identity
 * regardless of the user's theme preference or global styles.
 */

/* Import only Tailwind utilities - we'll define our own base and components */
@tailwind utilities;

/* =================================================================
   OPEN JOBS CSS RESET & BASE STYLES
   ================================================================= */

/* Scoped reset for open-jobs page - more selective to preserve necessary spacing */
[data-page='open-jobs'] * {
  box-sizing: border-box;
}

/* Reset margins for specific elements that commonly need it */
[data-page='open-jobs'] h1,
[data-page='open-jobs'] h2,
[data-page='open-jobs'] h3,
[data-page='open-jobs'] h4,
[data-page='open-jobs'] h5,
[data-page='open-jobs'] h6,
[data-page='open-jobs'] p,
[data-page='open-jobs'] ul,
[data-page='open-jobs'] ol,
[data-page='open-jobs'] blockquote,
[data-page='open-jobs'] figure,
[data-page='open-jobs'] pre {
  margin: 0;
}

/* Reset padding only for elements that don't need default padding */
[data-page='open-jobs'] ul,
[data-page='open-jobs'] ol {
  padding-left: 0;
  list-style: none;
}

/* Preserve button and input padding by default - they will be overridden by our component classes */
[data-page='open-jobs'] button,
[data-page='open-jobs'] input,
[data-page='open-jobs'] textarea,
[data-page='open-jobs'] select {
  margin: 0;
  /* Don't reset padding for form elements - let component styles handle it */
}

[data-page='open-jobs'] {
  /* Base font and color settings */
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell',
    'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  color: #111827;
  background-color: #f9fafb;
}

/* =================================================================
   OPEN JOBS CSS VARIABLES
   ================================================================= */

[data-page='open-jobs'] {
  /* Color Palette */
  --oj-primary-50: #f0f9ff;
  --oj-primary-100: #e0f2fe;
  --oj-primary-200: #bae6fd;
  --oj-primary-300: #7dd3fc;
  --oj-primary-400: #38bdf8;
  --oj-primary-500: #0ea5e9;
  --oj-primary-600: #0284c7;
  --oj-primary-700: #0369a1;
  --oj-primary-800: #075985;
  --oj-primary-900: #0c4a6e;

  /* Secondary Colors (Purple/Indigo) */
  --oj-secondary-50: #eef2ff;
  --oj-secondary-100: #e0e7ff;
  --oj-secondary-200: #c7d2fe;
  --oj-secondary-300: #a5b4fc;
  --oj-secondary-400: #818cf8;
  --oj-secondary-500: #6366f1;
  --oj-secondary-600: #4f46e5;
  --oj-secondary-700: #4338ca;
  --oj-secondary-800: #3730a3;
  --oj-secondary-900: #312e81;

  /* Accent Colors (Pink) */
  --oj-accent-50: #fdf2f8;
  --oj-accent-100: #fce7f3;
  --oj-accent-200: #fbcfe8;
  --oj-accent-300: #f9a8d4;
  --oj-accent-400: #f472b6;
  --oj-accent-500: #ec4899;
  --oj-accent-600: #db2777;
  --oj-accent-700: #be185d;
  --oj-accent-800: #9d174d;
  --oj-accent-900: #831843;

  /* Gray Scale */
  --oj-gray-50: #f9fafb;
  --oj-gray-100: #f3f4f6;
  --oj-gray-200: #e5e7eb;
  --oj-gray-300: #d1d5db;
  --oj-gray-400: #9ca3af;
  --oj-gray-500: #6b7280;
  --oj-gray-600: #4b5563;
  --oj-gray-700: #374151;
  --oj-gray-800: #1f2937;
  --oj-gray-900: #111827;

  /* Success/Error States */
  --oj-success-50: #f0fdf4;
  --oj-success-500: #22c55e;
  --oj-success-600: #16a34a;
  --oj-success-700: #15803d;

  --oj-error-50: #fef2f2;
  --oj-error-500: #ef4444;
  --oj-error-600: #dc2626;
  --oj-error-700: #b91c1c;

  --oj-warning-50: #fffbeb;
  --oj-warning-500: #f59e0b;
  --oj-warning-600: #d97706;
  --oj-warning-700: #b45309;

  /* Semantic Variables */
  --oj-text-primary: var(--oj-gray-900);
  --oj-text-secondary: var(--oj-gray-600);
  --oj-text-muted: var(--oj-gray-500);
  --oj-text-light: var(--oj-gray-400);
  --oj-text-white: #ffffff;

  --oj-bg-primary: #ffffff;
  --oj-bg-secondary: var(--oj-gray-50);
  --oj-bg-muted: var(--oj-gray-100);
  --oj-bg-overlay: rgba(0, 0, 0, 0.5);

  --oj-border-light: var(--oj-gray-200);
  --oj-border-medium: var(--oj-gray-300);
  --oj-border-dark: var(--oj-gray-400);

  /* Component Specific Variables */
  --oj-card-bg: var(--oj-bg-primary);
  --oj-card-border: var(--oj-border-light);
  --oj-card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --oj-card-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  --oj-button-radius: 0.5rem;
  --oj-input-radius: 0.375rem;
  --oj-card-radius: 0.75rem;

  /* Spacing Scale */
  --oj-space-1: 0.25rem;
  --oj-space-2: 0.5rem;
  --oj-space-3: 0.75rem;
  --oj-space-4: 1rem;
  --oj-space-5: 1.25rem;
  --oj-space-6: 1.5rem;
  --oj-space-8: 2rem;
  --oj-space-10: 2.5rem;
  --oj-space-12: 3rem;
  --oj-space-16: 4rem;
  --oj-space-20: 5rem;
  --oj-space-24: 6rem;
}

/* =================================================================
   TYPOGRAPHY
   ================================================================= */

[data-page='open-jobs'] h1,
[data-page='open-jobs'] h2,
[data-page='open-jobs'] h3,
[data-page='open-jobs'] h4,
[data-page='open-jobs'] h5,
[data-page='open-jobs'] h6 {
  font-weight: 700;
  line-height: 1.2;
  color: var(--oj-text-primary);
  margin-bottom: var(--oj-space-4);
}

[data-page='open-jobs'] h1 {
  font-size: 2.25rem; /* 36px */
  line-height: 2.5rem; /* 40px */
}

[data-page='open-jobs'] h2 {
  font-size: 1.875rem; /* 30px */
  line-height: 2.25rem; /* 36px */
}

[data-page='open-jobs'] h3 {
  font-size: 1.5rem; /* 24px */
  line-height: 2rem; /* 32px */
}

[data-page='open-jobs'] h4 {
  font-size: 1.25rem; /* 20px */
  line-height: 1.75rem; /* 28px */
}

[data-page='open-jobs'] h5 {
  font-size: 1.125rem; /* 18px */
  line-height: 1.75rem; /* 28px */
}

[data-page='open-jobs'] h6 {
  font-size: 1rem; /* 16px */
  line-height: 1.5rem; /* 24px */
}

[data-page='open-jobs'] p {
  color: var(--oj-text-secondary);
  line-height: 1.625; /* 26px */
  margin-bottom: var(--oj-space-4);
}

[data-page='open-jobs'] span {
  color: inherit;
}

[data-page='open-jobs'] small {
  font-size: 0.875rem; /* 14px */
  color: var(--oj-text-muted);
}

[data-page='open-jobs'] strong {
  font-weight: 600;
  color: var(--oj-text-primary);
}

/* =================================================================
   BUTTONS
   ================================================================= */

[data-page='open-jobs'] .oj-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--oj-space-2);
  padding: var(--oj-space-3) var(--oj-space-6);
  border-radius: var(--oj-button-radius);
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  transition: all 0.15s ease-in-out;
  border: 1px solid transparent;
  cursor: pointer;
  white-space: nowrap;
  text-decoration: none;
}

[data-page='open-jobs'] .oj-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Primary Button */
[data-page='open-jobs'] .oj-btn-primary {
  background: linear-gradient(to right, var(--oj-secondary-600), var(--oj-secondary-700));
  color: var(--oj-text-white);
  box-shadow: var(--oj-card-shadow);
}

[data-page='open-jobs'] .oj-btn-primary:hover:not(:disabled) {
  background: linear-gradient(to right, var(--oj-secondary-500), var(--oj-secondary-600));
  box-shadow: var(--oj-card-shadow-lg);
  transform: translateY(-1px);
}

/* Secondary Button */
[data-page='open-jobs'] .oj-btn-secondary {
  background-color: var(--oj-bg-primary);
  color: var(--oj-text-primary);
  border-color: var(--oj-border-medium);
}

[data-page='open-jobs'] .oj-btn-secondary:hover:not(:disabled) {
  background-color: var(--oj-bg-muted);
}

/* Accent Button */
[data-page='open-jobs'] .oj-btn-accent {
  background: linear-gradient(to right, var(--oj-accent-600), var(--oj-accent-700));
  color: var(--oj-text-white);
  box-shadow: var(--oj-card-shadow);
}

[data-page='open-jobs'] .oj-btn-accent:hover:not(:disabled) {
  background: linear-gradient(to right, var(--oj-accent-500), var(--oj-accent-600));
  box-shadow: var(--oj-card-shadow-lg);
  transform: translateY(-1px);
}

/* Button Sizes */
[data-page='open-jobs'] .oj-btn-sm {
  padding: var(--oj-space-2) var(--oj-space-4);
  font-size: 0.75rem;
}

[data-page='open-jobs'] .oj-btn-lg {
  padding: var(--oj-space-4) var(--oj-space-8);
  font-size: 1rem;
}

/* =================================================================
   FORMS & INPUTS
   ================================================================= */

[data-page='open-jobs'] .oj-input,
[data-page='open-jobs'] .oj-textarea,
[data-page='open-jobs'] .oj-select {
  width: 100%;
  padding: var(--oj-space-3);
  border: 1px solid var(--oj-border-medium);
  border-radius: var(--oj-input-radius);
  background-color: var(--oj-bg-primary);
  color: var(--oj-text-primary);
  font-size: 0.875rem;
  transition:
    border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
}

[data-page='open-jobs'] .oj-input:focus,
[data-page='open-jobs'] .oj-textarea:focus,
[data-page='open-jobs'] .oj-select:focus {
  outline: none;
  border-color: var(--oj-secondary-500);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

[data-page='open-jobs'] .oj-input::placeholder,
[data-page='open-jobs'] .oj-textarea::placeholder {
  color: var(--oj-text-light);
}

[data-page='open-jobs'] .oj-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--oj-text-primary);
  margin-bottom: var(--oj-space-2);
}

/* =================================================================
   CARDS & CONTAINERS
   ================================================================= */

[data-page='open-jobs'] .oj-card {
  background-color: var(--oj-card-bg);
  border: 1px solid var(--oj-card-border);
  border-radius: var(--oj-card-radius);
  box-shadow: var(--oj-card-shadow);
  padding: var(--oj-space-6);
  transition:
    box-shadow 0.15s ease-in-out,
    transform 0.15s ease-in-out;
}

[data-page='open-jobs'] .oj-card:hover {
  box-shadow: var(--oj-card-shadow-lg);
}

[data-page='open-jobs'] .oj-card-interactive {
  cursor: pointer;
}

[data-page='open-jobs'] .oj-card-interactive:hover {
  transform: translateY(-2px);
}

[data-page='open-jobs'] .oj-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--oj-space-6);
}

/* =================================================================
   UTILITY CLASSES
   ================================================================= */

[data-page='open-jobs'] .oj-text-primary {
  color: var(--oj-text-primary);
}
[data-page='open-jobs'] .oj-text-secondary {
  color: var(--oj-text-secondary);
}
[data-page='open-jobs'] .oj-text-muted {
  color: var(--oj-text-muted);
}
[data-page='open-jobs'] .oj-text-white {
  color: var(--oj-text-white);
}
[data-page='open-jobs'] .oj-text-success {
  color: var(--oj-success-600);
}
[data-page='open-jobs'] .oj-text-error {
  color: var(--oj-error-600);
}
[data-page='open-jobs'] .oj-text-warning {
  color: var(--oj-warning-600);
}

[data-page='open-jobs'] .oj-bg-primary {
  background-color: var(--oj-bg-primary);
}
[data-page='open-jobs'] .oj-bg-secondary {
  background-color: var(--oj-bg-secondary);
}
[data-page='open-jobs'] .oj-bg-muted {
  background-color: var(--oj-bg-muted);
}

[data-page='open-jobs'] .oj-border {
  border: 1px solid var(--oj-border-light);
}
[data-page='open-jobs'] .oj-border-medium {
  border: 1px solid var(--oj-border-medium);
}
[data-page='open-jobs'] .oj-border-dark {
  border: 1px solid var(--oj-border-dark);
}

[data-page='open-jobs'] .oj-shadow {
  box-shadow: var(--oj-card-shadow);
}
[data-page='open-jobs'] .oj-shadow-lg {
  box-shadow: var(--oj-card-shadow-lg);
}

[data-page='open-jobs'] .oj-rounded {
  border-radius: var(--oj-input-radius);
}
[data-page='open-jobs'] .oj-rounded-lg {
  border-radius: var(--oj-card-radius);
}

/* Flex utilities */
[data-page='open-jobs'] .oj-flex {
  display: flex;
}
[data-page='open-jobs'] .oj-flex-col {
  flex-direction: column;
}
[data-page='open-jobs'] .oj-items-center {
  align-items: center;
}
[data-page='open-jobs'] .oj-justify-center {
  justify-content: center;
}
[data-page='open-jobs'] .oj-justify-between {
  justify-content: space-between;
}
[data-page='open-jobs'] .oj-gap-2 {
  gap: var(--oj-space-2);
}
[data-page='open-jobs'] .oj-gap-4 {
  gap: var(--oj-space-4);
}
[data-page='open-jobs'] .oj-gap-6 {
  gap: var(--oj-space-6);
}

/* Spacing utilities */
[data-page='open-jobs'] .oj-m-4 {
  margin: var(--oj-space-4);
}
[data-page='open-jobs'] .oj-p-4 {
  padding: var(--oj-space-4);
}
[data-page='open-jobs'] .oj-p-6 {
  padding: var(--oj-space-6);
}
[data-page='open-jobs'] .oj-mb-4 {
  margin-bottom: var(--oj-space-4);
}
[data-page='open-jobs'] .oj-mt-8 {
  margin-top: var(--oj-space-8);
}

/* Additional spacing utilities for common patterns */
[data-page='open-jobs'] .oj-space-y-2 > * + * {
  margin-top: var(--oj-space-2);
}
[data-page='open-jobs'] .oj-space-y-3 > * + * {
  margin-top: var(--oj-space-3);
}
[data-page='open-jobs'] .oj-space-y-4 > * + * {
  margin-top: var(--oj-space-4);
}
[data-page='open-jobs'] .oj-space-y-6 > * + * {
  margin-top: var(--oj-space-6);
}

/* Default spacing for common components */
[data-page='open-jobs'] .oj-job-card {
  padding: var(--oj-space-6);
  margin-bottom: var(--oj-space-4);
}

[data-page='open-jobs'] .oj-button-group {
  gap: var(--oj-space-3);
}

/* Job card specific styling */
[data-page='open-jobs'] .oj-card.oj-card-interactive {
  margin-bottom: var(--oj-space-3);
}

/* Ensure proper spacing for job card content */
[data-page='open-jobs'] .oj-card .oj-flex {
  align-items: flex-start;
  gap: var(--oj-space-4);
}

[data-page='open-jobs'] .oj-card h3 {
  margin-bottom: var(--oj-space-2);
}

[data-page='open-jobs'] .oj-card p {
  margin-bottom: var(--oj-space-3);
}

/* Badge and tag styling */
[data-page='open-jobs'] .oj-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--oj-space-1) var(--oj-space-3);
  border-radius: var(--oj-input-radius);
  font-size: 0.75rem;
  font-weight: 500;
  background-color: var(--oj-secondary-100);
  color: var(--oj-secondary-700);
}

/* Ensure list items have proper spacing */
[data-page='open-jobs'] li {
  margin-bottom: var(--oj-space-2);
}

[data-page='open-jobs'] li:last-child {
  margin-bottom: 0;
}

/* =================================================================
   COMPONENT OVERRIDES FOR THIRD-PARTY LIBRARIES
   ================================================================= */

/* Framer Motion components */
[data-page='open-jobs'] [data-framer-component] {
  color: inherit;
}

/* React components that might inherit global styles */
[data-page='open-jobs'] .fixed,
[data-page='open-jobs'] .absolute {
  color: inherit;
}

/* Ensure modals and overlays work correctly */
[data-page='open-jobs'] .modal-overlay {
  background-color: var(--oj-bg-overlay);
}

/* Application Reason Modal Fixes */
[data-page='open-jobs'] .fixed {
  color: var(--oj-text-primary);
}

/* Ensure modal content has proper contrast */
[data-page='open-jobs'] .bg-white\/50,
[data-page='open-jobs'] .bg-white\/70 {
  color: var(--oj-text-primary);
}

/* Fix text colors in modal backgrounds */
[data-page='open-jobs'] .text-gray-700,
[data-page='open-jobs'] .text-gray-800,
[data-page='open-jobs'] .text-gray-900 {
  color: var(--oj-text-primary);
}

[data-page='open-jobs'] .text-gray-500,
[data-page='open-jobs'] .text-gray-600 {
  color: var(--oj-text-secondary);
}

/* Ensure modal form elements have proper styling */
[data-page='open-jobs'] .bg-gray-50 {
  background-color: var(--oj-bg-muted);
  color: var(--oj-text-primary);
}

[data-page='open-jobs'] .bg-white {
  background-color: var(--oj-bg-primary);
  color: var(--oj-text-primary);
}

/* Fix input and textarea styling within modals */
[data-page='open-jobs'] textarea,
[data-page='open-jobs'] input {
  background-color: var(--oj-bg-primary);
  color: var(--oj-text-primary);
  border-color: var(--oj-border-medium);
}

[data-page='open-jobs'] textarea::placeholder,
[data-page='open-jobs'] input::placeholder {
  color: var(--oj-text-light);
}

/* Specific fixes for ApplicationReasonModal text visibility */
[data-page='open-jobs'] .text-sm {
  color: var(--oj-text-secondary);
}

[data-page='open-jobs'] .text-xs {
  color: var(--oj-text-muted);
}

[data-page='open-jobs'] .font-semibold {
  color: var(--oj-text-primary);
}

/* Modal content area text color fixes */
[data-page='open-jobs'] .p-5 p,
[data-page='open-jobs'] .p-5 span,
[data-page='open-jobs'] .p-4 span,
[data-page='open-jobs'] .space-y-4 p {
  color: var(--oj-text-secondary);
}

/* Modal headers and important text */
[data-page='open-jobs'] .p-5 .font-semibold,
[data-page='open-jobs'] .p-4 .font-medium {
  color: var(--oj-text-primary);
}

/* Chart center text */
[data-page='open-jobs'] .absolute .text-4xl,
[data-page='open-jobs'] .absolute .text-xs {
  color: inherit;
}

/* List items in recommendations */
[data-page='open-jobs'] .space-y-2\.5 .text-xs {
  color: var(--oj-text-secondary);
}

/* Label text */
[data-page='open-jobs'] .block.text-xs {
  color: var(--oj-text-primary);
}

/* Force text visibility in modal overlay content */
[data-page='open-jobs'] .fixed.inset-0 * {
  color: var(--oj-text-primary);
}

[data-page='open-jobs'] .fixed.inset-0 .text-white {
  color: var(--oj-text-white);
}

[data-page='open-jobs'] .fixed.inset-0 .text-yellow-400 {
  color: #fbbf24;
}

[data-page='open-jobs'] .fixed.inset-0 .text-red-400 {
  color: #f87171;
}

[data-page='open-jobs'] .fixed.inset-0 .text-purple-600 {
  color: var(--oj-secondary-600);
}

/* Override all generic text size classes in modal */
[data-page='open-jobs'] .fixed .text-sm,
[data-page='open-jobs'] .fixed .text-xs,
[data-page='open-jobs'] .fixed .text-md {
  color: var(--oj-text-primary);
}

/* Ensure spans inside modal content are visible */
[data-page='open-jobs'] .fixed span:not([class*='text-']) {
  color: var(--oj-text-primary);
}

/* Chart score text visibility */
[data-page='open-jobs'] .fixed .absolute span {
  color: inherit;
}

/* =================================================================
   RESPONSIVE DESIGN
   ================================================================= */

/* Mobile First Approach */
@media (min-width: 640px) {
  [data-page='open-jobs'] {
    --oj-space-container: var(--oj-space-8);
  }
}

@media (min-width: 768px) {
  [data-page='open-jobs'] {
    --oj-space-container: var(--oj-space-12);
  }

  [data-page='open-jobs'] h1 {
    font-size: 3rem; /* 48px */
    line-height: 1;
  }

  [data-page='open-jobs'] h2 {
    font-size: 2.25rem; /* 36px */
    line-height: 2.5rem; /* 40px */
  }
}

@media (min-width: 1024px) {
  [data-page='open-jobs'] {
    --oj-space-container: var(--oj-space-16);
  }
}

@media (min-width: 1280px) {
  [data-page='open-jobs'] {
    --oj-space-container: var(--oj-space-20);
  }
}

/* =================================================================
   ANIMATION & TRANSITIONS
   ================================================================= */

[data-page='open-jobs'] .oj-fade-in {
  animation: oj-fadeIn 0.3s ease-out forwards;
}

[data-page='open-jobs'] .oj-slide-up {
  animation: oj-slideUp 0.3s ease-out forwards;
}

@keyframes oj-fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes oj-slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* =================================================================
   ACCESSIBILITY
   ================================================================= */

[data-page='open-jobs'] :focus-visible {
  outline: 2px solid var(--oj-secondary-500);
  outline-offset: 2px;
}

[data-page='open-jobs'] .sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* =================================================================
   PRINT STYLES
   ================================================================= */

@media print {
  [data-page='open-jobs'] {
    color: #000;
    background: #fff;
  }

  [data-page='open-jobs'] .oj-card {
    border: 1px solid #000;
    box-shadow: none;
  }
}
