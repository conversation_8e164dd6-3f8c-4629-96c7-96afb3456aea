'use client';

import './styles/openJobsGlobal.css';

import React, { useEffect } from 'react';

import styles from './styles/openJobs.module.css';

export default function OpenJobsLayout({ children }: { children: React.ReactNode }) {
  // Set the data attribute for scoped styling
  useEffect(() => {
    document.documentElement.setAttribute('data-page', 'open-jobs');

    return () => {
      // Clean up when leaving the page
      document.documentElement.removeAttribute('data-page');
    };
  }, []);

  // Set page data attribute for scoped styling
  useEffect(() => {
    // Keep dark theme but add page-specific styling
    document.documentElement.setAttribute('data-theme', 'dark');
    document.documentElement.style.colorScheme = 'dark';

    // Create and inject custom styles
    const styleElement = document.createElement('style');
    styleElement.id = 'open-jobs-theme-override';
    styleElement.innerHTML = `
      /* Light theme override for open jobs page */
      [data-page="open-jobs"] {
        /* Core color variables */
        --foreground-rgb: 17, 24, 39;
        --foreground-color: rgb(17, 24, 39);
        --background-rgb: 249, 250, 251;
        --background-start: #f9fafb;
        --background-middle: #f9fafb;
        --background-end: #f9fafb;

        /* Card and container styles */
        --card-bg: rgba(255, 255, 255, 1);
        --card-border: rgba(229, 231, 235, 1);
        --sidebar-bg: rgba(255, 255, 255, 0.9);
        --sidebar-border: rgba(229, 231, 235, 1);
        --header-bg: rgba(255, 255, 255, 0.9);

        /* Button styles */
        --button-primary-bg: linear-gradient(to right, #4f46e5, #7c3aed);
        --button-primary-text: #ffffff;
        --button-secondary-bg: rgba(243, 244, 246, 1);
        --button-secondary-text: rgba(55, 65, 81, 1);

        /* Input styles */
        --input-bg: rgba(255, 255, 255, 1);
        --input-border: rgba(209, 213, 219, 1);
        --input-text: rgba(17, 24, 39, 1);
        --input-placeholder: rgba(156, 163, 175, 1);

        /* Status colors */
        --success-color: #16a34a;
        --error-color: #dc2626;
        --warning-color: #ca8a04;
        --info-color: #2563eb;
        --success-bg: rgba(22, 163, 74, 0.1);
        --error-bg: rgba(220, 38, 38, 0.1);
        --warning-bg: rgba(202, 138, 4, 0.1);
        --info-bg: rgba(37, 99, 235, 0.1);

        /* Base styling */
        background-color: #f9fafb;
        color: #111827;
      }

      /* Preserve Tailwind utility classes - use higher specificity instead of !important */
      [data-page="open-jobs"][data-page="open-jobs"] .text-white {
        color: #ffffff;
      }

      [data-page="open-jobs"][data-page="open-jobs"] .text-gray-400 {
        color: #9ca3af;
      }

      [data-page="open-jobs"][data-page="open-jobs"] .text-gray-500 {
        color: #6b7280;
      }

      [data-page="open-jobs"][data-page="open-jobs"] .text-gray-600 {
        color: #4b5563;
      }

      [data-page="open-jobs"][data-page="open-jobs"] .text-gray-700 {
        color: #374151;
      }

      [data-page="open-jobs"][data-page="open-jobs"] .text-gray-800 {
        color: #1f2937;
      }

      [data-page="open-jobs"][data-page="open-jobs"] .text-gray-900 {
        color: #111827;
      }

      /* Preserve background colors with higher specificity */
      [data-page="open-jobs"][data-page="open-jobs"] .bg-white {
        background-color: #ffffff;
      }

      [data-page="open-jobs"][data-page="open-jobs"] .bg-gray-50 {
        background-color: #f9fafb;
      }

      [data-page="open-jobs"][data-page="open-jobs"] .bg-gray-100 {
        background-color: #f3f4f6;
      }

      /* Ensure inputs maintain light theme styling */
      [data-page="open-jobs"] input,
      [data-page="open-jobs"] select,
      [data-page="open-jobs"] textarea {
        background-color: #ffffff;
        color: #111827;
      }

      [data-page="open-jobs"] input::placeholder,
      [data-page="open-jobs"] textarea::placeholder {
        color: #9ca3af;
      }
    `;
    document.head.appendChild(styleElement);

    // Add a data attribute to the body to target open jobs specific styles
    document.body.setAttribute('data-page', 'open-jobs');

    // Cleanup function
    return () => {
      // Remove the style element
      const styleEl = document.getElementById('open-jobs-theme-override');
      if (styleEl) {
        styleEl.remove();
      }

      // Remove the data attribute
      document.body.removeAttribute('data-page');

      // Ensure dark theme is maintained
      document.documentElement.setAttribute('data-theme', 'dark');
      document.documentElement.style.colorScheme = 'dark';
    };
  }, []);

  return <div className={styles.openJobsContainer}>{children}</div>;
}
