'use client';

import { AnimatePresence, motion } from 'framer-motion';
import {
  ArrowRight,
  Briefcase,
  ChevronDown,
  Clock,
  Eye,
  FileText,
  Plus,
  Sparkles,
  Target,
  TrendingUp,
  Users,
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';

import PageHeader from '@/components/common/PageHeader';
import { TabItem, TabNavigation } from '@/components/common/TabNavigation';
import ColourfulLoader from '@/components/Layouts/ColourfulLoader';
import AppLayout from '@/components/steps/layout/AppLayout';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { SimpleTable, SimpleTableColumn } from '@/components/ui/SimpleTable';
import { InsightStatus, InsightType, insightTypeConfig } from '@/lib/career-insights/config';
import { cn } from '@/lib/utils';
import { useCareerInsightsStore } from '@/stores/careerInsightsStore';

// Map insight types to their respective images
const insightTypeImages: Record<string, string> = {
  all: '/images/insights/career_development.png',
  [InsightType.SKILL_GAP_ANALYSIS]: '/images/insights/skill_gap_analysis_revised_1.png',
  [InsightType.CAREER_PATH_RECOMMENDATION]: '/images/insights/career_development.png',
  [InsightType.MARKET_TREND_ANALYSIS]: '/images/insights/performance_analytics_revised.png',
  [InsightType.ROLE_TRANSITION_GUIDANCE]: '/images/insights/professional_training_revised.png',
  [InsightType.COMPENSATION_BENCHMARK]: '/images/insights/team_collaboration_revised.png',
};

export default function ImprovedCareerInsightsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { insights, isLoading, fetchInsights } = useCareerInsightsStore();

  // Initialize activeTab from URL params or default to 'all'
  const typeParam = searchParams.get('type');
  const [activeTab, setActiveTab] = useState<string>(typeParam || 'all');
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());
  const [showInsightOptions, setShowInsightOptions] = useState(false);

  // Update activeTab when URL changes
  useEffect(() => {
    const type = searchParams.get('type');
    if (type) {
      setActiveTab(type);
    } else {
      setActiveTab('all');
    }
  }, [searchParams]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (!target.closest('.insight-options-dropdown')) {
        setShowInsightOptions(false);
      }
    };

    if (showInsightOptions) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [showInsightOptions]);

  useEffect(() => {
    fetchInsights().catch(error => {
      console.error('Failed to fetch insights:', error);
    });
  }, [fetchInsights]);

  // Filter insights based on active tab
  const filteredInsights = useMemo(() => {
    if (activeTab === 'all') {
      return insights;
    }
    return insights.filter(insight => insight.type === activeTab);
  }, [insights, activeTab]);

  // Calculate stats for each tab
  const tabStats = useMemo(() => {
    const stats: Record<string, { count: number; views: number }> = {
      all: {
        count: insights.length,
        views: insights.reduce((sum, i) => sum + (i.viewCount || 0), 0),
      },
    };

    Object.values(InsightType).forEach(type => {
      const typeInsights = insights.filter(i => i.type === type);
      stats[type] = {
        count: typeInsights.length,
        views: typeInsights.reduce((sum, i) => sum + (i.viewCount || 0), 0),
      };
    });

    return stats;
  }, [insights]);

  // Define tabs for navigation with responsive labels
  const tabs: TabItem[] = useMemo(() => {
    const formatLabel = (shortLabel: string, count: number) => {
      // Only show count if greater than 0
      return count > 0 ? `${shortLabel} (${count})` : shortLabel;
    };

    const allTabs: TabItem[] = [
      {
        id: 'all',
        label: formatLabel('All Insights', tabStats.all?.count || 0),
        icon: <Sparkles className="w-4 h-4" />,
      },
      {
        id: InsightType.SKILL_GAP_ANALYSIS,
        label: formatLabel('Skill Gap', tabStats[InsightType.SKILL_GAP_ANALYSIS]?.count || 0),
        icon: <Target className="w-4 h-4" />,
      },
      {
        id: InsightType.CAREER_PATH_RECOMMENDATION,
        label: formatLabel(
          'Career Path',
          tabStats[InsightType.CAREER_PATH_RECOMMENDATION]?.count || 0
        ),
        icon: <TrendingUp className="w-4 h-4" />,
      },
      {
        id: InsightType.MARKET_TREND_ANALYSIS,
        label: formatLabel(
          'Market Trends',
          tabStats[InsightType.MARKET_TREND_ANALYSIS]?.count || 0
        ),
        icon: <FileText className="w-4 h-4" />,
      },
      {
        id: InsightType.ROLE_TRANSITION_GUIDANCE,
        label: formatLabel(
          'Role Transition',
          tabStats[InsightType.ROLE_TRANSITION_GUIDANCE]?.count || 0
        ),
        icon: <Users className="w-4 h-4" />,
      },
      {
        id: InsightType.COMPENSATION_BENCHMARK,
        label: formatLabel(
          'Compensation',
          tabStats[InsightType.COMPENSATION_BENCHMARK]?.count || 0
        ),
        icon: <Briefcase className="w-4 h-4" />,
      },
    ];

    return allTabs;
  }, [tabStats]);

  const handleCreateInsight = (type: InsightType) => {
    router.push(`/career-insights/create?type=${type}`);
  };

  const handleViewInsight = (id: string) => {
    router.push(`/career-insights/${id}`);
  };

  const getStatusBadge = (status: InsightStatus) => {
    const statusConfig = {
      [InsightStatus.READY]: {
        label: 'Ready',
        className: 'bg-green-500/20 text-green-400 border-green-500/30',
      },
      [InsightStatus.PROCESSING]: {
        label: 'Processing',
        className: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
      },
      [InsightStatus.DRAFT]: {
        label: 'Draft',
        className: 'bg-gray-500/20 text-gray-400 border-gray-500/30',
      },
      [InsightStatus.ARCHIVED]: {
        label: 'Archived',
        className: 'bg-red-500/20 text-red-400 border-red-500/30',
      },
    };

    const config = statusConfig[status] || statusConfig[InsightStatus.DRAFT];
    return (
      <Badge variant="outline" className={cn('text-xs', config.className)}>
        {config.label}
      </Badge>
    );
  };

  const formatTimeAgo = (date: string | Date) => {
    const now = new Date();
    const then = typeof date === 'string' ? new Date(date) : date;
    const diffInMs = now.getTime() - then.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
    return `${Math.floor(diffInDays / 365)} years ago`;
  };

  // Get the current header image based on active tab
  const currentHeaderImage = useMemo(() => {
    return insightTypeImages[activeTab] || insightTypeImages.all;
  }, [activeTab]);

  return (
    <AppLayout>
      <div className="h-full flex flex-col relative">
        {/* Full-width Header with dynamic image */}
        <PageHeader
          variant="fullwidth"
          title="Career Insights"
          description="AI-powered insights to accelerate your career growth and unlock opportunities"
          icon={Sparkles}
          imageSrc={currentHeaderImage}
        >
          {insights.length > 0 && (
            <div className="flex items-center gap-6 text-sm text-gray-400">
              <span className="flex items-center gap-2">
                <Sparkles className="w-4 h-4 text-pink-400" />
                {insights.length} Total Insights
              </span>
              <span className="flex items-center gap-2">
                <Eye className="w-4 h-4 text-pink-400" />
                {insights.reduce((sum, i) => sum + (i.viewCount || 0), 0)} Total Views
              </span>
            </div>
          )}
        </PageHeader>

        {/* Spacer for fixed header */}
        <div className="h-[200px] flex-shrink-0"></div>

        {/* Tabs Navigation */}
        <div className="sticky top-0 z-40 flex-none border-b border-gray-300/10 bg-background/95 backdrop-blur">
          <div className="px-4 sm:px-6">
            {/* Tabs row */}
            <div className="flex items-center h-12 sm:h-14">
              <TabNavigation
                tabs={tabs}
                activeTab={activeTab}
                onTabChange={setActiveTab}
                variant="gradient"
                gradientColors={{
                  from: 'from-purple-600',
                  to: 'to-pink-600',
                }}
                updateUrl={true}
                queryParamName="type"
                showBorder={true}
                showSeparators={true}
                mobileCollapse={false}
                className="flex-1 h-full text-white overflow-x-auto"
              />

              {/* Generate New Insight Button - Fixed position on desktop */}
              <div className="hidden lg:flex ml-4 relative insight-options-dropdown">
                <button
                  onClick={() => {
                    if (activeTab === 'all') {
                      setShowInsightOptions(!showInsightOptions);
                    } else {
                      handleCreateInsight(activeTab as InsightType);
                    }
                  }}
                  className="px-3 py-1.5 bg-gradient-to-r from-purple-600 to-pink-600 text-white text-sm font-medium rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all flex items-center gap-1 whitespace-nowrap"
                >
                  <Plus className="w-4 h-4" />
                  <span>Generate New</span>
                  {activeTab === 'all' && (
                    <ChevronDown
                      className={`w-3 h-3 transition-transform ${showInsightOptions ? 'rotate-180' : ''}`}
                    />
                  )}
                </button>

                {/* Dropdown Options for All Insights */}
                <AnimatePresence>
                  {activeTab === 'all' && showInsightOptions && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="absolute top-full right-0 mt-2 w-64 bg-gray-900 border border-gray-700 rounded-lg shadow-xl z-50"
                    >
                      <div className="p-2">
                        <p className="text-xs text-gray-400 px-3 py-2">
                          Choose insight type to generate:
                        </p>
                        {Object.values(InsightType).map(type => {
                          const config = insightTypeConfig[type];
                          const Icon = config.icon;
                          return (
                            <button
                              key={type}
                              onClick={() => {
                                handleCreateInsight(type);
                                setShowInsightOptions(false);
                              }}
                              className="w-full flex items-center gap-3 px-3 py-2 hover:bg-gray-800 rounded-lg transition-colors text-left"
                            >
                              <div className={`p-1.5 rounded-lg bg-gradient-to-br ${config.color}`}>
                                <Icon className="w-3.5 h-3.5 text-white" />
                              </div>
                              <div>
                                <p className="text-sm font-medium text-white">{config.title}</p>
                                <p className="text-xs text-gray-400 line-clamp-1">
                                  {config.description}
                                </p>
                              </div>
                            </button>
                          );
                        })}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>

            {/* Mobile Generate Button - Below tabs */}
            <div className="lg:hidden pb-3">
              {activeTab === 'all' ? (
                <div className="space-y-2">
                  <button
                    onClick={() => setShowInsightOptions(!showInsightOptions)}
                    className="w-full px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white text-sm font-medium rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all flex items-center justify-center gap-2"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Generate New Insight</span>
                    <ChevronDown
                      className={`w-4 h-4 transition-transform ${showInsightOptions ? 'rotate-180' : ''}`}
                    />
                  </button>

                  {/* Mobile Dropdown Options */}
                  {showInsightOptions && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="bg-gray-900 border border-gray-700 rounded-lg overflow-hidden"
                    >
                      <div className="p-2 space-y-1">
                        {Object.values(InsightType).map(type => {
                          const config = insightTypeConfig[type];
                          const Icon = config.icon;
                          return (
                            <button
                              key={type}
                              onClick={() => {
                                handleCreateInsight(type);
                                setShowInsightOptions(false);
                              }}
                              className="w-full flex items-center gap-3 px-3 py-2 hover:bg-gray-800 rounded-lg transition-colors text-left"
                            >
                              <div className={`p-1.5 rounded-lg bg-gradient-to-br ${config.color}`}>
                                <Icon className="w-3.5 h-3.5 text-white" />
                              </div>
                              <span className="text-sm text-white">{config.title}</span>
                            </button>
                          );
                        })}
                      </div>
                    </motion.div>
                  )}
                </div>
              ) : (
                <button
                  onClick={() => handleCreateInsight(activeTab as InsightType)}
                  className="w-full px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white text-sm font-medium rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all flex items-center justify-center gap-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>
                    Generate New {insightTypeConfig[activeTab as InsightType]?.title || 'Insight'}
                  </span>
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 overflow-auto">
          <div className="px-4 sm:px-6 py-4 sm:py-8">
            {/* Loading State */}
            {isLoading && (
              <div className="flex items-center justify-center py-12">
                <ColourfulLoader />
              </div>
            )}

            {/* Insights List - Table View */}
            {!isLoading && filteredInsights.length > 0 && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
              >
                <SimpleTable
                  columns={[
                    {
                      key: 'title',
                      label: 'Title',
                      width: '35%',
                      align: 'left',
                    },
                    {
                      key: 'status',
                      label: 'Status',
                      width: '12%',
                      align: 'center',
                    },
                    {
                      key: 'createdAt',
                      label: 'Created',
                      width: '15%',
                      align: 'left',
                    },
                    {
                      key: 'viewCount',
                      label: 'Views',
                      width: '10%',
                      align: 'center',
                    },
                    {
                      key: 'lastViewedAt',
                      label: 'Last Viewed',
                      width: '15%',
                      align: 'left',
                    },
                    {
                      key: 'action',
                      label: 'Action',
                      width: '13%',
                      align: 'center',
                    },
                  ]}
                  data={filteredInsights.map(insight => {
                    const config = insightTypeConfig[insight.type as InsightType];
                    const Icon = config.icon;

                    return {
                      title: (
                        <div className="flex items-center gap-2">
                          <div
                            className={`p-1.5 rounded-lg bg-gradient-to-br ${config.color} shadow-sm`}
                          >
                            <Icon className="w-3.5 h-3.5 text-white" />
                          </div>
                          <span className="text-white">{insight.title}</span>
                        </div>
                      ),
                      status: getStatusBadge(insight.status),
                      createdAt: (
                        <span className="text-gray-400 text-xs">
                          {formatTimeAgo(insight.createdAt)}
                        </span>
                      ),
                      viewCount: (
                        <span className="flex items-center justify-center gap-1 text-gray-400 text-xs">
                          <Eye className="w-3 h-3" />
                          {insight.viewCount}
                        </span>
                      ),
                      lastViewedAt: (
                        <span className="flex items-center gap-1 text-gray-400 text-xs">
                          {insight.lastViewedAt ? (
                            <>
                              <Clock className="w-3 h-3" />
                              {formatTimeAgo(insight.lastViewedAt)}
                            </>
                          ) : (
                            '-'
                          )}
                        </span>
                      ),
                      action: (
                        <button
                          onClick={() => handleViewInsight(insight.id)}
                          className="px-3 py-1.5 text-gray-400 hover:text-white text-xs font-medium rounded-lg transition-all flex items-center gap-1.5 mx-auto"
                        >
                          <Eye className="w-3.5 h-3.5" />
                          View
                        </button>
                      ),
                    };
                  })}
                  compact={true}
                  hoverable={true}
                  striped={false}
                  className="rounded-lg overflow-hidden bg-white/[0.02] backdrop-blur-sm"
                  headerClassName="bg-gradient-to-r from-purple-600/10 to-pink-600/10"
                  itemsPerPage={10}
                  showPagination={true}
                />
              </motion.div>
            )}

            {/* Empty State for specific tab */}
            {!isLoading && filteredInsights.length === 0 && activeTab !== 'all' && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="text-center py-12"
              >
                <div className="p-6 rounded-full bg-purple-500/10 w-fit mx-auto mb-4">
                  {(() => {
                    const config = insightTypeConfig[activeTab as InsightType];
                    const Icon = config?.icon || Sparkles;
                    return <Icon className="w-12 h-12 text-purple-400" />;
                  })()}
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">
                  No {insightTypeConfig[activeTab as InsightType]?.title || 'insights'} yet
                </h3>
                <p className="text-gray-400 mb-6">
                  Generate your first{' '}
                  {insightTypeConfig[activeTab as InsightType]?.title.toLowerCase() || 'insight'} to
                  get started
                </p>
                <button
                  onClick={() => handleCreateInsight(activeTab as InsightType)}
                  className="px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-medium rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all"
                >
                  Generate {insightTypeConfig[activeTab as InsightType]?.title || 'Insight'}
                </button>
              </motion.div>
            )}

            {/* Empty State for all insights */}
            {!isLoading && insights.length === 0 && activeTab === 'all' && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="text-center py-12"
              >
                <div className="p-6 rounded-full bg-purple-500/10 w-fit mx-auto mb-4">
                  <Sparkles className="w-12 h-12 text-purple-400" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">No insights yet</h3>
                <p className="text-gray-400 mb-8">Choose an insight type to get started</p>

                {/* Quick Action Cards */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 max-w-4xl mx-auto">
                  {Object.values(InsightType).map(type => {
                    const config = insightTypeConfig[type];
                    const Icon = config.icon;

                    return (
                      <Card
                        key={type}
                        className="group border border-white/5 bg-purple-300/5 hover:bg-purple-400/10 transition-all duration-300 cursor-pointer"
                        onClick={() => handleCreateInsight(type)}
                      >
                        <CardHeader className="text-center">
                          <div
                            className={`p-3 rounded-lg bg-gradient-to-br ${config.color} shadow-md w-fit mx-auto mb-3`}
                          >
                            <Icon className="w-6 h-6 text-white" />
                          </div>
                          <CardTitle className="text-sm text-white">{config.title}</CardTitle>
                          <CardDescription className="text-xs text-white/60">
                            {config.description}
                          </CardDescription>
                        </CardHeader>
                      </Card>
                    );
                  })}
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
