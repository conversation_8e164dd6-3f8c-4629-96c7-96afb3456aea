/* Import custom scrollbar styles */
@import '../styles/custom-scrollbar.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations */
@keyframes slowPulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(0.95);
  }
}

html,
body {
  background-color: transparent;
}

/* Ensure all elements have transparent backgrounds by default */
* {
  background-color: transparent;
}

.bg-grid-black {
  background-size: 40px 40px;
  background-image:
    linear-gradient(to right, rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
}

.container {
  /* max-width: 1200px; */
  margin: 0 auto;
  padding: 0 1rem;
}

/* Header styles */
.header {
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Button styles */
.btn {
  @apply font-semibold py-2 px-4 rounded;
}

.btn-primary {
  @apply bg-purple-500 text-white;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-700;
}

/* Input styles */
.input {
  @apply border border-gray-300 rounded px-3 py-2 w-full;
}

/* Card styles */
.card {
  @apply bg-white rounded-lg shadow-md p-6;
}

/* Step indicator styles */
.step-indicator {
  @apply flex items-center;
}

.step-indicator-item {
  @apply w-8 h-8 rounded-full flex items-center justify-center border-2 border-gray-300;
}

.step-indicator-item.active {
  @apply bg-purple-500 text-white border-purple-500;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  @apply font-bold mb-2;
}

h1 {
  @apply text-3xl;
}
h2 {
  @apply text-2xl;
}
h3 {
  @apply text-xl;
}
h4 {
  @apply text-lg;
}

/* Utility classes */
.text-muted {
  @apply text-gray-600;
}

.divider {
  @apply border-t border-gray-200 my-4;
}

.reflection-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transform: skewX(-20deg);
  animation: reflectionSlide 4s ease-in-out infinite;
}

@keyframes reflectionSlide {
  0% {
    left: -100%;
  }
  50% {
    left: 200%;
  }
  100% {
    left: -100%;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out forwards;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  animation: gradient 5s ease infinite;
}

@keyframes gradient-slow {
  0% {
    transform: translate(0) rotate(0deg);
  }
  50% {
    transform: translate(10%) rotate(-45deg);
  }
  100% {
    transform: translate(0) rotate(0deg);
  }
}

.animate-gradient-slow {
  animation: gradient-slow 15s ease infinite;
}

/* Date picker styling */
input[type='date']::-webkit-calendar-picker-indicator {
  filter: invert(0.8);
  opacity: 0.6;
}

input[type='date']::-webkit-calendar-picker-indicator:hover {
  opacity: 1;
}

/* Custom calendar styling */
::-webkit-datetime-edit-fields-wrapper {
  color: inherit;
}

/* Style for the date picker popup calendar */
::-webkit-calendar-picker {
  color-scheme: dark;
}

/* Style for selected date in calendar */
::-webkit-calendar-picker td[selected] {
  background-color: #a855f7;
  color: white;
}

::-webkit-calendar-picker td:active,
::-webkit-calendar-picker td:focus,
::-webkit-calendar-picker td:hover {
  background-color: rgba(168, 85, 247, 0.5);
}

/* Global accent color for all date inputs */
input[type='date'] {
  accent-color: #a855f7;
}

.clip-path-hexagon {
  clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
}
