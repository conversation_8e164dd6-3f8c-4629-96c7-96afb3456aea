/* Theming CSS - Contains all theme-related variables and styles */

/* Default theme (dark) */
:root {
  /* Common variables */
  --sidebar-expanded-width: 240px;
  --sidebar-collapsed-width: 70px;

  /* Dark theme variables (default) */
  --foreground-rgb: 255, 255, 255;
  --foreground-color: rgb(var(--foreground-rgb)); /* Added for direct color use */
  --background-rgb: 10, 10, 20;
  --background-start: #2a1f3e;
  --background-middle: #1f3a45;
  --background-end: #251445;
  --background-accent: #40e0d0;
  --card-bg: rgba(255, 255, 255, 0.1);
  --card-border: rgba(255, 255, 255, 0.2);
  --sidebar-bg: rgba(0, 0, 0, 0.3);
  --sidebar-border: rgba(255, 255, 255, 0.1);
  --header-bg: rgba(0, 0, 0, 0.2);
  --button-primary-bg: linear-gradient(to right, #4f46e5, #7c3aed);
  --button-primary-text: #ffffff;
  --button-secondary-bg: rgba(255, 255, 255, 0.1);
  --button-secondary-text: rgba(255, 255, 255, 0.8);
  /* Dark theme inputs */
  --input-bg: rgba(46, 54, 55, 0.2);
  --input-border: rgb(68, 71, 70);
  --input-text: rgba(255, 255, 255, 0.9);
  --input-placeholder: rgba(255, 255, 255, 0.5);

  /* Dark theme dropdowns and popups */
  --dropdown-bg: rgba(46, 54, 55, 0.2);
  --dropdown-border: rgba(255, 255, 255, 0.15);
  --dropdown-text: rgba(255, 255, 255, 0.9);
  --dropdown-hover-bg: rgba(255, 255, 255, 0.05);
  --dropdown-selected-bg: rgba(79, 70, 229, 0.1);

  /* Status colors */
  --success-color: #22c55e;
  --error-color: #ef4444;
  --warning-color: #eab308;
  --info-color: #d460f1dd;
  --success-bg: rgba(34, 197, 94, 0.2);
  --error-bg: rgba(239, 68, 68, 0.2);
  --warning-bg: rgba(234, 179, 8, 0.2);
  --info-bg: rgba(187, 91, 235, 0.2);
}

/* Light theme */
[data-theme='light'] {
  --foreground-rgb: 51, 51, 51;
  --foreground-color: rgb(var(--foreground-rgb)); /* Added for direct color use */
  --background-rgb: 240, 240, 240;
  --background-start: #f0f4f8;
  --background-middle: #e1e8ed;
  --background-end: #d4e5f9;
  --background-accent: #e1e8ed;
  --card-bg: rgba(255, 255, 255, 0.9);
  --card-border: rgba(0, 0, 0, 0.15);
  --sidebar-bg: rgba(255, 255, 255, 0.7);
  --sidebar-border: rgba(0, 0, 0, 0.07);
  --header-bg: rgba(255, 255, 255, 0.7);
  --button-primary-bg: linear-gradient(to right, #4f46e5, #7c3aed);
  --button-primary-text: #ffffff;
  --button-secondary-bg: rgba(0, 0, 0, 0.04);
  --button-secondary-text: rgba(0, 0, 0, 0.8);
  /* Light theme inputs */
  --input-bg: rgba(255, 255, 255, 0.95);
  --input-border: rgba(0, 0, 0, 0.2);
  --input-text: rgba(0, 0, 0, 0.9);
  --input-placeholder: rgba(0, 0, 0, 0.5);

  /* Light theme dropdowns and popups */
  --dropdown-bg: rgba(255, 255, 255, 0.98);
  --dropdown-border: rgba(0, 0, 0, 0.15);
  --dropdown-text: rgba(0, 0, 0, 0.9);
  --dropdown-hover-bg: rgba(0, 0, 0, 0.05);
  --dropdown-selected-bg: rgba(79, 70, 229, 0.1);

  /* Status colors for light theme */
  --success-color: #16a34a;
  --error-color: #dc2626;
  --warning-color: #ca8a04;
  --info-color: #2563eb;
  --success-bg: rgba(22, 163, 74, 0.1);
  --error-bg: rgba(220, 38, 38, 0.1);
  --warning-bg: rgba(202, 138, 4, 0.1);
  --info-bg: rgba(37, 99, 235, 0.1);
}

/* Dark theme */
[data-theme='dark'] {
  --foreground-rgb: 255, 255, 255;
  --foreground-color: rgb(var(--foreground-rgb)); /* Added for direct color use */
  --background-rgb: 15, 15, 15;
  --background-start: #2a1f3e;
  --background-middle: #1f3a45;
  --background-end: #251445;
  --background-accent: #40e0d0;

  --sidebar-bg: rgba(20, 20, 20, 0.7);
  --sidebar-border: rgba(255, 255, 255, 0.07);
  --header-bg: rgba(20, 20, 20, 0.7);
  --button-primary-bg: linear-gradient(to right, #4f46e5, #7c3aed);
  --button-primary-text: #ffffff;
  --button-secondary-bg: rgba(255, 255, 255, 0.04);
  --button-secondary-text: rgba(255, 255, 255, 0.8);
  --input-bg: rgba(46, 54, 55, 0.2);
  --input-border: rgb(68, 71, 70);
  --input-text: rgba(255, 255, 255, 0.9);
  --input-placeholder: rgba(255, 255, 255, 0.5);

  /* Dark theme dropdowns and popups */
  --dropdown-bg: rgba(46, 54, 55, 0.2);
  --dropdown-border: rgba(255, 255, 255, 0.15);
  --dropdown-text: rgba(255, 255, 255, 0.9);
  --dropdown-hover-bg: rgba(255, 255, 255, 0.05);
  --dropdown-selected-bg: rgba(79, 70, 229, 0.1);

  /* Status colors for dark theme */
  --success-color: #22c55e;
  --error-color: #ef4444;
  --warning-color: #eab308;
  --info-color: #3b82f6;
  --success-bg: rgba(34, 197, 94, 0.1);
  --error-bg: rgba(239, 68, 68, 0.1);
  --warning-bg: rgba(234, 179, 8, 0.1);
  --info-bg: rgba(59, 130, 246, 0.1);
}

/* Apply theme-specific text colors ONLY to elements that opt-in with .themed class */
[data-theme='light'] .themed,
[data-theme='light'] .themed h1,
[data-theme='light'] .themed h2,
[data-theme='light'] .themed h3,
[data-theme='light'] .themed h4,
[data-theme='light'] .themed h5,
[data-theme='light'] .themed h6,
[data-theme='light'] .themed p,
[data-theme='light'] .themed span,
[data-theme='light'] .themed div {
  color: rgb(51, 51, 51);
}

[data-theme='dark'] .themed,
[data-theme='dark'] .themed h1,
[data-theme='dark'] .themed h2,
[data-theme='dark'] .themed h3,
[data-theme='dark'] .themed h4,
[data-theme='dark'] .themed h5,
[data-theme='dark'] .themed h6,
[data-theme='dark'] .themed p,
[data-theme='dark'] .themed span,
[data-theme='dark'] .themed div {
  color: rgb(255, 255, 255);
}

/* Text over images - use higher specificity for white text contrast */
.text-over-image,
.text-over-image h1,
.text-over-image h2,
.text-over-image h3,
.text-over-image h4,
.text-over-image h5,
.text-over-image h6,
.text-over-image p,
.text-over-image span,
.text-over-image div {
  color: white;
}

/* Specific utility classes for image overlays - use double class for specificity */
.text-white-force.text-white-force {
  color: white;
}

.text-white-force-90.text-white-force-90 {
  color: rgba(255, 255, 255, 0.9);
}

.text-white-force-80.text-white-force-80 {
  color: rgba(255, 255, 255, 0.8);
}

.text-white-force-70.text-white-force-70 {
  color: rgba(255, 255, 255, 0.7);
}

.text-white-force-60.text-white-force-60 {
  color: rgba(255, 255, 255, 0.6);
}

/* Apply theme-specific background colors */
[data-theme='light'] .bg-theme-primary {
  background-color: #f0f4f8;
}

[data-theme='light'] .text-theme-primary {
  color: #333333;
}

[data-theme='dark'] .bg-theme-primary {
  background-color: #0f0f0f;
}

[data-theme='dark'] .text-theme-primary {
  color: #ffffff;
}

/* Theme-aware body styles - only apply to elements that opt-in */
.themed-body {
  color: rgb(var(--foreground-rgb));
  background-color: transparent;
}

/* Utility classes for components that want to use theme colors - use double class for specificity */
.theme-text.theme-text {
  color: rgb(var(--foreground-rgb));
}

.theme-text-muted.theme-text-muted {
  color: rgb(var(--foreground-rgb));
  opacity: 0.7;
}

.theme-bg.theme-bg {
  background-color: var(--card-bg);
}

.theme-border.theme-border {
  border-color: var(--card-border);
}

/* Specific utility classes for inline styles replacement - use higher specificity */
.theme-foreground-color.theme-foreground-color {
  color: var(--foreground-color);
}

.theme-input-placeholder.theme-input-placeholder {
  color: var(--input-placeholder);
}

.theme-card-bg.theme-card-bg {
  background-color: var(--card-bg);
}

.theme-card-border.theme-card-border {
  border-color: var(--card-border);
}

.theme-success-bg.theme-success-bg {
  background-color: var(--success-bg);
}

.theme-success-color.theme-success-color {
  color: var(--success-color);
}

.theme-warning-bg.theme-warning-bg {
  background-color: var(--warning-bg);
}

.theme-warning-color.theme-warning-color {
  color: var(--warning-color);
}

.theme-button-secondary-bg.theme-button-secondary-bg {
  background-color: var(--button-secondary-bg);
}

.theme-button-secondary-text.theme-button-secondary-text {
  color: var(--button-secondary-text);
}

/* Override for components that should NOT be affected by global theming - use attribute selector for specificity */
[class~="no-theme"],
[class~="no-theme"] * {
  color: inherit;
  background-color: inherit;
}

/* Preserve original colors for specific component types */
[class~="preserve-colors"],
[class~="preserve-colors"] *:not(.theme-text):not(.theme-bg):not(.theme-border) {
  color: inherit;
}
