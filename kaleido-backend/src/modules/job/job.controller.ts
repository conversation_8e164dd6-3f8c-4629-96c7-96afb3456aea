import { Public } from '@/auth/public.decorator';
import { GetUser, User } from '@/shared/decorators/get-user.decorator';
import { CreditActionType } from '@/shared/enums/subscription-limit-type.enum';
import { Auth0Guard } from '@/shared/guards/auth0.guard';
import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  NotFoundException,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Put,
  Query,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JobAIOperationsService } from './services/job-ai-operations.service';
import { JobPublishingService } from './services/job-publishing.service';
import { JobSearchService } from './services/job-search.service';
import { JobSeekerJobsService } from './services/job-seeker-jobs.service';

import { Candidate } from '../candidate/entities/candidate.entity';
import { CreditService } from '../subscription/credit.service';
import { CreateJobDto } from './dto/create-job.dto';
import { UpdateJobDto } from './dto/update-job.dto';
import { Job } from './entities/job.entity';
import { JobService } from './job.service';
import {
  JobMatchRankResponse,
  JobsByStatusResponse,
  JobsResponse,
  MatchRankResponseWithMetadata,
  PublicJobFilters,
  RankedJobsStats,
} from './jobs.types';
import { JobCriteriaService } from './services/job-criteria.service';
import { JobMatchRankOperationsService } from './services/job-match-rank-operations.service';
import { JobStatsService } from './services/job-stats.service';
import { JobTransformationService } from './services/job-transformation.service';

@ApiTags('jobs')
@ApiBearerAuth()
@Controller('jobs')
export class JobController {
  constructor(
    private readonly jobService: JobService,
    private readonly creditService: CreditService,
    private readonly jobSearchService: JobSearchService,
    private readonly jobPublishingService: JobPublishingService,
    private readonly jobSeekerJobsService: JobSeekerJobsService,
    private readonly jobAIOperationsService: JobAIOperationsService,
    private readonly jobStatsService: JobStatsService,
    private readonly jobTransformationService: JobTransformationService,
    private readonly jobCriteriaService: JobCriteriaService,
    private readonly jobMatchRankOperationsService: JobMatchRankOperationsService,
    @InjectRepository(Candidate)
    private readonly candidateRepository: Repository<Candidate>,
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
  ) {}

  @Post()
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Create a new job' })
  @ApiResponse({ status: 201, description: 'Job created successfully' })
  async create(@Body() createJobDto: CreateJobDto, @GetUser() authUser: User): Promise<Job> {
    // Job creation is free - no credit validation needed

    // Create the job
    const createdJob = await this.jobService.create(createJobDto, authUser);

    // Job description generation is free - no credit consumption needed

    return createdJob;
  }

  @Get('by-status')
  @UseGuards(Auth0Guard)
  async getJobsByStatus(
    @GetUser() user: User,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('status') status?: string,
    @Query('includeStats') includeStats?: string,
  ): Promise<JobsByStatusResponse> {
    return this.jobSearchService.getJobsByStatus(user.userId, {
      page: Number(page),
      limit: Number(limit),
      status,
      includeStats: includeStats === 'true',
    });
  }

  @Get()
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Get all jobs' })
  async findAll(@GetUser() user: User): Promise<JobsResponse> {
    if (!user) {
      throw new UnauthorizedException('User not authenticated');
    }
    return this.jobService.findByClientId(user.userId, ['candidates', 'candidateEvaluations']);
  }

  @Get('ranked-jobs-stats')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Get statistics for ranked jobs' })
  @ApiResponse({
    status: 200,
    description: 'Returns analysis of job matched records, candidate counts, and other statistics',
  })
  async getRankedJobsStats(
    @GetUser() user: User,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ): Promise<RankedJobsStats> {
    return this.jobStatsService.getRankedJobsStats(user.userId, Number(page), Number(limit));
  }

  @Get('match-ranked')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Get all jobs with match rank information' })
  @ApiResponse({
    status: 200,
    description: 'Returns jobs with match rank information for the table view',
  })
  async getMatchRanked(
    @GetUser() user: User,
    @Query('page') page = 1,
    @Query('limit') limit = 20,
  ): Promise<MatchRankResponseWithMetadata> {
    return this.jobSearchService.getMatchRanked(user.userId, Number(page), Number(limit));
  }

  @Get(':id/criteria')
  @UseGuards(Auth0Guard)
  @ApiOperation({
    summary: 'Get job criteria for editing with essential data only (performance optimized)',
  })
  async findJobCriteria(
    @Param('id', ParseUUIDPipe) id: string,
    @GetUser() user: User,
    @Query('timestamp') timestamp?: string,
    @Query('forceRefresh') forceRefresh?: string,
  ): Promise<any> {
    return this.jobCriteriaService.getJobCriteria(id, user.userId, timestamp, forceRefresh);
  }

  @Get(':id/details')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Get lightweight job details without candidate data' })
  @ApiResponse({
    status: 200,
    description: 'Returns job details optimized for JobDetailsTab component',
  })
  async getJobDetails(@Param('id', ParseUUIDPipe) id: string) {
    const job = await this.jobService.findOne(id, ['company']);

    // Get total candidates count without loading all candidate data
    const totalCandidates = await this.jobService.getCandidatesCount(id);

    return {
      ...job,
      totalCandidates,
      // Exclude heavy data
      candidates: undefined,
      candidateEvaluations: undefined,
      videoResponses: undefined,
    };
  }

  @Get(':id')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Get job by id' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('timestamp') timestamp?: string,
    @Query('forceRefresh') forceRefresh?: string,
  ) {
    const shouldForceRefresh = !!timestamp || forceRefresh === 'true';

    const job = await this.jobService.findOne(
      id,
      ['candidates', 'candidateEvaluations', 'videoJDs', 'videoResponses'],
      shouldForceRefresh,
    );

    const totalCandidates = job.candidates?.length || 0;
    const recentCandidates = job.candidates?.slice(-5) || [];

    const matchRankCost = {
      success: true,
      creditCost: 0,
      unevaluatedCandidatesCount: totalCandidates,
      isValid: true,
      message: 'Cost calculation available',
      availableCredits: 1000,
    };

    return this.jobTransformationService.transformJobDetailResponse(
      job,
      totalCandidates,
      recentCandidates,
      matchRankCost,
    );
  }

  @Get(':id/candidates')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Get all candidates for a job with job context and thresholds' })
  @ApiResponse({
    status: 200,
    description:
      'Returns job context with thresholds and all candidates (frontend handles pagination)',
  })
  async findCandidates(@Param('id', ParseUUIDPipe) id: string, @Query() query: any) {
    console.log(`[JobController] GET /jobs/${id}/candidates called with query params:`, query);
    return this.jobService.findCandidates(id);
  }

  @Patch(':id')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Update job' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateJobDto: UpdateJobDto,
    @GetUser() user: User,
  ): Promise<Job> {
    // Check if culture fit questions or description are being modified
    const isCultureFitUpdate =
      updateJobDto.cultureFitQuestions !== undefined ||
      updateJobDto.cultureFitDescription !== undefined;

    if (isCultureFitUpdate) {
      // Get the current job to check if this is actually a new culture fit setup
      const currentJob = await this.jobService.findOne(id);

      // Only consume credits if:
      // 1. cultureFitQuestions are being added/modified AND
      // 2. Either there were no existing questions or the questions are being changed
      const isNewCultureFitSetup =
        updateJobDto.cultureFitQuestions &&
        updateJobDto.cultureFitQuestions.length > 0 &&
        (!currentJob.cultureFitQuestions ||
          currentJob.cultureFitQuestions.length === 0 ||
          JSON.stringify(currentJob.cultureFitQuestions) !==
            JSON.stringify(updateJobDto.cultureFitQuestions));

      if (isNewCultureFitSetup) {
        // Validate and consume credits for culture fit questions
        const companyId = user.sub || user.userId || '';
        await this.creditService.checkAndConsumeCredits(
          companyId,
          CreditActionType.CULTURE_FIT_QUESTIONS,
          1, // 1 credit per culture fit question set
        );
      }
    }

    // Update the job
    const updatedJob = await this.jobService.update(id, updateJobDto);

    return updatedJob;
  }

  @Patch(':id/matchrank-criteria')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Update job criteria for MatchRank (thresholds/requirements only)' })
  async updateMatchRankCriteria(
    @Param('id', ParseUUIDPipe) id: string,
    @Body()
    updateData: {
      topCandidateThreshold?: number;
      secondTierCandidateThreshold?: number;
      requirements?: string[];
    },
  ): Promise<Job> {
    // This endpoint only updates thresholds and requirements
    // No AI generation, no candidate modifications, no side effects
    const updatedJob = await this.jobService.updateMatchRankCriteriaOnly(id, updateData);
    return updatedJob;
  }

  @Patch(':id/culture-fit')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Update job culture fit questions and description' })
  async updateCultureFit(
    @Param('id', ParseUUIDPipe) id: string,
    @Body()
    updateData: {
      cultureFitQuestions?: any[];
      cultureFitDescription?: string;
    },
    @GetUser() user: User,
  ): Promise<{
    success: boolean;
    data: Job;
    message: string;
    debug?: {
      originalQuestions: any[];
      processedQuestions: any[];
      savedQuestions: any[];
    };
  }> {
    const currentJob = await this.jobService.findOne(id);

    const isNewCultureFitSetup =
      updateData.cultureFitQuestions &&
      updateData.cultureFitQuestions.length > 0 &&
      (!currentJob.cultureFitQuestions ||
        currentJob.cultureFitQuestions.length === 0 ||
        JSON.stringify(currentJob.cultureFitQuestions) !==
          JSON.stringify(updateData.cultureFitQuestions));

    if (isNewCultureFitSetup) {
      const companyId = user.sub || user.userId || '';
      await this.creditService.checkAndConsumeCredits(
        companyId,
        CreditActionType.CULTURE_FIT_QUESTIONS,
        1,
      );
    }

    const processedQuestions = this.jobTransformationService.processCultureFitQuestions(
      updateData.cultureFitQuestions,
    );

    await this.jobService.updateCultureFitOnly(id, {
      cultureFitQuestions: processedQuestions,
      cultureFitDescription: updateData.cultureFitDescription,
    });

    const verifiedJob = await this.jobService.findOne(
      id,
      ['candidates', 'candidateEvaluations'],
      true,
    );

    // Return a response that tells the frontend to refresh
    // and trigger an event to notify components
    const response = {
      success: true,
      message: 'Culture fit questions updated successfully',
      data: verifiedJob,
      requiresRefresh: true,
      job: {
        id: verifiedJob.id,
        cultureFitQuestions: verifiedJob.cultureFitQuestions || [],
        cultureFitDescription: verifiedJob.cultureFitDescription || '',
      },
    };

    // Emit a server-sent event or WebSocket message if needed
    // This ensures VideoIntroTab gets the update

    return response;
  }

  @Delete(':id')
  @UseGuards(Auth0Guard)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete job' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    await this.jobService.remove(id);
  }

  @Put(':id/publish')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Toggle job publication status for selected platforms' })
  @ApiResponse({ status: 200, description: 'Job publication status updated successfully' })
  async publishJob(@Param('id', ParseUUIDPipe) id: string, @Body() body: { platforms: string[] }) {
    if (!id) {
      throw new BadRequestException('Job ID is required in URL path');
    }

    if (!body.platforms || !Array.isArray(body.platforms) || body.platforms.length === 0) {
      throw new BadRequestException('Platforms array is required in request body');
    }

    return this.jobPublishingService.toggleJobPublication(id, body.platforms);
  }

  @Post('generate-tone')
  @UseGuards(Auth0Guard)
  async generateJobWithTone(@Body() body: { jobId: string; tone: string }) {
    return this.jobPublishingService.generateJobWithTone(body.jobId, body.tone);
  }

  @Post('extract-job-description')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Extract structured information from job description text' })
  @ApiResponse({
    status: 200,
    description: 'Job information extracted successfully',
    schema: {
      type: 'object',
      properties: {
        title: { type: 'string' },
        companyName: { type: 'string' },
        companyDescription: { type: 'string' },
        department: { type: 'string' },
        jobType: { type: 'string' },
        typeOfHiring: { type: 'string' },
        typeOfJob: { type: 'string' },
        experienceLevel: { type: 'string' },
        experience: { type: 'string' },
        skills: { type: 'array', items: { type: 'string' } },
        jobResponsibilities: { type: 'array', items: { type: 'string' } },
        requirements: { type: 'array', items: { type: 'string' } },
        benefits: { type: 'array', items: { type: 'string' } },
        salaryRange: { type: 'string' },
        currency: { type: 'string' },
        location: { type: 'string' },
        hiringManagerDescription: { type: 'string' },
        socialMediaDescription: { type: 'string' },
      },
    },
  })
  async extractJobDescription(
    @Body() body: { jobDescriptionText: string },
    @GetUser() authUser: User,
  ) {
    return this.jobAIOperationsService.extractJobDescription(
      body.jobDescriptionText,
      authUser.userId,
    );
  }

  @Post('generate-social-media-description')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Generate social media description for job posting' })
  async generateSocialMediaDescription(@Body() body: { jobId: string }) {
    return this.jobPublishingService.generateSocialMediaDescription(body.jobId);
  }

  @Get(':id/public-application')
  @Public()
  @ApiOperation({ summary: 'Get job details for public application (no candidate data)' })
  @ApiResponse({
    status: 200,
    description:
      'Returns job details with video JDs for public viewing, excluding candidate information',
  })
  async findBySlug(@Param('id') id: string): Promise<Job> {
    // Only load videoJDs and company relations for public viewing
    // Exclude candidates and candidateEvaluations to protect privacy
    // requirePublished=true ensures only published jobs are returned at database level
    return this.jobService.findByJobId(id, ['videoJDs', 'company'], true);
  }

  @Get('company/:companyId/public')
  @Public()
  @ApiOperation({ summary: 'Get public jobs for a company' })
  @ApiResponse({
    status: 200,
    description: 'Returns jobs for a specific company for public display',
  })
  async getCompanyJobsPublic(@Param('companyId') companyId: string): Promise<JobsResponse> {
    return this.jobSearchService.getCompanyJobsPublic(companyId);
  }

  @Post('public/search')
  @Public()
  @ApiOperation({ summary: 'Search for public jobs using POST' })
  @ApiResponse({
    status: 200,
    description: 'Returns public job listings with basic information',
  })
  async searchPublicJobs(
    @Body()
    searchParams: {
      companyId?: string;
      page?: number;
      limit?: number;
      filters?: PublicJobFilters;
      search?: string;
    },
  ): Promise<JobsResponse> {
    return this.jobSearchService.searchPublicJobs({
      ...searchParams,
      page: searchParams.page || 1,
      limit: searchParams.limit || 10,
    });
  }

  @Get('public/search')
  @Public()
  @ApiOperation({ summary: 'Search for public jobs' })
  @ApiResponse({
    status: 200,
    description: 'Returns public job listings with basic information',
  })
  async getPublicJobs(
    @Query('companyId') companyId: string,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
    @Query('filters') filters?: string,
    @Query('search') search?: string,
  ): Promise<JobsResponse> {
    const parsedFilters: PublicJobFilters | undefined = filters ? JSON.parse(filters) : undefined;

    return this.jobSearchService.searchPublicJobs({
      companyId,
      page: Number(page),
      limit: Number(limit),
      filters: parsedFilters,
      search,
    });
  }

  @Get('company/my-jobs')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Get jobs posted by the authenticated company/employer' })
  @ApiResponse({
    status: 200,
    description: 'Returns jobs posted by the authenticated user (employer/company)',
  })
  async getMyJobs(
    @GetUser() authUser: User,
    @Query('page') page = 1,
    @Query('limit') limit = 50,
  ): Promise<JobsResponse> {
    return this.jobSearchService.getMyJobs(authUser.userId, Number(page), Number(limit), [
      'videoJDs',
    ]);
  }

  @Post('open-jobs')
  @Public()
  @ApiOperation({ summary: 'Search open jobs (public endpoint with optional auth)' })
  @ApiResponse({
    status: 200,
    description: 'Returns open job listings with hasApplied field if authenticated',
  })
  async searchOpenJobs(
    @Body()
    searchParams: {
      companyId?: string;
      page?: number;
      limit?: number;
      search?: string;
      filters?: PublicJobFilters;
      jobId?: string;
      jobType?: string;
      department?: string;
      experienceLevel?: string;
      industry?: string;
      userId?: string; // Optional userId to check application status
    },
  ): Promise<JobsResponse> {
    // Extract individual filter parameters if they're sent directly in the body
    const filters: PublicJobFilters = searchParams.filters || {};

    // If individual filter fields are provided, add them to filters
    if (searchParams.jobType) filters.jobType = searchParams.jobType;
    if (searchParams.department) filters.department = searchParams.department;
    if (searchParams.experienceLevel) filters.experienceLevel = searchParams.experienceLevel;
    if (searchParams.industry) filters.industry = searchParams.industry;

    return this.jobSearchService.searchOpenJobs({
      companyId: searchParams.companyId,
      page: Number(searchParams.page || 1),
      limit: Number(searchParams.limit || 10),
      search: searchParams.search,
      filters,
      userId: searchParams.userId, // Pass userId to check application status
    });
  }

  @Post('job-seeker/search')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Search published jobs for job seeker or employer' })
  @ApiResponse({
    status: 200,
    description:
      "Returns published jobs filtered by job seeker preferences with application status or employer's own published jobs",
  })
  async searchJobsForJobSeeker(
    @GetUser() authUser: User,
    @Body()
    searchParams: {
      all?: boolean;
      applied?: string;
      postedByMe?: string;
      page?: number;
      limit?: number;
      search?: string;
      filters?: Record<string, string>;
    },
  ) {
    return this.jobSeekerJobsService.searchJobsForJobSeeker(authUser, searchParams);
  }

  @Get('job-seeker/search')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Get published jobs for job seeker or employer' })
  @ApiResponse({
    status: 200,
    description:
      "Returns published jobs filtered by job seeker preferences with application status or employer's own published jobs",
  })
  async getJobsForJobSeeker(
    @GetUser() authUser: User,
    @Query('all') all?: boolean,
    @Query('applied') applied?: string,
    @Query('postedByMe') postedByMe?: string,
    @Query('page') page = 1,
    @Query('limit') limit = 50,
  ) {
    return this.jobSeekerJobsService.searchJobsForJobSeeker(authUser, {
      all,
      applied,
      postedByMe,
      page: Number(page),
      limit: Number(limit),
    });
  }

  @Get('job-seeker/applications')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Get all job applications for a job seeker' })
  @ApiResponse({
    status: 200,
    description: 'Returns all jobs the job seeker has applied for',
  })
  async getJobSeekerApplications(@GetUser() authUser: User) {
    return this.jobSeekerJobsService.getJobSeekerApplications(authUser.userId);
  }

  @Get(':id/application-status')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Get job application status for the current job seeker' })
  @ApiResponse({
    status: 200,
    description: 'Returns the application status for the current job seeker',
  })
  async getJobApplicationStatus(@Param('id') jobId: string, @GetUser() authUser: User) {
    return this.jobSeekerJobsService.getJobApplicationStatus(jobId, authUser.userId);
  }

  @Get('matched-candidates/:clientId')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Get all matched candidates for jobs by client ID' })
  @ApiResponse({
    status: 200,
    description: "Returns all matched candidates grouped by threshold for a client's jobs",
  })
  async getAllMatchedCandidates(
    @Param('clientId') clientId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 9,
  ) {
    return this.jobService.getAllMatchedCandidates(clientId, { page, limit });
  }

  @Get(':id/matched-candidates')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Get matched candidates separated by threshold for a specific job' })
  @ApiResponse({
    status: 200,
    description:
      'Returns matched candidates separated into top candidates and other candidates based on match score',
  })
  async getMatchedCandidates(
    @Param('id') id: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 9,
  ) {
    return this.jobService.getMatchedCandidates(id, { page, limit });
  }

  @Post(':id/add-candidates')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Add existing candidates to a job' })
  @ApiResponse({
    status: 200,
    description: 'Returns the updated job with added candidates',
  })
  async addCandidatesToJob(
    @Param('id') id: string,
    @Body() data: { candidateIds: string[] },
    @GetUser() user: User,
  ) {
    return this.jobService.addCandidatesToJob(id, data.candidateIds, user.userId);
  }

  @Get(':jobId/match-ranks')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Get candidates grouped by match rank tiers' })
  @ApiResponse({
    status: 200,
    description: 'Returns candidates grouped by their match rank tiers',
  })
  async getMatchRanks(
    @Param('jobId') jobId: string,
    @Query('page') page = 1,
    @Query('limit') limit = 10,
  ): Promise<JobMatchRankResponse> {
    return this.jobService.getMatchRanks(jobId, {
      page: Number(page),
      limit: Number(limit),
    });
  }

  @Get(':id/candidates-paginated')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Get job candidates with minimal data for candidates page' })
  @ApiResponse({
    status: 200,
    description: 'Returns job context and paginated candidates with minimal data',
  })
  async getJobCandidates(
    @Param('id') jobId: string,
    @Query('page') page = 1,
    @Query('limit') limit = 20,
    @Query('tier') tier?: string,
    @Query('status') status?: string,
  ) {
    return this.jobService.getJobCandidatesMinimal(jobId, {
      page: Number(page),
      limit: Number(limit),
      tier,
      status,
    });
  }

  @Get(':id/culture-fit-details')
  @UseGuards(Auth0Guard)
  @ApiOperation({
    summary: 'Get job culture fit details with minimal candidate info and video responses',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns job culture fit questions and candidates with their video responses',
  })
  async getCultureFitDetails(@Param('id') jobId: string) {
    return this.jobService.getCultureFitDetails(jobId);
  }

  @Get(':jobId/candidates/:candidateId/culture-fit-response')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Get culture fit response for updating frontend after PATCH' })
  @ApiResponse({
    status: 200,
    description: 'Returns culture fit questions in the same format as video-responses endpoint',
  })
  async getCultureFitResponse(
    @Param('jobId') jobId: string,
    @Param('candidateId') candidateId: string,
  ) {
    // Return the same structure as getCandidateVideoResponses
    return this.getCandidateVideoResponses(jobId, candidateId);
  }

  @Get(':id/debug-video-responses')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Debug endpoint to check video responses for a job' })
  async debugVideoResponses(@Param('id') jobId: string) {
    return this.jobService.debugVideoResponses(jobId);
  }

  @Get(':jobId/candidates/:candidateId/video-responses')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Get video responses for a specific candidate in a job' })
  @ApiResponse({
    status: 200,
    description: 'Returns video responses for the candidate including culture fit questions',
  })
  async getCandidateVideoResponses(
    @Param('jobId') jobId: string,
    @Param('candidateId') candidateId: string,
  ) {
    const job = await this.jobRepository.findOne({
      where: { id: jobId },
      select: ['id', 'jobType', 'companyName', 'department', 'cultureFitQuestions'],
    });

    if (!job) {
      throw new NotFoundException(`Job with ID ${jobId} not found`);
    }

    // Find candidate and their video responses for this specific job
    const candidate = await this.candidateRepository
      .createQueryBuilder('candidate')
      .leftJoinAndSelect(
        'candidate.videoResponses',
        'videoResponse',
        'videoResponse.candidateId = candidate.id AND videoResponse.jobId = :jobId',
        { jobId },
      )
      .where('candidate.id = :candidateId', { candidateId })
      .select([
        'candidate.id',
        'candidate.fullName',
        'candidate.jobTitle',
        'candidate.email',
        'candidate.location',
        'candidate.currentCompany',
        'candidate.yearsOfExperience',
        'candidate.activityHistory',
        'videoResponse.id',
        'videoResponse.questionId',
        'videoResponse.question',
        'videoResponse.videoUrl',
        'videoResponse.duration',
        'videoResponse.recordedAt',
        'videoResponse.status',
        'videoResponse.isExpired',
      ])
      .getOne();

    if (!candidate) {
      throw new NotFoundException(`Candidate with ID ${candidateId} not found`);
    }

    // Check if this candidate is actually associated with this job
    if (candidate.jobId !== jobId) {
      console.warn(
        `Warning: Candidate ${candidateId} is associated with job ${candidate.jobId}, ` +
          `but video responses are being requested for job ${jobId}`,
      );
    }

    return this.jobTransformationService.transformCandidateVideoResponses(job, candidate);
  }

  @Post('generate-responsibilities')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Generate job responsibilities using AI' })
  async generateResponsibilities(
    @Body() data: { department: string; jobType: string; experience?: string },
  ) {
    return this.jobAIOperationsService.generateResponsibilities(data);
  }

  @Post('summarize')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Generate company summary using AI' })
  async generateCompanySummary(@Body() data: { url: string; clientId: string }) {
    return this.jobAIOperationsService.generateCompanySummary(data);
  }

  @Get('match-rank-status/:jobId')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Get the status of a match rank job' })
  @ApiResponse({
    status: 200,
    description: 'Returns the status of a match rank job',
  })
  async getMatchRankStatus(@Param('jobId') jobId: string) {
    return this.jobMatchRankOperationsService.getMatchRankStatus(jobId);
  }

  @Get('match-rank-results/:jobId')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Get paginated match rank results' })
  @ApiResponse({
    status: 200,
    description: 'Returns paginated match rank results for better performance with large datasets',
  })
  async getMatchRankResults(
    @Param('jobId') jobId: string,
    @Query('page') page = 1,
    @Query('limit') limit = 50,
  ) {
    return this.jobMatchRankOperationsService.getMatchRankResults(
      jobId,
      Number(page),
      Number(limit),
    );
  }

  @Get('match-rank-cost/:jobId')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Get the credit cost for match ranking a specific job' })
  @ApiResponse({
    status: 200,
    description: 'Returns the credit cost and validation result for match ranking',
  })
  async getMatchRankCost(@Param('jobId') jobId: string, @GetUser() user: User) {
    if (!user?.userId) {
      throw new UnauthorizedException('User not authenticated');
    }
    return this.jobMatchRankOperationsService.getMatchRankCost(jobId, user.userId);
  }

  @Post('start-match-rank/:jobId')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Start a match rank job' })
  @ApiResponse({
    status: 200,
    description: 'Returns the job ID if the job was started successfully',
  })
  async startMatchRankJob(@Param('jobId') jobId: string, @GetUser() user: User) {
    return this.jobMatchRankOperationsService.startMatchRankJob(jobId, user.userId);
  }

  @Post('cancel-match-rank/:jobId')
  @UseGuards(Auth0Guard)
  @ApiOperation({ summary: 'Cancel a match rank job' })
  @ApiResponse({
    status: 200,
    description: 'Returns success if the job was cancelled',
  })
  async cancelMatchRankJob(@Param('jobId') jobId: string) {
    return this.jobMatchRankOperationsService.cancelMatchRankJob(jobId);
  }
}
