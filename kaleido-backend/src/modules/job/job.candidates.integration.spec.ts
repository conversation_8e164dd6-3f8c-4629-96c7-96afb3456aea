import { INestApplication, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import request from 'supertest';
import { Repository } from 'typeorm';
import { Auth0Guard } from '@shared/guards/auth0.guard';
import { MultiAIContentService } from '@shared/services/multi-ai-content.service';

import { Candidate } from '@modules/candidate/entities/candidate.entity';
import { CandidateEvaluation } from '@modules/candidate/entities/candidate-evaluation.entity';
import { JobApplication } from '@modules/job-seeker/entities/job-application.entity';
import { JobSeeker } from '@modules/job-seeker/entities/job-seeker.entity';
import { JobSeekerService } from '@modules/job-seeker/job-seeker.service';
import { GraduateService } from '@modules/graduate/graduate.service';
import { CompanyService } from '@modules/company/company.service';
import { OpenaiService } from '@shared/services/openai.service';
import { LinkedInService } from '@modules/vendors/linkedin/linkedin.service';
import { EnhancedJobPublishingService } from '@modules/company/services/enhanced-job-publishing.service';
import { Job } from './entities/job.entity';
import { JobCandidatesHelpers } from './job.candidates.helpers';
import { JobController } from './job.controller';
import { JobService } from './job.service';
import { JobMatchRankHelper } from './job.matchrank.helper';
import { JobAiHelpers } from './job.ai.helpers';
import { JobCrudUtils } from './job.crud.utils';
import { JobMatchingService } from './job-matching.service';
import { CreditValidationEngine } from '../subscription/credit-validation.engine';
import { CreditService } from '../subscription/credit.service';
import { JobSearchService } from './services/job-search.service';
import { JobPublishingService } from './services/job-publishing.service';
import { JobSeekerJobsService } from './services/job-seeker-jobs.service';
import { JobAIOperationsService } from './services/job-ai-operations.service';
import { JobStatsService } from './services/job-stats.service';
import { JobTransformationService } from './services/job-transformation.service';
import { JobCriteriaService } from './services/job-criteria.service';
import { JobMatchRankOperationsService } from './services/job-match-rank-operations.service';

describe('Job Candidates API Integration', () => {
  let app: INestApplication;
  let jobRepository: Repository<Job>;
  let candidateRepository: Repository<Candidate>;

  const mockJob = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    jobType: 'Software Engineer',
    department: 'Engineering',
    companyName: 'Test Company',
    topCandidateThreshold: 80,
    secondTierCandidateThreshold: 60,
    status: 'OPEN' as any,
  };

  const mockCandidatesWithJobStats = [
    {
      id: 'candidate-1',
      fullName: 'John Doe',
      jobTitle: 'Senior Developer',
      location: 'New York',
      skills: ['JavaScript', 'React'],
      status: 'OPEN' as any,
      contacted: false,
      tier: 'TOP',
      evaluation: {
        matchScore: 85,
        lastEvaluatedAt: new Date(),
        criterionMatchedOn: ['skills', 'experience'],
      },
      currentCompany: 'Previous Company',
      yearsOfExperience: 5,
      videoResponses: [],
      jobStats: {
        topCandidateThreshold: 80,
        secondTierCandidateThreshold: 60,
      },
    },
    {
      id: 'candidate-2',
      fullName: 'Jane Smith',
      jobTitle: 'Frontend Developer',
      location: 'San Francisco',
      skills: ['React', 'TypeScript'],
      status: 'SHORTLISTED',
      contacted: true,
      tier: 'SECOND',
      evaluation: {
        matchScore: 65,
        lastEvaluatedAt: new Date(),
        criterionMatchedOn: ['skills'],
      },
      currentCompany: 'Another Company',
      yearsOfExperience: 3,
      videoResponses: [],
      jobStats: {
        topCandidateThreshold: 80,
        secondTierCandidateThreshold: 60,
      },
    },
  ];

  beforeEach(async () => {
    const mockJobRepository = {
      createQueryBuilder: jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockJob),
      })),
    };

    const mockCandidateRepository = {
      createQueryBuilder: jest.fn(() => ({
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue(mockCandidatesWithJobStats),
      })),
      findOne: jest.fn(),
    };
    const mockConfigService = {
      get: jest.fn((key: string) => {
        switch (key) {
          case 'NODE_ENV':
            return 'test';
          case 'AUTH_OFFLINE_DEV':
            return 'false';
          default:
            return undefined;
        }
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [JobController],
      providers: [
        {
          provide: JobService,
          useValue: {
            findCandidates: jest.fn().mockResolvedValue({
              job: mockJob,
              candidates: {
                topTier: [mockCandidatesWithJobStats[0]],
                secondTier: [mockCandidatesWithJobStats[1]],
                others: [],
                shortlisted: [mockCandidatesWithJobStats[1]],
                unranked: [],
              },
              stats: {
                totalCandidates: 2,
                topTierCount: 1,
                secondTierCount: 1,
                othersCount: 0,
                shortlistedCount: 1,
                unrankedCount: 0,
              },
            }),
            findOne: jest.fn().mockResolvedValue(mockJob),
            create: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: JobCandidatesHelpers,
          useValue: {
            findCandidates: jest.fn().mockResolvedValue({
              job: mockJob,
              candidates: {
                topTier: [mockCandidatesWithJobStats[0]],
                secondTier: [mockCandidatesWithJobStats[1]],
                others: [],
                shortlisted: [mockCandidatesWithJobStats[1]],
                unranked: [],
              },
              stats: {
                totalCandidates: 2,
                topTierCount: 1,
                secondTierCount: 1,
                othersCount: 0,
                shortlistedCount: 1,
                unrankedCount: 0,
              },
            }),
          },
        },
        {
          provide: getRepositoryToken(Job),
          useValue: mockJobRepository,
        },
        {
          provide: getRepositoryToken(Candidate),
          useValue: mockCandidateRepository,
        },
        {
          provide: getRepositoryToken(JobApplication),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(CandidateEvaluation),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(JobSeeker),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: JobSeekerService,
          useValue: {
            findAll: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: GraduateService,
          useValue: {
            findAll: jest.fn(),
            create: jest.fn(),
          },
        },
        {
          provide: CompanyService,
          useValue: {
            findOne: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: OpenaiService,
          useValue: {
            generateCompletion: jest.fn(),
            validateJsonStructure: jest.fn(),
          },
        },
        {
          provide: MultiAIContentService,
          useValue: {
            extractJobInformation: jest.fn(),
            generateSkillsAndResponsibilities: jest.fn(),
          },
        },
        {
          provide: JobMatchingService,
          useValue: {
            findMatchingCandidates: jest.fn(),
            matchCandidates: jest.fn(),
          },
        },
        {
          provide: CreditValidationEngine,
          useValue: {
            validateCredits: jest.fn(),
          },
        },
        {
          provide: CreditService,
          useValue: {
            deductCredits: jest.fn(),
            checkAndConsumeCredits: jest.fn(),
          },
        },
        {
          provide: JobSearchService,
          useValue: {
            getJobsByStatus: jest.fn().mockResolvedValue({
              jobs: [],
              stats: {},
              pagination: { page: 1, limit: 10, total: 0, totalPages: 0 },
            }),
            searchPublicJobs: jest.fn().mockResolvedValue({
              jobs: [],
              total: 0,
              page: 1,
              limit: 10,
              totalPages: 0,
            }),
          },
        },
        {
          provide: JobPublishingService,
          useValue: {
            publishJob: jest.fn().mockResolvedValue({ success: true }),
            unpublishJob: jest.fn().mockResolvedValue({ success: true }),
            publishToLinkedIn: jest.fn().mockResolvedValue({ success: true }),
          },
        },
        {
          provide: JobSeekerJobsService,
          useValue: {
            getJobsForJobSeeker: jest.fn().mockResolvedValue([]),
            getJobApplications: jest.fn().mockResolvedValue([]),
            applyForJob: jest.fn().mockResolvedValue({ success: true }),
          },
        },
        {
          provide: JobAIOperationsService,
          useValue: {
            generateJobDescription: jest.fn().mockResolvedValue('Generated job description'),
            extractJobRequirements: jest.fn().mockResolvedValue({
              skills: [],
              experience: '',
              education: '',
            }),
            generateMatchingCriteria: jest.fn().mockResolvedValue({
              criteria: [],
            }),
          },
        },
        {
          provide: JobStatsService,
          useValue: {
            getJobStats: jest.fn().mockResolvedValue({
              totalJobs: 0,
              openJobs: 0,
              closedJobs: 0,
              draftJobs: 0,
            }),
            getCandidateStats: jest.fn().mockResolvedValue({
              totalCandidates: 0,
              topTier: 0,
              secondTier: 0,
              others: 0,
            }),
            updateJobStats: jest.fn().mockResolvedValue({ success: true }),
          },
        },
        {
          provide: JobTransformationService,
          useValue: {
            transformJobResponse: jest.fn().mockImplementation((job) => job),
            transformJobsResponse: jest.fn().mockImplementation((jobs) => jobs),
            enrichJobWithStats: jest.fn().mockImplementation((job) => job),
          },
        },
        {
          provide: JobCriteriaService,
          useValue: {
            generateCriteria: jest.fn().mockResolvedValue({
              criteria: [],
            }),
            updateCriteria: jest.fn().mockResolvedValue({ success: true }),
            validateCriteria: jest.fn().mockResolvedValue({ isValid: true }),
          },
        },
        {
          provide: JobMatchRankOperationsService,
          useValue: {
            matchAndRankCandidates: jest.fn().mockResolvedValue({
              rankedCandidates: [],
              stats: {},
            }),
            updateMatchScores: jest.fn().mockResolvedValue({ success: true }),
            recalculateRankings: jest.fn().mockResolvedValue({ success: true }),
          },
        },
        {
          provide: LinkedInService,
          useValue: {
            postJob: jest.fn(),
          },
        },
        {
          provide: JobMatchRankHelper,
          useValue: {
            processMatchRank: jest.fn(),
          },
        },
        {
          provide: JobAiHelpers,
          useValue: {
            generateJobDescription: jest.fn(),
          },
        },
        {
          provide: JobCrudUtils,
          useValue: {
            validateJobAccess: jest.fn(),
          },
        },
        {
          provide: EnhancedJobPublishingService,
          useValue: {
            publishJob: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    })
      .overrideGuard(Auth0Guard)
      .useValue({
        canActivate: () => true,
      })
      .compile();

    app = module.createNestApplication();
    await app.init();

    jobRepository = module.get<Repository<Job>>(getRepositoryToken(Job));
    candidateRepository = module.get<Repository<Candidate>>(getRepositoryToken(Candidate));
  });

  afterEach(async () => {
    await app.close();
  });

  describe('GET /jobs/:id/candidates', () => {
    it('should return optimized response structure', async () => {
      const response = await request(app.getHttpServer())
        .get('/jobs/123e4567-e89b-12d3-a456-426614174000/candidates')
        .expect(200);

      // Verify response structure
      expect(response.body).toHaveProperty('job');
      expect(response.body).toHaveProperty('candidates');
      expect(response.body).toHaveProperty('stats');

      // Verify job context includes required fields
      expect(response.body.job).toMatchObject({
        id: '123e4567-e89b-12d3-a456-426614174000',
        jobType: 'Software Engineer',
        department: 'Engineering',
        companyName: 'Test Company',
        topCandidateThreshold: 80,
        secondTierCandidateThreshold: 60,
        status: 'OPEN',
      });

      // Verify candidates structure (organized by tiers)
      expect(response.body.candidates).toHaveProperty('topTier');
      expect(response.body.candidates).toHaveProperty('secondTier');
      expect(response.body.candidates).toHaveProperty('others');
      expect(response.body.candidates).toHaveProperty('unranked');
      expect(response.body.candidates).toHaveProperty('shortlisted');

      // Total candidates across all tiers should be 2
      const totalCandidates =
        response.body.candidates.topTier.length +
        response.body.candidates.secondTier.length +
        response.body.candidates.others.length +
        response.body.candidates.unranked.length;
      expect(totalCandidates).toBe(2);

      // Verify first candidate in topTier has required properties
      if (response.body.candidates.topTier.length > 0) {
        expect(response.body.candidates.topTier[0]).toHaveProperty('tier');
        expect(response.body.candidates.topTier[0]).toHaveProperty('jobStats');
        expect(response.body.candidates.topTier[0].jobStats).toMatchObject({
          topCandidateThreshold: 80,
          secondTierCandidateThreshold: 60,
        });
      }

      // Verify stats
      expect(response.body.stats).toMatchObject({
        totalCandidates: 2,
        topTierCount: 1,
        secondTierCount: 1,
        othersCount: 0,
        shortlistedCount: 1,
      });
    });

    it('should not include pagination parameters in response', async () => {
      const response = await request(app.getHttpServer())
        .get('/jobs/123e4567-e89b-12d3-a456-426614174000/candidates')
        .expect(200);

      // Verify no pagination in response
      expect(response.body).not.toHaveProperty('pagination');
      expect(response.body).not.toHaveProperty('page');
      expect(response.body).not.toHaveProperty('limit');
      expect(response.body).not.toHaveProperty('totalPages');
    });

    it('should return all candidates without pagination', async () => {
      const response = await request(app.getHttpServer())
        .get('/jobs/123e4567-e89b-12d3-a456-426614174000/candidates')
        .expect(200);

      // Should return all candidates across all tiers (no pagination)
      const totalCandidates =
        response.body.candidates.topTier.length +
        response.body.candidates.secondTier.length +
        response.body.candidates.others.length +
        response.body.candidates.unranked.length;
      expect(totalCandidates).toBe(2);
      expect(response.body.stats.totalCandidates).toBe(2);
    });

    it('should handle non-existent job', async () => {
      // Mock JobService to throw NotFoundException for non-existent job
      const jobService = app.get(JobService);
      jest
        .spyOn(jobService, 'findCandidates')
        .mockRejectedValue(new NotFoundException('Job not found'));

      await request(app.getHttpServer()).get('/jobs/123e4567-e89b-12d3-a456-426614174001/candidates').expect(404);
    });

    it('should handle job with no candidates', async () => {
      // Mock JobService to return empty candidates
      const jobService = app.get(JobService);
      jest.spyOn(jobService, 'findCandidates').mockResolvedValue({
        job: mockJob,
        candidates: {
          topTier: [],
          secondTier: [],
          others: [],
          unranked: [],
          shortlisted: [],
        },
        stats: {
          totalCandidates: 0,
          topTierCount: 0,
          secondTierCount: 0,
          othersCount: 0,
          shortlistedCount: 0,
          unrankedCount: 0,
        },
      });

      const response = await request(app.getHttpServer())
        .get('/jobs/123e4567-e89b-12d3-a456-426614174000/candidates')
        .expect(200);

      expect(response.body.candidates.topTier).toEqual([]);
      expect(response.body.candidates.secondTier).toEqual([]);
      expect(response.body.candidates.others).toEqual([]);
      expect(response.body.candidates.unranked).toEqual([]);
      expect(response.body.candidates.shortlisted).toEqual([]);
      expect(response.body.stats.totalCandidates).toBe(0);
      expect(response.body.job).toBeDefined(); // Job context should still be present
    });
  });

  describe('Response Performance', () => {
    it('should respond within reasonable time', async () => {
      const startTime = Date.now();

      await request(app.getHttpServer()).get('/jobs/123e4567-e89b-12d3-a456-426614174000/candidates').expect(200);

      const responseTime = Date.now() - startTime;
      expect(responseTime).toBeLessThan(1000); // Should respond within 1 second
    });

    it('should include all required data in single response', async () => {
      const response = await request(app.getHttpServer())
        .get('/jobs/123e4567-e89b-12d3-a456-426614174000/candidates')
        .expect(200);

      // Verify all required data is present in single response
      const requiredJobFields = [
        'id',
        'jobType',
        'department',
        'topCandidateThreshold',
        'secondTierCandidateThreshold',
      ];
      const requiredCandidateFields = ['id', 'fullName', 'tier', 'jobStats', 'evaluation'];
      const requiredStatsFields = [
        'totalCandidates',
        'topTierCount',
        'secondTierCount',
        'othersCount',
        'shortlistedCount',
      ];

      requiredJobFields.forEach((field) => {
        expect(response.body.job).toHaveProperty(field);
      });

      // Check required fields in the first available candidate from any tier
      const firstCandidate =
        response.body.candidates.topTier[0] ||
        response.body.candidates.secondTier[0] ||
        response.body.candidates.others[0] ||
        response.body.candidates.unranked[0];

      if (firstCandidate) {
        requiredCandidateFields.forEach((field) => {
          expect(firstCandidate).toHaveProperty(field);
        });
      }

      requiredStatsFields.forEach((field) => {
        expect(response.body.stats).toHaveProperty(field);
      });
    });
  });
});
