import { JobStatus as JobStatusType } from '@/shared/types/job.types';
import { Candidate } from '@modules/candidate/entities/candidate.entity';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { countCandidatesForJob } from '@shared/utils/candidate-job-query.util';
import { Repository } from 'typeorm';
import { Job } from '../entities/job.entity';
import { JobService } from '../job.service';
import { JobsResponse, PaginationOptions, PublicJobFilters } from '../jobs.types';

@Injectable()
export class JobSearchService {
  private readonly logger = new Logger(JobSearchService.name);

  constructor(
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
    @InjectRepository(Candidate)
    private readonly candidateRepository: Repository<Candidate>,
    private readonly jobService: JobService,
  ) {}

  /**
   * Get jobs filtered by status with pagination
   */
  async getJobsByStatus(
    clientId: string,
    options: {
      page: number;
      limit: number;
      status?: string;
      includeStats?: boolean;
    },
  ): Promise<{
    data: Job[];
    pagination: {
      currentPage: number;
      totalPages: number;
      totalItems: number;
      itemsPerPage: number;
    };
  }> {
    const { page, limit, status, includeStats } = options;
    const skip = (page - 1) * limit;

    // Build the base query
    const queryBuilder = this.jobRepository
      .createQueryBuilder('job')
      .leftJoinAndSelect('job.videoJDs', 'videoJDs')
      .where('job.clientId = :clientId', { clientId });

    if (status && status !== 'ALL') {
      queryBuilder.andWhere('job.status = :status', { status });
    }

    if (includeStats) {
      // Also load candidates and evaluations when stats are requested
      queryBuilder
        .leftJoinAndSelect('job.candidates', 'candidates')
        .leftJoinAndSelect('job.candidateEvaluations', 'candidateEvaluations');
    }

    const [jobs, totalItems] = await queryBuilder
      .skip(skip)
      .take(limit)
      .orderBy('job.updatedAt', 'DESC')
      .getManyAndCount();

    // Get accurate candidate counts that include appliedJobs array
    // This ensures we count candidates who:
    // 1. Have this job as their primary jobId
    // 2. Have this job in their appliedJobs array (uploaded to multiple jobs)
    // This includes both ranked and unranked candidates
    const jobsWithCandidateCount = await Promise.all(
      jobs.map(async (job) => {
        // Get accurate count using the same query as getCandidatesCount in job.service.ts
        // This counts ALL candidates associated with the job, regardless of evaluation status
        const candidateCount = await countCandidatesForJob(this.candidateRepository, job.id);

        // Check if job has any video with videoUrl
        const hasVideoUrl =
          job.videoJDs?.some((videoJD) => videoJD.videoUrl && videoJD.videoUrl.trim() !== '') ||
          false;

        job.totalCandidates = candidateCount;
        job.hasVideoUrl = hasVideoUrl;
        return job;
      }),
    );

    return {
      data: jobsWithCandidateCount,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(totalItems / limit),
        totalItems,
        itemsPerPage: limit,
      },
    };
  }

  /**
   * Search public jobs with filters
   */
  async searchPublicJobs(searchParams: {
    companyId?: string;
    page: number;
    limit: number;
    filters?: PublicJobFilters;
    search?: string;
  }): Promise<JobsResponse> {
    const { companyId = '', page = 1, limit = 10, filters, search } = searchParams;

    const paginationOptions: PaginationOptions = {
      page: Number(page),
      limit: Number(limit),
    };

    // Use the existing jobService.getPublicJobs to ensure consistency
    return this.jobService.getPublicJobs(companyId, paginationOptions, filters, search);
  }

  /**
   * Get jobs for a specific company
   */
  async getCompanyJobsPublic(companyId: string): Promise<JobsResponse> {
    // Use the existing jobService method to ensure consistency
    return this.jobService.getJobsByCompanyId(companyId);
  }

  /**
   * Get jobs posted by authenticated company/employer
   */
  async getMyJobs(
    clientId: string,
    page: number,
    limit: number,
    relations?: string[],
  ): Promise<JobsResponse> {
    // Get jobs by clientId directly from the database
    const jobs = await this.jobService.findByClientId(clientId, relations || ['videoJDs']);

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedData = jobs.data.slice(startIndex, endIndex);

    const result = {
      ...jobs,
      data: paginatedData,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(jobs.data.length / limit),
        totalItems: jobs.data.length,
        itemsPerPage: limit,
      },
    };

    return result;
  }

  /**
   * Search open jobs (public endpoint)
   */
  async searchOpenJobs(searchParams: {
    companyId?: string;
    page: number;
    limit: number;
    search?: string;
    filters?: PublicJobFilters;
    userId?: string;
  }): Promise<JobsResponse> {
    const { companyId = '', page = 1, limit = 10, filters, search, userId } = searchParams;

    const paginationOptions: PaginationOptions = {
      page: Number(page),
      limit: Number(limit),
    };

    // Use the existing jobService.getPublicJobs to ensure consistency
    return this.jobService.getPublicJobs(companyId || '', paginationOptions, filters, search, userId);
  }

  /**
   * Find job by slug for public viewing
   */
  async findBySlug(slug: string): Promise<Job | null> {
    const job = await this.jobRepository.findOne({
      where: {
        slug,
        isPublished: true,
        status: JobStatusType.OPEN,
      },
      relations: ['videoJDs', 'company'],
    });

    return job;
  }

  /**
   * Get job details for public application
   */
  async getJobForPublicApplication(id: string): Promise<Job | null> {
    const job = await this.jobRepository.findOne({
      where: {
        id,
        isPublished: true,
        status: JobStatusType.OPEN,
      },
      relations: ['videoJDs', 'company'],
    });

    return job;
  }

  /**
   * Get all jobs with match rank information
   */
  async getMatchRanked(userId: string, page: number, limit: number) {
    const result = await this.jobService.getMatchRankedJobs(userId, {
      page,
      limit,
    });

    // Transform the data to include metadata
    const transformedData = result.data.map((job) => ({
      id: job.id,
      companyName: job.companyName,
      jobType: job.jobType,
      department: job.department,
      candidates: job.candidates,
      updatedAt: job.updatedAt,
      metadata: {
        totalCandidates: job.candidates?.length || 0,
        topTierCount:
          job.candidates?.filter((c: Candidate) => (c.evaluation?.matchScore ?? 0) >= 70).length ||
          0,
        secondTierCount:
          job.candidates?.filter((c: Candidate) => {
            const score = c.evaluation?.matchScore ?? 0;
            return score >= 50 && score < 70;
          }).length || 0,
      },
      status: this.getJobStatus(job.candidates || []),
    }));

    return {
      data: transformedData,
      pagination: result.pagination,
    };
  }

  private getJobStatus(candidates: Candidate[]): string {
    const topMatches = candidates.filter((c) => (c.evaluation?.matchScore ?? 0) >= 70).length;
    const goodMatches = candidates.filter((c) => {
      const score = c.evaluation?.matchScore ?? 0;
      return score >= 50 && score < 70;
    }).length;

    if (topMatches > 0) return `${topMatches} Top Matches`;
    if (goodMatches > 0) return `${goodMatches} Good Matches`;
    return 'No Strong Matches';
  }
}
