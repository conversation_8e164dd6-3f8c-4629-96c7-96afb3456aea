import { Repository } from 'typeorm';

import { Company } from '@modules/company/entities/company.entity';
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { CompanyService } from '../company/company.service';
import { Job } from './entities/job.entity';
import { getPublicJobsHelper } from './job.helpers';
import { JobsResponse, PaginationOptions, PublicJobFilters } from './jobs.types';

@Injectable()
export class JobCrudUtils {
  private readonly logger = new Logger(JobCrudUtils.name);

  constructor(
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
    private readonly companyService: CompanyService,
    @InjectRepository(Company)
    private readonly companyRepository: Repository<Company>,
  ) {}

  async findAll(): Promise<JobsResponse> {
    const jobs = await this.jobRepository
      .createQueryBuilder('job')
      .leftJoinAndSelect('job.candidates', 'candidate', 'candidate.jobId = job.id')
      .leftJoinAndSelect('job.videoJDs', 'videoJDs')
      .leftJoinAndSelect('candidate.videoResponses', 'videoResponses')
      .leftJoinAndSelect('candidate.evaluations', 'candidateEvaluations')
      .where('job.jobType IS NOT NULL')
      .andWhere('job.department IS NOT NULL')
      .andWhere('job.companyName IS NOT NULL')
      .orderBy('job.updatedAt', 'DESC')
      .addOrderBy(
        "CASE candidate.status WHEN 'CULTURAL_FIT_ANSWERED' THEN 0 WHEN 'MATCHED' THEN 1 ELSE 2 END",
        'ASC',
      )
      .addOrderBy("COALESCE((candidate.evaluation->'matchScore')::float, 0)", 'DESC')
      .setParameter('matchedStatus', 'MATCHED')
      .getMany();

    // Process each job to use the job-specific evaluations
    const processedJobs = await Promise.all(jobs.map((job) => this.processJobCandidates(job)));

    const filteredJobs = processedJobs.filter((job) => job.isComplete());

    return {
      data: filteredJobs,
      metadata: {
        total: filteredJobs.length,
        filtered: filteredJobs.length,
      },
      pagination: {
        currentPage: 1,
        totalPages: 1,
        totalItems: filteredJobs.length,
        itemsPerPage: filteredJobs.length,
      },
    };
  }

  async findByClientId(
    userId: string,
    relations?: string[],
    requirePublished: boolean = false,
  ): Promise<JobsResponse> {
    const queryBuilder = this.jobRepository
      .createQueryBuilder('job')
      .where('job.clientId = :userId', { userId });

    // For public endpoints, only return published jobs
    if (requirePublished) {
      queryBuilder.andWhere('job.isPublished = :isPublished', { isPublished: true });
    }

    if (relations?.length) {
      relations.forEach((relation) => {
        queryBuilder.leftJoinAndSelect(`job.${relation}`, relation);
      });
    }

    const jobs = await queryBuilder.orderBy('job.createdAt', 'DESC').getMany();

    return {
      data: jobs,
      metadata: {
        total: jobs.length,
        filtered: jobs.length,
      },
      pagination: {
        currentPage: 1,
        totalPages: 1,
        totalItems: jobs.length,
        itemsPerPage: jobs.length,
      },
    };
  }

  async findOne(id: string, relations?: string[], forceFresh: boolean = false): Promise<Job> {
    // Create a query builder with cache disabled if forceFresh is true
    const queryBuilder = this.jobRepository.createQueryBuilder('job').where('job.id = :id', { id });

    if (forceFresh) {
      queryBuilder.cache(false);
    }

    // Process relations, handling 'candidates' and 'candidateEvaluations' specially to avoid duplicate table references
    const hasCandidatesRelation = relations?.includes('candidates');
    const hasCandidateEvaluationsRelation = relations?.includes('candidateEvaluations');
    const filteredRelations =
      relations?.filter(
        (relation) => relation !== 'candidates' && relation !== 'candidateEvaluations',
      ) || [];

    // Include filtered relations if provided
    if (filteredRelations.length) {
      filteredRelations.forEach((relation) => {
        queryBuilder.leftJoinAndSelect(`job.${relation}`, relation);
      });
    }

    // Handle candidates relation separately to avoid duplicate table references
    if (hasCandidatesRelation) {
      queryBuilder.leftJoinAndSelect('job.candidates', 'candidates', 'candidates.jobId = job.id');
    }

    // Handle candidate evaluations relation - join directly to job to avoid N+1 queries
    if (hasCandidateEvaluationsRelation) {
      queryBuilder.leftJoinAndSelect('job.candidateEvaluations', 'candidateEvaluations');
    }

    const job = await queryBuilder.getOne();
    if (!job) {
      throw new NotFoundException(`Job with ID ${id} not found`);
    }

    // Verify that the job ID matches the requested ID
    if (job.id !== id) {
      this.logger.error(`Wrong job returned! Requested ID: ${id}, Got ID: ${job.id}`);
      throw new NotFoundException(`Job with ID ${id} not found (got ${job.id} instead)`);
    }

    // Only fetch additional candidates if we need them and don't already have enough
    if (relations?.includes('candidates') && (!job.candidates || job.candidates.length < 10)) {
      // Get candidates that have this job in their appliedJobs array
      const additionalCandidates = await this.jobRepository.manager
        .createQueryBuilder()
        .select('candidate')
        .from('candidates', 'candidate')
        .where('candidate."appliedJobs" && ARRAY[:jobId]::uuid[]', { jobId: id })
        .limit(50) // Limit to prevent performance issues
        .getMany();

      // Add these candidates to the job's candidates array if they're not already there
      if (additionalCandidates.length > 0) {
        if (!job.candidates) {
          job.candidates = [];
        }

        // Add only candidates that aren't already in the array
        const existingIds = new Set(job.candidates.map((c) => c.id));
        additionalCandidates.forEach((candidate) => {
          if (!existingIds.has(candidate.id)) {
            job?.candidates?.push(candidate as any);
            existingIds.add(candidate.id);
          }
        });
      }
    }

    // If clientId is missing, try to find the company and set it (optimized query)
    if (!job.clientId && job.companyName) {
      this.logger.warn(`Job ${id} has no clientId set. Attempting to fix...`);
      try {
        // Use a more efficient query to find the company by name
        const matchingCompany = await this.companyRepository.findOne({
          where: { companyName: job.companyName },
          select: ['id', 'clientId', 'companyName'],
        });

        if (matchingCompany && matchingCompany.clientId) {
          job.clientId = matchingCompany.clientId;
          // Use update instead of save for better performance
          await this.jobRepository.update(id, { clientId: matchingCompany.clientId });
        }
      } catch (error: any) {
        this.logger.error(`Failed to update clientId for job ${id}: ${error.message}`);
      }
    }

    // Calculate unevaluated candidates count more efficiently
    const unevaluatedCandidatesCount =
      job.candidates && job.candidateEvaluations
        ? job.candidates.length -
          new Set(
            job.candidateEvaluations.filter((e) => e.jobId === job.id).map((e) => e.candidateId),
          ).size
        : job.candidates?.length || 0;

    // Prepare job stats that will be needed by the frontend
    const jobStats = {
      jobTitle: job.jobType,
      totalCandidates: job.candidates?.length || 0,
      unevaluatedCandidates: unevaluatedCandidatesCount,
      topCandidateThreshold: job.topCandidateThreshold
        ? Number(job.topCandidateThreshold) > 1
          ? Number(job.topCandidateThreshold) // Already in percentage format
          : Number(job.topCandidateThreshold) * 100 // Convert from decimal to percentage
        : undefined, // No default - use job entity value only
      secondTierCandidateThreshold: job.secondTierCandidateThreshold
        ? Number(job.secondTierCandidateThreshold) > 1
          ? Number(job.secondTierCandidateThreshold) // Already in percentage format
          : Number(job.secondTierCandidateThreshold) * 100 // Convert from decimal to percentage
        : undefined, // No default - use job entity value only
      companyName: job.companyName,
      department: job.department,
    };

    // If we have candidates and their evaluations, process them efficiently
    if (job.candidates && job.candidateEvaluations) {
      // Create a map for faster lookup of evaluations by candidate ID
      const evaluationMap = new Map();
      job.candidateEvaluations.forEach((evaluation) => {
        if (evaluation.jobId === job.id) {
          if (!evaluationMap.has(evaluation.candidateId)) {
            evaluationMap.set(evaluation.candidateId, []);
          }
          evaluationMap.get(evaluation.candidateId).push(evaluation);
        }
      });

      // Map candidate evaluations to their respective candidates
      job.candidates.forEach((candidate) => {
        const candidateEvaluations = evaluationMap.get(candidate.id) || [];

        // Assign the evaluations to the candidate
        if (candidateEvaluations.length > 0) {
          (candidate as any).evaluations = candidateEvaluations;

          // If the candidate has an evaluation field, make sure it's for this job
          if (candidateEvaluations[0].evaluation) {
            (candidate as any).evaluation = candidateEvaluations[0].evaluation;
          }
        }

        // Add job stats to each candidate for easy access in the frontend
        (candidate as any).jobStats = jobStats;

        // Include computed field for video intro email status
        (candidate as any).videoIntroEmailSent =
          (candidate as any).getVideoIntroEmailSent?.(job.id) || false;
      });

      // Sort candidates by match score in descending order
      job.candidates.sort((a, b) => {
        const scoreA = (a as any).evaluation?.matchScore || 0;
        const scoreB = (b as any).evaluation?.matchScore || 0;
        return scoreB - scoreA;
      });

      // Assign ranks to candidates based on match score
      // Candidates with the same score should have the same rank
      let currentRank = 1;
      let previousScore = -1;
      let sameRankCount = 0;

      job.candidates.forEach((candidate, index) => {
        // Ensure candidate has an evaluation object
        if (!(candidate as any).evaluation) {
          (candidate as any).evaluation = {
            matchScore: 0,
            criterionMatchedOn: [],
            yourReasoningForScoring: null,
            detailedScoreAnalysis: null,
            lastEvaluatedAt: new Date(),
          };
        }

        const currentScore = (candidate as any).evaluation?.matchScore || 0;
        const secondThreshold = jobStats.secondTierCandidateThreshold || 60;

        // Only assign rank if score meets minimum threshold
        if (currentScore < secondThreshold) {
          (candidate as any).evaluation.rank = null;
        } else {
          // If this is the first candidate or the score is different from the previous one
          if (index === 0 || currentScore !== previousScore) {
            // If this isn't the first candidate, update the rank
            if (index > 0) {
              currentRank += sameRankCount;
              sameRankCount = 0;
            }

            // Assign the current rank to this candidate
            (candidate as any).evaluation.rank = currentRank;

            previousScore = currentScore;
            sameRankCount = 1;
          } else {
            // If the score is the same as the previous one, assign the same rank
            (candidate as any).evaluation.rank = currentRank;
            sameRankCount++;
          }
        }
      });
    }

    // Also add job stats to the job object itself
    (job as any).stats = jobStats;

    return job;
  }

  async findBySlug(slug: string): Promise<Job> {
    const job = await this.jobRepository
      .createQueryBuilder('job')
      .leftJoinAndSelect('job.candidates', 'candidate')
      .leftJoinAndSelect('candidate.videoResponses', 'videoResponses')
      .leftJoinAndSelect('candidate.evaluations', 'candidateEvaluations')
      .where('job.slug = :slug', { slug })
      .getOne();

    if (!job) {
      throw new NotFoundException(`Job with slug ${slug} not found`);
    }
    return this.processJobCandidates(job);
  }

  async findByJobId(
    id: string,
    relations?: string[],
    requirePublished: boolean = false,
  ): Promise<Job> {
    const queryBuilder = this.jobRepository.createQueryBuilder('job').where('job.id = :id', { id });

    // For public endpoints, only return published jobs
    if (requirePublished) {
      queryBuilder.andWhere('job.isPublished = :isPublished', { isPublished: true });
    }

    // Include relations if provided
    if (relations?.length) {
      relations.forEach((relation) => {
        queryBuilder.leftJoinAndSelect(`job.${relation}`, relation);
      });
    }

    const job = await queryBuilder.getOne();

    if (!job && requirePublished) {
      // Check if job exists but is not published
      const unpublishedJob = await this.jobRepository.findOne({
        where: { id },
        select: ['id', 'isPublished', 'jobType'],
      });

      if (unpublishedJob) {
        throw new NotFoundException(`Job with ID ${id} is not publicly available`);
      }
    }

    if (!job) {
      throw new NotFoundException(`Job with ID ${id} not found`);
    }
    return job;
  }

  async findJobWithCandidates(jobId: string): Promise<Job> {
    const jobWithCandidates = await this.jobRepository
      .createQueryBuilder('job')
      .leftJoinAndSelect('job.candidates', 'candidate')
      .leftJoinAndSelect('candidate.videoResponses', 'videoResponses')
      .leftJoinAndSelect('candidate.evaluations', 'candidateEvaluations')
      .where('job.id = :jobId', { jobId })
      .andWhere('candidate.id IS NOT NULL') // Ensure we only get jobs with candidates
      .orderBy('candidateEvaluations.matchScore', 'DESC')
      .getOne();

    if (!jobWithCandidates) {
      throw new NotFoundException(`Job with ID ${jobId} not found or has no candidates`);
    }

    // Process the job to use the job-specific evaluations
    return this.processJobCandidates(jobWithCandidates);
  }

  async getJobsByCompanyId(companyId: string, relations?: string[]): Promise<JobsResponse> {
    const queryBuilder = this.jobRepository
      .createQueryBuilder('job')
      .leftJoin('job.company', 'company')
      .where('company.id = :companyId', { companyId });

    // Add any additional relations if provided
    if (relations?.length) {
      relations.forEach((relation) => {
        queryBuilder.leftJoinAndSelect(`job.${relation}`, relation);
      });
    }

    queryBuilder.orderBy('job.createdAt', 'DESC');

    const jobs = await queryBuilder.getMany();

    return {
      data: jobs,
      metadata: {
        total: jobs.length,
        filtered: jobs.length,
      },
      pagination: {
        currentPage: 1,
        totalPages: 1,
        totalItems: jobs.length,
        itemsPerPage: jobs.length,
      },
    };
  }

  async getPublicJobs(
    companyId: string,
    paginationOptions: PaginationOptions,
    filters?: PublicJobFilters,
    search?: string,
    userId?: string,
  ): Promise<JobsResponse> {
    return getPublicJobsHelper(
      this.jobRepository,
      this.companyService,
      companyId,
      paginationOptions,
      filters,
      search,
      userId,
    );
  }

  static processJobCandidatesStatic(job: Job): Job {
    if (job.candidates && job.candidates.length > 0) {
      // Process each candidate to use the job-specific evaluation
      job.candidates = job.candidates.map((candidate) => {
        // Use the getEvaluationForJob method to get the correct evaluation for this job
        if (candidate.getEvaluationForJob) {
          candidate.evaluation = candidate.getEvaluationForJob(job.id);
        }

        // Include computed field for video intro email status
        (candidate as any).videoIntroEmailSent =
          (candidate as any).getVideoIntroEmailSent?.(job.id) || false;

        return candidate;
      });

      // Sort candidates by match score in descending order
      job.candidates.sort((a, b) => {
        const scoreA = (a as any).evaluation?.matchScore || 0;
        const scoreB = (b as any).evaluation?.matchScore || 0;
        return scoreB - scoreA;
      });

      // Assign ranks to candidates based on match score
      // Candidates with the same score should have the same rank
      let currentRank = 1;
      let previousScore = -1;
      let sameRankCount = 0;

      job.candidates.forEach((candidate, index) => {
        // Ensure candidate has an evaluation object
        if (!(candidate as any).evaluation) {
          (candidate as any).evaluation = {
            matchScore: 0,
            criterionMatchedOn: [],
            yourReasoningForScoring: null,
            detailedScoreAnalysis: null,
            lastEvaluatedAt: new Date(),
          };
        }

        const currentScore = (candidate as any).evaluation?.matchScore || 0;
        const secondThreshold = job.secondTierCandidateThreshold || 60;

        // Only assign rank if score meets minimum threshold
        if (currentScore < secondThreshold) {
          (candidate as any).evaluation.rank = null;
        } else {
          // If this is the first candidate or the score is different from the previous one
          if (index === 0 || currentScore !== previousScore) {
            // If this isn't the first candidate, update the rank
            if (index > 0) {
              currentRank += sameRankCount;
              sameRankCount = 0;
            }

            // Assign the current rank to this candidate
            (candidate as any).evaluation.rank = currentRank;

            previousScore = currentScore;
            sameRankCount = 1;
          } else {
            // If the score is the same as the previous one, assign the same rank
            (candidate as any).evaluation.rank = currentRank;
            sameRankCount++;
          }
        }
      });
    }
    return job;
  }

  async processJobCandidates(job: Job): Promise<Job> {
    return JobCrudUtils.processJobCandidatesStatic(job);
  }
}
