import { OpenAI } from 'openai';
import { Repository } from 'typeorm';

import { JobStatus as JobStatusType } from '@/shared/types/job.types';
import { Candidate } from '@modules/candidate/entities/candidate.entity';
import { NotFoundException } from '@nestjs/common';

import { normalizeJobThresholds } from '@/shared/utils/threshold.util';
import { CompanyService } from '../company/company.service';
import { Job } from './entities/job.entity';
import { JobService } from './job.service';

interface PaginationOptions {
  page: number;
  limit: number;
  status?: string;
}

interface PublicJobFilters {
  location?: string;
  jobType?: string;
  department?: string;
  industry?: string;
  companySize?: string;
  layoutPreference?: string;
}

interface MatchRankResponse {
  topTier: {
    candidates: Candidate[];
    total: number;
  };
  secondTier: {
    candidates: Candidate[];
    total: number;
  };
  others: {
    candidates: Candidate[];
    total: number;
  };
  unranked?: {
    candidates: Candidate[];
    total: number;
  };
  shortlisted?: {
    candidates: Candidate[];
    total: number;
  };
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
  stats?: {
    totalCandidates: number;
    evaluatedCandidates: number;
    pendingEvaluation: number;
    averageMatchScore: number;
    matchingStats: {
      topTierCount: number;
      secondTierCount: number;
      belowThresholdCount: number;
      topTierThreshold: number;
      secondTierThreshold: number;
    };
    engagementStats: {
      contacted: number;
      notContacted: number;
      completedVideoInterviews: number;
      videoInterviewCompletionRate: number;
    };
  };
}

interface JobsResponse {
  data: Job[];
  metadata: {
    total: number;
    filtered: number;
    preferences?: {
      jobTypes: string[];
      departments: string[];
      locations: string[];
      industries?: string[];
      companySizes?: string[];
      remotePreference?: string;
    };
    contextual?: {
      hasPreferences: boolean;
      totalMatchingJobs: number;
      recommendedJobs: number;
      averageMatchScore: number;
      recommendations: {
        message: string;
        shouldUpdatePreferences: boolean;
      };
    };
  };
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
}

interface JobsByStatusResponse {
  jobsByStatus: Record<string, { jobs: Job[]; count: number }>;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
}

export function calculateJobStatus(candidates: Candidate[]): string {
  const topMatches = candidates.filter(
    (c) => Math.round((c.evaluation?.matchScore ?? 0) * 100) >= 70,
  ).length;
  const goodMatches = candidates.filter((c) => {
    const score = Math.round((c.evaluation?.matchScore ?? 0) * 100);
    return score >= 50 && score < 70;
  }).length;

  if (topMatches > 0) return `${topMatches} Top Matches`;
  if (goodMatches > 0) return `${goodMatches} Good Matches`;
  return 'No Strong Matches';
}

// Helper function to calculate preference match score
function calculatePreferenceMatch(job: Job, preferences: any): {
  score: number;
  matches: string[];
  mismatches: string[];
  details: any;
} {
  if (!preferences) {
    return { score: 0, matches: [], mismatches: [], details: {} };
  }

  let totalScore = 0;
  let maxScore = 0;
  const matches: string[] = [];
  const mismatches: string[] = [];
  const details: any = {};

  // Job Type matching (30% weight)
  if (preferences.jobTypes?.length > 0) {
    maxScore += 30;
    const jobTypeMatch = preferences.jobTypes.some((prefType: string) => 
      job.jobType?.toLowerCase().includes(prefType.toLowerCase()) ||
      prefType.toLowerCase().includes(job.jobType?.toLowerCase() || '')
    );
    if (jobTypeMatch) {
      totalScore += 30;
      matches.push('Job type matches your preferences');
      details.jobType = { match: true, preferred: preferences.jobTypes, actual: job.jobType };
    } else {
      mismatches.push('Job type differs from your preferences');
      details.jobType = { match: false, preferred: preferences.jobTypes, actual: job.jobType };
    }
  }

  // Location matching (25% weight)
  if (preferences.locations?.length > 0) {
    maxScore += 25;
    const locationMatch = preferences.locations.some((prefLocation: string) => 
      job.location?.some((jobLoc: string) => 
        jobLoc.toLowerCase().includes(prefLocation.toLowerCase()) ||
        prefLocation.toLowerCase().includes(jobLoc.toLowerCase())
      ) || false
    );
    if (locationMatch) {
      totalScore += 25;
      matches.push('Location matches your preferences');
      details.location = { match: true, preferred: preferences.locations, actual: job.location };
    } else {
      mismatches.push('Location differs from your preferences');
      details.location = { match: false, preferred: preferences.locations, actual: job.location };
    }
  }

  // Remote work preference matching (20% weight)
  if (preferences.remotePreference) {
    maxScore += 20;
    // Check if job supports remote work based on job description or location
    const jobSupportsRemote = 
      job.location?.some((loc: string) => loc.toLowerCase().includes('remote')) ||
      job.typeOfJob?.toLowerCase().includes('remote') ||
      job.jobResponsibilities?.some((resp: string) => resp.toLowerCase().includes('remote')) ||
      job.companyDescription?.toLowerCase().includes('remote');
    
    const preferenceMatch = 
      (preferences.remotePreference === 'remote' && jobSupportsRemote) ||
      (preferences.remotePreference === 'flexible') ||
      (preferences.remotePreference === 'hybrid' && (jobSupportsRemote || (job.location && job.location.length > 0))) ||
      (preferences.remotePreference === 'onsite' && !jobSupportsRemote);

    if (preferenceMatch) {
      totalScore += 20;
      matches.push('Work arrangement matches your preferences');
      details.remoteWork = { match: true, preferred: preferences.remotePreference, supports: jobSupportsRemote };
    } else {
      mismatches.push('Work arrangement differs from your preferences');
      details.remoteWork = { match: false, preferred: preferences.remotePreference, supports: jobSupportsRemote };
    }
  }

  // Industry matching (15% weight) - based on company industry
  if (preferences.industries?.length > 0) {
    maxScore += 15;
    const industryMatch = preferences.industries.some((prefIndustry: string) => 
      job.company?.industry?.toLowerCase().includes(prefIndustry.toLowerCase()) ||
      prefIndustry.toLowerCase().includes(job.company?.industry?.toLowerCase() || '')
    );
    if (industryMatch) {
      totalScore += 15;
      matches.push('Industry matches your preferences');
      details.industry = { match: true, preferred: preferences.industries, actual: job.company?.industry };
    } else {
      mismatches.push('Industry differs from your preferences');
      details.industry = { match: false, preferred: preferences.industries, actual: job.company?.industry };
    }
  }

  // Salary matching (10% weight) - rough matching based on available salary info
  if (preferences.desiredSalary?.min || preferences.desiredSalary?.max) {
    maxScore += 10;
    // This is basic matching - job might not have detailed salary info
    if (job.salaryRange && job.salaryRange.trim()) {
      // For now, just give credit for having salary information
      totalScore += 5;
      matches.push('Job includes salary information');
      details.salary = { match: 'partial', hasInfo: true, jobSalary: job.salaryRange };
    } else {
      details.salary = { match: false, hasInfo: false, preferred: preferences.desiredSalary };
    }
  }

  // Calculate final percentage
  const finalScore = maxScore > 0 ? Math.round((totalScore / maxScore) * 100) : 0;

  return {
    score: finalScore,
    matches,
    mismatches,
    details
  };
}

export function calculateJobStats(job: Job) {
  const totalCandidates = job.candidates?.length || 0;
  // Use job entity thresholds - no hardcoded defaults
  const topThreshold = job.topCandidateThreshold;
  const secondThreshold = job.secondTierCandidateThreshold;

  // Count candidates by tier (only if thresholds are set)
  let topTierCount = 0;
  let secondTierCount = 0;

  if (topThreshold !== undefined && secondThreshold !== undefined) {
    topTierCount =
      job.candidates?.filter((candidate) => {
        const score = candidate.evaluation?.matchScore || 0;
        return score >= topThreshold;
      }).length || 0;

    secondTierCount =
      job.candidates?.filter((candidate) => {
        const score = candidate.evaluation?.matchScore || 0;
        return score >= secondThreshold && score < topThreshold;
      }).length || 0;
  }

  const belowThresholdCount = totalCandidates - topTierCount - secondTierCount;

  // Calculate percentages
  const topTierPercentage = totalCandidates > 0 ? (topTierCount / totalCandidates) * 100 : 0;
  const secondTierPercentage = totalCandidates > 0 ? (secondTierCount / totalCandidates) * 100 : 0;

  // Count candidates with completed video interviews
  const completedVideoInterviews =
    job.candidates?.filter((candidate) => candidate.hasCompletedVideoInterview).length || 0;

  // Count contacted candidates
  const contactedCandidates =
    job.candidates?.filter((candidate) => candidate.contacted).length || 0;

  // Calculate average match score
  let totalScore = 0;
  let scoredCandidates = 0;

  job.candidates?.forEach((candidate) => {
    if (candidate.evaluation?.matchScore) {
      totalScore += candidate.evaluation.matchScore;
      scoredCandidates++;
    }
  });

  const averageMatchScore = scoredCandidates > 0 ? totalScore / scoredCandidates : 0;

  // Count candidates that need evaluation
  const pendingEvaluation =
    job.candidates?.filter((candidate) => !candidate.evaluation?.matchScore).length || 0;

  // Get latest video JD status
  const latestVideoJD = job.videoJDs?.sort(
    (a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(),
  )[0];

  return {
    candidateStats: {
      totalCandidates,
      topTierCount,
      topTierPercentage: Math.round(topTierPercentage),
      secondTierCount,
      secondTierPercentage: Math.round(secondTierPercentage),
      belowThresholdCount,
      topThreshold,
      secondThreshold,
      completedVideoInterviews,
      videoInterviewCompletionRate:
        totalCandidates > 0 ? (completedVideoInterviews / totalCandidates) * 100 : 0,
      contactedCandidates,
      contactRate: totalCandidates > 0 ? (contactedCandidates / totalCandidates) * 100 : 0,
      averageMatchScore: Math.round(averageMatchScore * 100) / 100,
      pendingEvaluation,
      evaluatedCandidates: totalCandidates - pendingEvaluation,
      evaluationCompletionRate:
        totalCandidates > 0 ? ((totalCandidates - pendingEvaluation) / totalCandidates) * 100 : 0,
    },
    actionStats: {
      videoJD: {
        status: latestVideoJD?.status || 'NOT_CREATED',
        url: latestVideoJD?.videoUrl,
        count: job.videoJDs?.length || 0,
      },
      matchAndRank: {
        totalCandidates,
        topTierCount,
        secondTierCount,
        pendingEvaluation,
        evaluationRate:
          Math.round(((totalCandidates - pendingEvaluation) / totalCandidates) * 100) || 0,
      },
      cultureFit: {
        completedInterviews: completedVideoInterviews,
        totalCandidates,
        completionRate: Math.round((completedVideoInterviews / totalCandidates) * 100) || 0,
        contactedCandidates,
        contactRate: Math.round((contactedCandidates / totalCandidates) * 100) || 0,
      },
    },
    displayStatus: generateDisplayStatus(
      totalCandidates,
      topTierCount,
      topTierPercentage,
      secondTierCount,
      secondTierPercentage,
    ),
    videoJDStatus: latestVideoJD?.status,
    videoJDUrl: latestVideoJD?.videoUrl,
  };
}

export function generateDisplayStatus(
  totalCandidates: number,
  topTierCount: number,
  topTierPercentage: number,
  secondTierCount: number,
  secondTierPercentage: number,
): string {
  if (totalCandidates === 0) {
    return 'No candidates';
  }

  if (topTierCount > 0) {
    return `${topTierCount} Top Matches (${Math.round(topTierPercentage)}%)`;
  }

  if (secondTierCount > 0) {
    return `${secondTierCount} Good Matches (${Math.round(secondTierPercentage)}%)`;
  }

  return `${totalCandidates} Candidates Below Threshold`;
}

// Helper method to determine job status based on candidates and their progress
export function determineJobStatus(job: Job): JobStatusType {
  if (!job.candidates || job.candidates.length === 0) {
    return JobStatusType.NEW;
  }

  // Check if any candidates are hired
  if (job.candidates.some((c) => c.status === 'HIRED')) {
    return JobStatusType.HIRED;
  }

  // Check if currently interviewing candidates
  if (job.candidates.some((c) => c.status === 'INTERVIEWING')) {
    return JobStatusType.INTERVIEWING;
  }

  // Check if any candidates are contacted or in a post-contact stage
  const contactedStatuses = [
    'CONTACTED',
    'INTERESTED',
    'NOT_INTERESTED',
    'OFFER_PENDING_APPROVAL',
    'OFFER_APPROVED',
    'OFFER_REJECTED',
    'OFFER_EXTENDED',
    'OFFER_ACCEPTED',
    'OFFER_DECLINED',
    'HIRE_PENDING_APPROVAL',
    'HIRE_APPROVED',
    'CULTURAL_FIT_ANSWERED',
    // 'SHORTLISTED'
  ];

  if (job.candidates.some((c) => contactedStatuses.includes(c.status))) {
    return JobStatusType.CONTACTED;
  }

  // Check if job has matches - safely check evaluation property
  try {
    // Check if any candidates have been evaluated (have match scores)
    const hasEvaluatedCandidates = job.candidates.some(
      (c) => c.evaluation && typeof c.evaluation.matchScore === 'number',
    );

    // Only set to MATCHED if there are actually evaluated candidates
    if (hasEvaluatedCandidates) {
      // Check if any of the evaluated candidates have a score above threshold
      const hasQualifiedMatches = job.candidates.some((c) => {
        // Safely check if evaluation exists and has a matchScore above threshold
        const score = c.evaluation?.matchScore || 0;
        // If score is 0-1 range, convert to percentage, otherwise use as is (assuming 0-100)
        const normalizedScore = score > 1 ? score : score * 100;
        return normalizedScore >= 50;
      });

      if (hasQualifiedMatches) {
        return JobStatusType.MATCHED;
      }
    }
  } catch (error) {
    console.error('Error checking match scores:', error);
    // If there's an error accessing evaluation, default to OPEN
  }

  return JobStatusType.OPEN;
}

export async function getJobsByCompanyId(
  jobRepository: Repository<Job>,
  companyId: string,
): Promise<JobsResponse> {
  const jobs = await jobRepository
    .createQueryBuilder('job')
    .leftJoin('job.company', 'company')
    .where('company.id = :companyId', { companyId })
    .orderBy('job.createdAt', 'DESC')
    .getMany();

  return {
    data: jobs,
    metadata: {
      total: jobs.length,
      filtered: jobs.length,
    },
    pagination: {
      currentPage: 1,
      totalPages: 1,
      totalItems: jobs.length,
      itemsPerPage: jobs.length,
    },
  };
}

export async function getPublicJobsHelper(
  jobRepository: Repository<Job>,
  companyService: CompanyService,
  companyId: string,
  paginationOptions: PaginationOptions,
  filters?: PublicJobFilters,
  search?: string,
  userId?: string,
): Promise<JobsResponse> {
  const { page, limit } = paginationOptions;
  const skip = (page - 1) * limit;

  // Get user's applied jobs and job seeker preferences if userId is provided
  let userAppliedJobs: Set<string> = new Set();
  let jobSeekerPreferences: any = null;
  if (userId) {
    try {
      const candidateRepository = jobRepository.manager.getRepository(Candidate);
      const candidate = await candidateRepository
        .createQueryBuilder('candidate')
        .select(['candidate.appliedJobs'])
        .where('candidate.clientId = :userId OR candidate.userId = :userId', { userId })
        .getOne();
      
      if (candidate?.appliedJobs) {
        userAppliedJobs = new Set(candidate.appliedJobs);
      }

      // Also fetch job seeker preferences for contextual matching
      const JobSeeker = jobRepository.manager.getRepository('JobSeeker');
      const jobSeeker = await JobSeeker
        .createQueryBuilder('jobSeeker')
        .select(['jobSeeker.preferences'])
        .where('jobSeeker.clientId = :userId OR jobSeeker.userId = :userId', { userId })
        .getOne();
      
      if (jobSeeker?.preferences) {
        jobSeekerPreferences = jobSeeker.preferences;
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
      // Continue without applied jobs data if there's an error
    }
  }

  // Build the query - IMPORTANT: Do not include candidates data for public endpoints
  const query = jobRepository
    .createQueryBuilder('job')
    .leftJoin('job.company', 'company') // Join company but don't select all fields
    .leftJoin('job.videoJDs', 'videoJDs', 'videoJDs.status = :videoStatus') // Only join, don't select
    // Select only public job fields
    .select([
      'job.id',
      'job.companyName',
      'job.slug',
      'job.companyDescription',
      'job.jobType',
      'job.department',
      'job.experienceLevel',
      'job.isGraduateRole',
      'job.graduateRequirements',
      'job.experience',
      'job.currency',
      'job.salaryRange',
      'job.skills',
      'job.requirements',
      'job.paymentPeriod',
      'job.typeOfHiring',
      'job.typeOfJob',
      'job.status',
      'job.isPublished',
      'job.jobResponsibilities',
      'job.cultureFitDescription',
      'job.cultureFitQuestions',
      'job.culturalFit',
      'job.education',
      'job.language',
      'job.softSkills',
      'job.location',
      'job.benefits',
      'job.careerGrowth',
      'job.tldr',
      'job.socialMediaDescription',
      'job.createdAt',
      'job.updatedAt',
      // Select only public company fields
      'company.id',
      'company.companyName',
      'company.companyWebsite',
      'company.industry',
      'company.size',
      'company.location',
      'company.logo',
      'company.description',
      'company.layoutPreference',
      'company.primaryColor',
      'company.secondaryColor',
      'company.accentColor',
      'company.heroImage',
      'company.featuredImages',
      'company.customCss',
      'company.companyValues',
      'company.isPublished',
    ])
    .addSelect('videoJDs.videoUrl') // Only select the videoUrl field
    .addSelect('videoJDs.updatedAt') // Add updatedAt for sorting
    .where('job.isPublished = :isPublished', {
      isPublished: true,
      videoStatus: 'COMPLETED', // Only get completed videos
    })
    .orderBy('videoJDs.updatedAt', 'DESC'); // Order by updatedAt to get the latest

  // If companyId is provided, filter by company
  if (companyId) {
    const company = await companyService.findOne(companyId);
    query.andWhere('job.clientId = :clientId', { clientId: company.clientId });
  }

  // Apply text search if provided - only search in jobType field
  if (search) {
    query.andWhere('job.jobType ILIKE :search', { search: `%${search}%` });
  }

  // Apply job filters if provided
  if (filters) {
    if (filters.location) {
      query.andWhere('job.location ILIKE :location', { location: `%${filters.location}%` });
    }
    if (filters.jobType) {
      query.andWhere('job.jobType ILIKE :jobType', { jobType: `%${filters.jobType}%` });
    }
    if (filters.department) {
      query.andWhere('job.department ILIKE :department', {
        department: `%${filters.department}%`,
      });
    }

    // Apply company-related filters
    if (filters.industry) {
      query.andWhere('company.industry ILIKE :industry', {
        industry: `%${filters.industry}%`,
      });
    }
    if (filters.companySize) {
      query.andWhere('company.size ILIKE :companySize', {
        companySize: `%${filters.companySize}%`,
      });
    }
    if (filters.layoutPreference) {
      query.andWhere('company.layoutPreference = :layoutPreference', {
        layoutPreference: filters.layoutPreference,
      });
    }
  }

  // Execute the query with pagination
  const [jobEntities, totalCount] = await Promise.all([
    query.clone().orderBy('job.createdAt', 'DESC').skip(skip).take(limit).getMany(),
    query.getCount(),
  ]);

  // Process jobs to extract videoUrl and create valid Job objects
  const jobs = jobEntities.map((job) => {
    // Skip candidate processing for public endpoints - no candidates data loaded
    // Create a new Job instance with the same prototype
    const jobInstance = Object.create(Object.getPrototypeOf(job));

    // Copy all properties from job
    Object.assign(jobInstance, job);

    // Extract videoUrl from videoJDs if available
    if (job.videoJDs && job.videoJDs.length > 0) {
      // Directly set videoUrl as a property (TypeScript will ignore it)
      (jobInstance as any).videoUrl = job.videoJDs[0].videoUrl;
    }

    // Remove sensitive and unnecessary data
    delete (jobInstance as any).videoJDs;
    delete (jobInstance as any).candidates;
    delete (jobInstance as any).candidateEvaluations;
    delete (jobInstance as any).clientId;
    delete (jobInstance as any).externalId;
    delete (jobInstance as any).source;
    delete (jobInstance as any).sourceType;
    delete (jobInstance as any).metrics;
    delete (jobInstance as any).topCandidateThreshold;
    delete (jobInstance as any).secondTierCandidateThreshold;
    delete (jobInstance as any).referralSettings;
    delete (jobInstance as any).hiringManagerDescription;
    delete (jobInstance as any).finalDraft;
    delete (jobInstance as any).generatedJDTone;
    delete (jobInstance as any).generatedJD;

    // Clean up company data if it exists
    if (jobInstance.company) {
      const cleanCompany = {
        id: jobInstance.company.id,
        companyName: jobInstance.company.companyName,
        companyWebsite: jobInstance.company.companyWebsite,
        industry: jobInstance.company.industry,
        size: jobInstance.company.size,
        location: jobInstance.company.location,
        logo: jobInstance.company.logo,
        description: jobInstance.company.description,
        layoutPreference: jobInstance.company.layoutPreference,
        primaryColor: jobInstance.company.primaryColor,
        secondaryColor: jobInstance.company.secondaryColor,
        accentColor: jobInstance.company.accentColor,
        heroImage: jobInstance.company.heroImage,
        featuredImages: jobInstance.company.featuredImages,
        customCss: jobInstance.company.customCss,
        companyValues: jobInstance.company.companyValues,
        isPublished: jobInstance.company.isPublished,
      };
      jobInstance.company = cleanCompany;
    }

    // Add hasApplied field if userId was provided
    if (userId) {
      (jobInstance as any).hasApplied = userAppliedJobs.has(job.id);
    }

    // Add preference matching if job seeker preferences are available
    if (jobSeekerPreferences) {
      const preferenceMatch = calculatePreferenceMatch(job, jobSeekerPreferences);
      (jobInstance as any).preferenceMatch = {
        score: preferenceMatch.score,
        matches: preferenceMatch.matches,
        mismatches: preferenceMatch.mismatches,
        isRecommended: preferenceMatch.score >= 70, // High match jobs are recommended
        matchLevel: preferenceMatch.score >= 80 ? 'excellent' : 
                   preferenceMatch.score >= 60 ? 'good' : 
                   preferenceMatch.score >= 40 ? 'fair' : 'poor'
      };
      // Also add detailed matching info for debugging/advanced UI
      (jobInstance as any).preferenceDetails = preferenceMatch.details;
    }

    return jobInstance;
  });

  // Get distinct values for filter preferences
  const [jobTypes, departments, locations, industries, companySizes] = await Promise.all([
    jobRepository
      .createQueryBuilder('job')
      .select('DISTINCT job.jobType', 'jobType')
      .where('job.jobType IS NOT NULL')
      .getRawMany()
      .then((results) => results.map((r) => r.jobType)),
    jobRepository
      .createQueryBuilder('job')
      .select('DISTINCT job.department', 'department')
      .where('job.department IS NOT NULL')
      .getRawMany()
      .then((results) => results.map((r) => r.department)),
    jobRepository
      .createQueryBuilder('job')
      .select('DISTINCT job.location', 'location')
      .where('job.location IS NOT NULL')
      .getRawMany()
      .then((results) => results.map((r) => r.location)),
    companyService.findAllIndustries().then((results) => results.filter((i) => i)),
    companyService.findAllSizes().then((results) => results.filter((s) => s)),
  ]);

  // Calculate contextual insights if preferences are available
  let contextualInsights: any = {};
  if (jobSeekerPreferences && jobs.length > 0) {
    const jobsWithPreferences = jobs.filter(job => (job as any).preferenceMatch);
    const recommendedJobs = jobsWithPreferences.filter(job => (job as any).preferenceMatch?.isRecommended);
    const avgMatchScore = jobsWithPreferences.length > 0 
      ? Math.round(jobsWithPreferences.reduce((sum, job) => sum + ((job as any).preferenceMatch?.score || 0), 0) / jobsWithPreferences.length)
      : 0;

    contextualInsights = {
      hasPreferences: true,
      totalMatchingJobs: jobsWithPreferences.length,
      recommendedJobs: recommendedJobs.length,
      averageMatchScore: avgMatchScore,
      recommendations: {
        message: recommendedJobs.length > 0 
          ? `We found ${recommendedJobs.length} job${recommendedJobs.length > 1 ? 's' : ''} that closely match your preferences!`
          : avgMatchScore > 0 
            ? `Jobs shown have an average ${avgMatchScore}% match with your preferences. Consider updating your preferences for better matches.`
            : 'Complete your job preferences to see personalized job recommendations.',
        shouldUpdatePreferences: avgMatchScore < 50 && jobsWithPreferences.length > 0
      }
    };
  }

  // Return processed data
  return {
    data: jobs,
    metadata: {
      total: totalCount,
      filtered: jobs.length,
      preferences: {
        jobTypes,
        departments,
        locations,
        industries,
        companySizes,
      },
      contextual: contextualInsights,
    },
    pagination: {
      currentPage: page,
      totalPages: Math.ceil(totalCount / limit),
      totalItems: totalCount,
      itemsPerPage: limit,
    },
  };
}

export async function generateWithToneHelper(
  jobRepository: Repository<Job>,
  openai: OpenAI,
  jobId: string,
  tone: string,
): Promise<string> {
  // Find the job
  const job = await jobRepository.findOne({
    where: { id: jobId },
  });

  if (!job) {
    throw new NotFoundException(`Job with ID ${jobId} not found`);
  }

  // Define tone descriptions
  const toneDescriptions: Record<string, string> = {
    professional:
      'Write in a formal, business-appropriate tone that maintains professionalism while being clear and straightforward.',
    friendly:
      'Write in a warm, approachable tone that feels personal and welcoming while maintaining professionalism.',
    casual:
      "Write in a relaxed, conversational tone that's informal but still respectful and appropriate for a job description.",
    enthusiastic:
      'Write in an energetic, passionate tone that conveys excitement about the role and company.',
    formal:
      'Write in a highly structured, traditional business tone with sophisticated language and formal constructions.',
    conversational:
      "Write as if you're having a one-on-one conversation with the candidate, using natural language and a friendly approach.",
    like5yearold:
      'Explain the job in extremely simple terms that would be understandable to someone with no industry knowledge, while maintaining professionalism.',
    genz: 'Write using contemporary casual language that would appeal to Generation Z, including modern phrases and a more relaxed structure, while still being professional.',
  };

  const toneDescription = toneDescriptions[tone] || toneDescriptions.professional;

  // Create prompt for OpenAI
  const prompt = `
    Rewrite the following job description in a ${tone} tone. ${toneDescription}

    Use proper formatting with paragraphs, bullet points where appropriate, and clear section headings.
    Make sure to preserve all the important details from the original job description.

    Job Title: ${job.jobType}
    Company: ${job.companyName}
    Experience Level: ${job.experienceLevel}
    Department: ${job.department}

    Company Description:
    ${job.companyDescription || 'N/A'}

    Current Job Description:
    ${job.finalDraft || 'N/A'}

    Skills Required:
    ${job.skills ? job.skills.join(', ') : 'N/A'}

    Requirements:
    ${job.requirements ? job.requirements.join(', ') : 'N/A'}

    Job Responsibilities:
    ${job.jobResponsibilities ? job.jobResponsibilities.join(', ') : 'N/A'}

    Benefits:
    ${job.benefits ? job.benefits.join(', ') : 'N/A'}

    Salary Range:
    ${job.salaryRange || 'N/A'} ${job.currency || ''} ${job.paymentPeriod || ''}
  `;

  try {
    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content:
            'You are an expert job description writer who can adapt to different tones while maintaining professionalism and accuracy.',
        },
        { role: 'user', content: prompt },
      ],
      temperature: 0.7,
      max_tokens: 2000,
    });

    const generatedJD = completion.choices[0]?.message?.content || '';

    // Update the job with the generated JD and tone
    job.generatedJD = generatedJD;
    job.generatedJDTone = tone;
    await jobRepository.save(job);

    return generatedJD;
  } catch (error) {
    console.error('Error generating job description:', error);
    throw new Error('Failed to generate job description');
  }
}

export async function getMatchRanksHelper(
  jobRepository: Repository<Job>,
  jobId: string,
  options: PaginationOptions,
): Promise<MatchRankResponse> {
  const job = await jobRepository.findOne({
    where: { id: jobId },
    relations: ['candidates', 'candidates.evaluations', 'candidates.videoResponses'],
  });

  if (!job) {
    throw new NotFoundException(`Job with ID ${jobId} not found`);
  }

  if (!job.candidates || job.candidates.length === 0) {
    return {
      topTier: { candidates: [], total: 0 },
      secondTier: { candidates: [], total: 0 },
      others: { candidates: [], total: 0 },
      pagination: {
        currentPage: options.page,
        totalPages: 0,
        totalItems: 0,
        itemsPerPage: options.limit,
      },
    };
  }

  // Process the job to use job-specific evaluations
  const processedJob = JobService.processJobCandidatesStatic(job);

  // After processing, candidates should still exist, but let's ensure it for type safety
  if (!processedJob.candidates || processedJob.candidates.length === 0) {
    throw new Error(`Unexpected error: Candidates missing after processing for job ID ${jobId}`);
  }

  const { page, limit } = options;
  const skip = (page - 1) * limit;

  // Sort candidates by match score (highest first)
  const sortedCandidates = [...processedJob.candidates].sort(
    (a, b) =>
      Math.round((b.evaluation?.matchScore ?? 0) * 100) -
      Math.round((a.evaluation?.matchScore ?? 0) * 100),
  );

  // Group candidates by tier
  const groupedCandidates = sortedCandidates.reduce(
    (acc, candidate) => {
      const matchScore = Math.round((candidate.evaluation?.matchScore ?? 0) * 100);
      // Calculate tier thresholds using utility function
      const { topThreshold, secondThreshold } = normalizeJobThresholds(processedJob);

      // Check if candidate is shortlisted first
      if (candidate.status === 'SHORTLISTED') {
        acc.shortlisted.push(candidate);
      } else if (candidate.evaluation && matchScore >= topThreshold) {
        acc.topTier.push(candidate);
      } else if (candidate.evaluation && matchScore >= secondThreshold) {
        acc.secondTier.push(candidate);
      } else if (candidate.evaluation && matchScore > 0) {
        acc.others.push(candidate);
      } else {
        // Candidates with no evaluation or matchScore of 0
        acc.unranked.push(candidate);
      }
      return acc;
    },
    {
      topTier: [] as Candidate[],
      secondTier: [] as Candidate[],
      others: [] as Candidate[],
      unranked: [] as Candidate[],
      shortlisted: [] as Candidate[],
    },
  );

  // Helper function to paginate a group of candidates and add computed fields
  function paginateGroup(candidates: Candidate[]) {
    const paginatedCandidates = candidates.slice(skip, skip + limit).map((candidate) => {
      // Create a proper Candidate instance to preserve methods
      const candidateInstance = Object.create(Object.getPrototypeOf(candidate));
      Object.assign(candidateInstance, candidate);

      // Add computed field for video intro email status
      candidateInstance.videoIntroEmailSent =
        (candidate as any).getVideoIntroEmailSent?.(jobId) || false;

      return candidateInstance;
    });

    return {
      candidates: paginatedCandidates,
      total: candidates.length,
    };
  }

  // Calculate total candidates
  const totalCandidates = processedJob.candidates.length;

  // Calculate evaluated candidates
  const evaluatedCandidates = processedJob.candidates.filter((c) => c.evaluation);

  const matchScores = evaluatedCandidates.map((c) => {
    const rawScore = c.evaluation?.matchScore ?? 0;
    // If score is already in 0-100 range, use it directly, otherwise convert from decimal
    return rawScore > 1 ? rawScore : Math.round(rawScore * 100);
  });
  const averageMatchScore =
    matchScores.length > 0
      ? matchScores.reduce((acc, curr) => acc + curr, 0) / matchScores.length
      : 0;

  const completedVideoInterviews = processedJob.candidates.filter(
    (c) => c.hasCompletedVideoInterview,
  ).length;
  const contactedCandidates = processedJob.candidates.filter((c) => c.contacted).length;

  return {
    topTier: paginateGroup(groupedCandidates.topTier),
    secondTier: paginateGroup(groupedCandidates.secondTier),
    others: paginateGroup(groupedCandidates.others),
    unranked: paginateGroup(groupedCandidates.unranked),
    shortlisted: paginateGroup(groupedCandidates.shortlisted),
    pagination: {
      currentPage: page,
      totalPages: Math.ceil(totalCandidates / limit),
      totalItems: totalCandidates,
      itemsPerPage: limit,
    },
    stats: {
      totalCandidates,
      evaluatedCandidates: evaluatedCandidates.length,
      pendingEvaluation: totalCandidates - evaluatedCandidates.length,
      averageMatchScore: Math.round(averageMatchScore),
      matchingStats: {
        topTierCount: groupedCandidates.topTier.length,
        secondTierCount: groupedCandidates.secondTier.length,
        belowThresholdCount: groupedCandidates.others.length,
        topTierThreshold: Math.round((processedJob.topCandidateThreshold || 0.8) * 100),
        secondTierThreshold: Math.round((processedJob.secondTierCandidateThreshold || 0.6) * 100),
      },
      engagementStats: {
        contacted: contactedCandidates,
        notContacted: totalCandidates - contactedCandidates,
        completedVideoInterviews,
        videoInterviewCompletionRate:
          totalCandidates > 0 ? Math.round((completedVideoInterviews / totalCandidates) * 100) : 0,
      },
    },
  };
}

export async function getMatchRankedJobsHelper(
  jobRepository: Repository<Job>,
  userId: string,
  options: { page: number; limit: number },
) {
  const { page, limit } = options;
  const skip = (page - 1) * limit;

  const [jobs, total] = await jobRepository
    .createQueryBuilder('job')
    .leftJoinAndSelect('job.candidates', 'candidates')
    .leftJoinAndSelect('candidates.evaluations', 'candidateEvaluations')
    .where('job.clientId = :userId', { userId })
    .orderBy('job.updatedAt', 'DESC')
    .skip(skip)
    .take(limit)
    .getManyAndCount();

  const formattedJobs = jobs.map((job) => {
    // Process job candidates with job-specific evaluations
    const processedJob = JobService.processJobCandidatesStatic(job);

    return {
      id: processedJob.id,
      companyName: processedJob.companyName,
      jobType: processedJob.jobType,
      department: processedJob.department,
      candidates: processedJob.candidates || [],
      updatedAt: processedJob.updatedAt,
      metadata: {
        totalCandidates: processedJob.candidates?.length || 0,
        topTierCount:
          processedJob.candidates?.filter(
            (c) => Math.round((c.evaluation?.matchScore ?? 0) * 100) >= 70,
          ).length || 0,
        secondTierCount:
          processedJob.candidates?.filter((c) => {
            const score = Math.round((c.evaluation?.matchScore ?? 0) * 100);
            return score >= 50 && score < 70;
          }).length || 0,
      },
      status: calculateJobStatus(processedJob.candidates || []),
    };
  });

  return {
    data: formattedJobs,
    pagination: {
      currentPage: page,
      totalPages: Math.ceil(total / limit),
      totalItems: total,
      itemsPerPage: limit,
    },
  };
}
