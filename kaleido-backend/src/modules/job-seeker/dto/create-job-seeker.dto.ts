import { Transform, Type } from 'class-transformer';
import {
  Is<PERSON>rray,
  IsDate,
  IsEmail,
  IsEnum,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUrl,
  ValidateNested,
} from 'class-validator';

import { CandidateExperience, RemotePreference } from '@shared/types';

class EducationDto {
  @IsString()
  institution!: string;

  @IsString()
  degree!: string;

  @IsString()
  @IsOptional()
  @Transform(({ value }) => value || 'Not specified')
  field?: string;

  @IsDate()
  @IsOptional()
  @Type(() => Date)
  startDate?: Date | null;

  @IsDate()
  @IsOptional()
  @Type(() => Date)
  endDate?: Date | null;

  @IsString()
  @IsOptional()
  description?: string;
}

class CertificationDto {
  @IsString()
  name!: string;

  @IsString()
  issuer!: string;

  @IsDate()
  @IsOptional()
  @Type(() => Date)
  @Transform(({ value }) => value || new Date())
  issueDate?: Date;

  @IsDate()
  @IsOptional()
  @Type(() => Date)
  expiryDate?: Date;

  @IsString()
  @IsOptional()
  credentialId?: string;

  @IsString()
  @IsOptional()
  @Transform(({ value }) => {
    if (!value) return 'https://example.com';
    if (!value.startsWith('http')) return `https://${value}`;
    return value;
  })
  credentialUrl?: string;
}

class ProjectDto {
  @IsString()
  name!: string;

  @IsString()
  description!: string;

  @IsDate()
  @Type(() => Date)
  startDate!: Date;

  @IsDate()
  @IsOptional()
  @Type(() => Date)
  endDate?: Date;

  @IsUrl()
  @IsOptional()
  url?: string;

  @IsArray()
  technologies!: string[];
}

class DesiredSalaryDto {
  @IsNumber()
  min!: number;

  @IsNumber()
  max!: number;

  @IsString()
  @IsOptional()
  currency?: string;

  @IsString()
  @IsOptional()
  period?: string;
}

class PreferencesDto {
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  jobTypes?: string[];

  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  locations?: string[];

  @IsEnum(RemotePreference)
  @IsOptional()
  remotePreference?: RemotePreference;

  @IsObject()
  @IsOptional()
  // @ValidateNested()
  @Type(() => DesiredSalaryDto)
  desiredSalary?: DesiredSalaryDto;

  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  industries?: string[];
}

export class CreateJobSeekerDto {
  @IsString()
  clientId?: string;

  @IsString()
  @IsOptional()
  userId?: string;

  @IsString()
  firstName!: string;

  @IsString()
  lastName!: string;

  @IsEmail()
  email!: string;

  @IsString()
  @IsOptional()
  phone?: string;

  @IsString()
  @IsOptional()
  location?: string;

  @IsString()
  @IsOptional()
  summary?: string;

  @IsArray()
  @IsOptional()
  skills?: string[];

  @IsArray()
  @IsOptional()
  experience?: CandidateExperience[];

  @IsString()
  @IsOptional()
  resumeUrl?: string;

  @IsString()
  @IsOptional()
  linkedinUrl?: string;

  @IsString()
  @IsOptional()
  githubUrl?: string;

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => EducationDto)
  education?: EducationDto[];

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => CertificationDto)
  certifications?: CertificationDto[];

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ProjectDto)
  projects?: ProjectDto[];

  @IsArray()
  @IsOptional()
  languages?: string[];

  @IsArray()
  @IsOptional()
  myValues?: string[];

  // @IsUrl()
  @IsOptional()
  portfolioUrl?: string;

  @IsUrl()
  @IsOptional()
  videoIntroUrl?: string;

  @IsString()
  @IsOptional()
  myProfileImage?: string;

  @IsString()
  @IsOptional()
  resumeTemplate?: 'modern' | 'professional' | 'creative';

  @IsString()
  @IsOptional()
  resumeHtml?: string;

  @IsString()
  @IsOptional()
  resumeMobileTemplate?: 'modern' | 'professional' | 'creative';

  @IsString()
  @IsOptional()
  resumeMobileHtml?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => PreferencesDto)
  preferences?: PreferencesDto;

  @IsOptional()
  workAvailability?: any; // WorkAvailability type

  @IsString()
  @IsOptional()
  role?: string; // Using string type to avoid enum validation issues

  @IsOptional()
  isLinkedInUpdate?: boolean; // Flag to indicate this is a LinkedIn data update
}
